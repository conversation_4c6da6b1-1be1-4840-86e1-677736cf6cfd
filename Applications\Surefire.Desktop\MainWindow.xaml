﻿<Window x:Class="Surefire.Desktop.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Surefire.Desktop"
        xmlns:wv2="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
        mc:Ignorable="d"
        Title="Surefire Desktop"
        Height="720" Width="1280">
    <Grid>
        <Grid x:Name="LoadingOverlay" Background="White">
            <TextBlock Text="Loading..." FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="LightGray" />
        </Grid>
        <wv2:WebView2 x:Name="WebView" Source="http://localhost:5000" />
    </Grid>
</Window>