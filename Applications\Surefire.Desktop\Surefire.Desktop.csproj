﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<OutputPath>..\..\Build\Web</OutputPath>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
	</PropertyGroup>
	
	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net9.0-windows</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UseWPF>true</UseWPF>
		<ApplicationIcon>Resources\appicon.ico</ApplicationIcon>
	</PropertyGroup>
	
	<ItemGroup>
	  <Content Include="Resources\appicon.ico">
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	</ItemGroup>
	
	<ItemGroup>
	  <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.3065.39" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Properties\Settings.Designer.cs">
	    <DesignTimeSharedInput>True</DesignTimeSharedInput>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>Settings.settings</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <None Update="Properties\Settings.settings">
	    <Generator>SettingsSingleFileGenerator</Generator>
	    <LastGenOutput>Settings.Designer.cs</LastGenOutput>
	  </None>
	</ItemGroup>

</Project>
