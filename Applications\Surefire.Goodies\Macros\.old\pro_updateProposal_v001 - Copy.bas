﻿' ---------------------------------------
' - Update Proposal v0.1
' - by <PERSON> / flashvenom.com
' ---------------------------------------
'
' DESCRIPTION:
'   Updates fields and calculates values based on XML generated by an AMS with a Word Doc template... For use with building proposals.
'
' REFERNCES REQUIRED:
'   Microsoft XML, v6.0
'
'-----------------------------------------

Sub UpdateAllFields()
    Selection.WholeStory
    Selection.Fields.Update
    Selection.Fields.Update

    Dim variables As clsVariables
    Set variables = New clsVariables
    Dim doc As Document
    Documenter = Application.ActiveDocument

    myDoc = Application.ActiveDocument

    'Get the variables
    Dim rawXml As String
    Dim paragraphCount As Integer
    Dim xmlDoc As MSXML2.DOMDocument60

    paragraphCount = ActiveDocument.Range.Paragraphs.Count

    rawXml = ActiveDocument.Range.Paragraphs(paragraphCount - 1).Range.text
    Set xmlDoc = New MSXML2.DOMDocument60
    If xmlDoc.LoadXML(rawXml) Then
        pSubject = xmlDoc.SelectSingleNode("//SUBJECT").text
        pEmail = xmlDoc.SelectSingleNode("//EMAIL").text
        pCompany = xmlDoc.SelectSingleNode("//COMPANY").text
        pNotes = xmlDoc.SelectSingleNode("//NOTES").text
        pCreateLink = xmlDoc.SelectSingleNode("//CREATELINK").text
        pAmount = xmlDoc.SelectSingleNode("//AMOUNT").text
        
        Set objNode = xmlDoc.SelectSingleNode("//FULLAMOUNT")
        If Not objNode Is Nothing Then
            pFullAmount = objNode.text
        End If

    End If

    For Each doc In Application.Documents
        'Loop through all hyperlinks.
        If ActiveDocument.Name = doc.Name Then
            For i = 1 To doc.Hyperlinks.Count
                'Update old bookmarks to https
                If i = 1 Then
                    mySring = "https://epaypolicy.com/?payer=" + pCompany + "&emailAddress=" + pEmail + "&amount=" + Replace(Replace(pAmount, "$", ""), ",", "") + "&comments=" + Replace(pNotes, "#", "%23")
                    doc.Hyperlinks(i).Address = mySring
                End If
                If i = 2 Then
                    mySring = "https://epaypolicy.com/?payer=" + pCompany + "&emailAddress=" + pEmail + "&amount=" + Replace(Replace(pFullAmount, "$", ""), ",", "") + "&comments=" + Replace(pNotes, "#", "%23")
                    doc.Hyperlinks(i).Address = mySring
                End If
            Next
        End If
    Next
End Sub

