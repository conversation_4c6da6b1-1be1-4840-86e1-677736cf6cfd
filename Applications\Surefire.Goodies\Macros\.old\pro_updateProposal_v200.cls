﻿' ---------------------------------------
' - Update Proposal v1.34 (Classes File)
' - by <PERSON> / flashvenom.com
' ---------------------------------------
'
' DESCRIPTION:
'   Updates fields and calculates values based on XML generated by an AMS with a Word Doc template... For use with building proposals.
'
' REFERNCES REQUIRED:
'   Microsoft XML, v6.0
'
'-----------------------------------------

Private pSubject As String
Private pEmail As String
Private pCompany As String
Private pNotes As String
Private pAmount As String
Private pCreateLink As String
Private pFullAmount As String

Public Property Get Subject() As String
    Subject = pSubject
End Property

Public Property Get Email() As String
    Email = pEmail
End Property

Public Property Get Company() As String
    Company = pCompany
End Property

Public Property Get CreateLink() As String
    CreateLink = pCreateLink
End Property

Public Property Get Notes() As String
    Notes = pNotes
End Property

Public Property Get Amount() As String
    Amount = pAmount
End Property

Public Property Get AmountVal() As Single
    Temper = Replace(pAmount, "$", "")
    Temper = Replace(Temper, ",", "")
AmountVal = Val(Replace(Temper, "$", ""))
End Property

Public Sub ParseDocument(doc As Document)
    Dim rawXml As String
    Dim paragraphCount As Integer
    Dim xmlDoc As MSXML2.DOMDocument60

    paragraphCount = doc.Range.Paragraphs.Count
    testText = doc.Range.Paragraphs(1).Range.text
    rawXml = doc.Range.Paragraphs(paragraphCount - 1).Range.text
    Set xmlDoc = New MSXML2.DOMDocument60
    
    If xmlDoc.LoadXML(rawXml) Then
        pSubject = xmlDoc.SelectSingleNode("//SUBJECT").text
        pEmail = xmlDoc.SelectSingleNode("//EMAIL").text
        pCompany = xmlDoc.SelectSingleNode("//COMPANY").text
        pNotes = xmlDoc.SelectSingleNode("//NOTES").text
        pCreateLink = xmlDoc.SelectSingleNode("//CREATELINK").text
    End If

    If ActiveDocument.Bookmarks.Exists("txtBalance") = True Then
        pAmount = doc.FormFields("txtBalance").Result

        If ActiveDocument.Bookmarks.Exists("txtPaymentTotal") = True Then
            pFullAmount = doc.FormFields("txtPaymentTotal").Result
        End If
    Else
        pCreateLink = 0
    End If
End Sub

