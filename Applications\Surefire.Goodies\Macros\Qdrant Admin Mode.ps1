﻿<#
.SYNOPSIS
    VectorSearchCLI – A friendly CLI wrapper for your Surefire Embeddings API.

.DESCRIPTION
    • Runs fully interactive (menu-driven) **or** via command-line flags.
    • Calls /api/embeddings/search, /upsert, and a lightweight status check.
    • Pretty, colourised tables; raw-JSON option; local config stored in `$HOME`.
    • Works on Windows PowerShell 5.1+ and PowerShell 7 (Core).

.EXAMPLES
    # Launch TUI-like interactive mode:
    PS> .\VectorSearchCLI.ps1

    # One-off search via a single command line:
    PS> .\VectorSearchCLI.ps1 search -Query "work comp" -TopK 10 -Entity clients

    # Trigger an upsert job:
    PS> .\VectorSearchCLI.ps1 upsert

    # Update stored API URL / defaults:
    PS> .\VectorSearchCLI.ps1 config
#>
param(
    [Parameter(Position=0)]
    [ValidateSet('search','upsert','status','config','interactive')]
    [string]$Command = 'interactive',

    # Search‑specific args
    [string]$Query,
    [int]$TopK = 5,
    [ValidateSet('all','clients','policytypes')]
    [string]$Entity = 'all',

    # If -Raw is set, dump API JSON untouched
    [switch]$Raw
)

# ─────────────────────────────────────────────────────────────────────────────
#  CONFIGURATION  (stored at ~/.vectorsearchcli.json)
# ─────────────────────────────────────────────────────────────────────────────
$ConfigPath = Join-Path $HOME '.vectorsearchcli.json'
if (-not (Test-Path $ConfigPath)) {
    @{ BaseUrl = 'https://localhost:7074'; DefaultTopK = 5 } |
        ConvertTo-Json -Depth 3 | Set-Content $ConfigPath
}
$Config = Get-Content $ConfigPath | ConvertFrom-Json

# ─────────────────────────────────────────────────────────────────────────────
function Write-Header {
    param([string]$Text)
    Write-Host "`n$Text" -ForegroundColor Cyan -BackgroundColor Black
    Write-Host ('=' * $Text.Length) -ForegroundColor DarkCyan
}

function Invoke-Api {
    param(
        [string]$Endpoint,
        [hashtable]$Body = @{}
    )
    try {
        $json = $Body | ConvertTo-Json -Depth 5
        $uri  = "$($Config.BaseUrl)/api/embeddings/$Endpoint"
        Invoke-WebRequest -Uri $uri `
                          -Method POST `
                          -Headers @{ 'Content-Type' = 'application/json' } `
                          -Body $json |
            Select-Object -ExpandProperty Content |
            ConvertFrom-Json
    }
    catch {
        Write-Error "API call failed: $($_.Exception.Message)"
    }
}

function Show-Results {
    param($Payload)
    if (-not $Payload) { return }
    if ($Raw) { $Payload | ConvertTo-Json -Depth 6; return }

    $rows = $Payload.results | ForEach-Object {
        [PSCustomObject]@{
            Id     = $_.id
            Entity = $_.entity
            Name   = $_.name
            Score  = [math]::Round($_.score,3)
        }
    }
    if ($rows.Count -eq 0) {
        Write-Host 'No results found.' -ForegroundColor Yellow; return
    }
    $rows | Format-Table -AutoSize
}

function Do-Search {
    param([string]$Query,[int]$TopK,[string]$Entity)
    if (-not $Query) { $Query = Read-Host 'Enter search term' }
    $body = @{ query = $Query; topK = $TopK; entity = $Entity }
    $res = Invoke-Api -Endpoint 'search' -Body $body
    Show-Results $res
}

function Do-Upsert {
    Write-Header 'Trigger embedding upsert'
    Invoke-Api -Endpoint 'upsert' | Out-Null
    Write-Host 'Upsert triggered.' -ForegroundColor Green
}

function Do-Status {
    Write-Header 'API status check'
    Do-Search -Query 'ping' -TopK 1 -Entity 'all'
}

function Configure {
    Write-Header 'Configuration'
    $newUrl = Read-Host "Base API URL [$($Config.BaseUrl)]"
    if ($newUrl) { $Config.BaseUrl = $newUrl }

    $defTop = Read-Host "Default TopK [$($Config.DefaultTopK)]"
    if ($defTop) { $Config.DefaultTopK = [int]$defTop }

    $Config | ConvertTo-Json -Depth 3 | Set-Content $ConfigPath
    Write-Host 'Saved.' -ForegroundColor Green
}

function Interactive {
    Write-Header 'VectorSearch CLI'
    do {
        $choice = Read-Host '[S]earch  [U]psert  [C]onfig  [Q]uit'
        switch ($choice.ToUpper()) {
            'S' { Do-Search -TopK $Config.DefaultTopK -Entity 'all' }
            'U' { Do-Upsert }
            'C' { Configure }
            'Q' { return }
            default { Write-Host 'Unknown option.' -ForegroundColor Yellow }
        }
    } while ($true)
}

# ─────────────────────────────────────────────────────────────────────────────
#  MAIN ROUTER
# ─────────────────────────────────────────────────────────────────────────────
$Command = $Command.ToLower()
if ($Command -eq 'interactive') { Interactive; exit }

switch ($Command) {
    'search'  { Do-Search -Query $Query -TopK $TopK -Entity $Entity }
    'upsert'  { Do-Upsert }
    'status'  { Do-Status }
    'config'  { Configure }
    default   { Write-Host 'Unknown command. Use search|upsert|status|config or run without args for interactive mode.' -ForegroundColor Red }
}
