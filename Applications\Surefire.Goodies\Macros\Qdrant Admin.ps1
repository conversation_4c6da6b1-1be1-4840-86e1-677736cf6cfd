﻿# Test Vector Search for Policy Types:
Invoke-WebRequest -Uri "https://localhost:7074/api/embeddings/search" -Method POST -Headers @{ "Content-Type" = "application/json" } -Body '{"query": "work comp", "topK": 5}'

# Test Vector Search for Clients:
Invoke-WebRequest -Uri "https://localhost:7074/api/embeddings/search" -Method POST -Headers @{ "Content-Type" = "application/json" } -Body '{"query": "TWS", "topK": 5}'

# Test with More Variations:
Invoke-WebRequest -Uri "https://localhost:7074/api/embeddings/search" -Method POST -Headers @{ "Content-Type" = "application/json" } -Body '{"query": "workers compensation", "topK": 5}'
Invoke-WebRequest -Uri "https://localhost:7074/api/embeddings/search" -Method POST -Headers @{ "Content-Type" = "application/json" } -Body '{"query": "general liability", "topK": 5}'
Invoke-WebRequest -Uri "https://localhost:7074/api/embeddings/search" -Method POST -Headers @{ "Content-Type" = "application/json" } -Body '{"query": "commercial auto", "topK": 5}'

# To See Formatted Results:
Invoke-WebRequest -Uri "https://localhost:7074/api/embeddings/search" -Method POST -Headers @{ "Content-Type" = "application/json" } -Body '{"query": "work comp", "topK": 5}'

# Check System Status:
Invoke-WebRequest -Uri "https://localhost:7074/api/embeddings/search" -Method POST -Headers @{ "Content-Type" = "application/json" } -Body '{"query": "TWS", "topK": 5}'

# Trigger Embedding Upsert (if needed):
Invoke-WebRequest -Uri "https://localhost:7074/api/embeddings/upsert" -Method POST -Headers @{ "Content-Type" = "application/json" } -Body "{}"

# The search endpoint expects a JSON body with:
#     query:    The search term (string)
#     topK:     Number of results to return (optional, defaults to 5)
#
# The response will show:
#   - The original query
#   - Number of results found
#   - Array of results with:
#        id:        Vector ID
#        score:     Similarity score (0-1, higher is better)
#        entity:    Type of entity (Client, Product, etc.)
#        entityId:  Database ID
#        name:      Entity name

#This will help you verify that the vector search is working and finding the right matches for "work comp" → "Workers Compensation" and "TWS" → your actual client name.