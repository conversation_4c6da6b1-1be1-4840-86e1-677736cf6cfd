﻿' ---------------------------------------
' - Pro<PERSON>sler Updater v0.31
' - by <PERSON> / flashvenom.com
' ---------------------------------------
'
' DESCRIPTION:
'   Updates the form fields in a proposler template created with Surefire's Proposler system
'
' REFERNCES REQUIRED:
'   Microsoft XML, v6.0
'
'-----------------------------------------

Sub ProposalerUpdater3()
    On Error GoTo ErrorHandler

    Dim doc As Document
    Set doc = Application.ActiveDocument

    Dim purePremium As Double
    Dim brokerFee As Double
    Dim taxFeeItems As Double
    Dim totalFees As Double
    Dim minimumPercent As Double
    Dim minimumEarned As Double
    Dim totalDeposit As Double
    Dim totalPolicyCost As Double

    ' 1) Pure Premium from intPurePrem
    purePremium = GetCCValueByTag(doc, "intPurePrem")
    UpdateAllCCByTag doc, "calcTotalPure", purePremium
    UpdateAllCCByTag doc, "intPurePrem", purePremium

    ' 2) Broker fee from intRbf
    brokerFee = GetCCValueByTag(doc, "intRbf")

    ' 3) Sum all intFeeAmount items
    taxFeeItems = SumCCValuesByTag(doc, "intFeeAmount")

    ' 4) Total Fees = tax items + broker fee
    totalFees = taxFeeItems + brokerFee
    UpdateAllCCByTag doc, "calcTotalFees", totalFees

    ' 5) Minimum Earned %
    minimumPercent = GetCCValueByTag(doc, "varDisMinEarned")
    If minimumPercent = 0 Then minimumPercent = 25
    minimumEarned = purePremium * (minimumPercent / 100)
    UpdateAllCCByTag doc, "calcMinEarn", minimumEarned

    ' 6) Down payment = min earned + fees
    totalDeposit = minimumEarned + totalFees
    UpdateAllCCByTag doc, "calcDown", totalDeposit

    ' 7) Total policy cost = pure premium + fees
    totalPolicyCost = purePremium + totalFees
    UpdateAllCCByTag doc, "calcTotal", totalPolicyCost

    ' 8) Update Links
    Call UpdatePaymentLinks(doc, totalPolicyCost, totalDeposit, brokerFee)

    Exit Sub

ErrorHandler:
    MsgBox "Error " & Err.Number & ": " & Err.Description, vbCritical
End Sub

Function GetCCValueByTag(doc As Document, tag As String) As Double
    Dim cc As ContentControl
    For Each cc In doc.ContentControls
        If cc.tag = tag Then
            If IsNumeric(cc.Range.text) Then
                GetCCValueByTag = CDbl(cc.Range.text)
            Else
                GetCCValueByTag = 0
            End If
            Exit Function
        End If
    Next
    GetCCValueByTag = 0
End Function

Function SumCCValuesByTag(doc As Document, tag As String) As Double
    Dim total As Double : total = 0
    Dim cc As ContentControl
    For Each cc In doc.ContentControls
        If cc.tag = tag And IsNumeric(cc.Range.text) Then
            total = total + CDbl(cc.Range.text)
        End If
    Next
    SumCCValuesByTag = total
End Function

Sub UpdateAllCCByTag(doc As Document, tag As String, newValue As Double)
    Dim cc As ContentControl
    For Each cc In doc.ContentControls
        If cc.tag = tag Then
            ' unlock both control and contents
            cc.LockContentControl = False
            cc.LockContents = False

            ' write formatted number (2 decimals + commas)
            cc.Range.text = FormatNumber(newValue, 2, -1, 0, -1)

            ' re-lock if desired
            cc.LockContents = True
            cc.LockContentControl = True
        End If
    Next
End Sub

Sub UpdatePaymentLinks(ByRef doc As Document, ByVal totalPolicyCost As Double, ByVal totalDeposit As Double, ByVal brokerFee As Double)

    Dim hl As Hyperlink, url As String, baseUrl As String, qs As String
    Dim pairs() As String, kv() As String, i As Long
    Dim amountNew As String, commentsTxt As String
    Dim dict As Object :     Set dict = CreateObject("Scripting.Dictionary")

    For Each hl In doc.Hyperlinks
        url = hl.Address
        If InStr(1, url, "epaypolicy.com", vbTextCompare) = 0 Then GoTo NextHL

        '--- split into base + querystring -----------------------------------
        Dim pos As Long : pos = InStr(url, "?")
        If pos = 0 Then GoTo NextHL
        baseUrl = Left$(url, pos - 1)
        qs = Mid$(url, pos + 1)

        '--- load querystring into a dictionary ------------------------------
        dict.RemoveAll
        pairs = Split(qs, "&")
        For i = LBound(pairs) To UBound(pairs)
            kv = Split(pairs(i), "=")
            If UBound(kv) = 1 Then dict(LCase$(kv(0))) = kv(1)
        Next i

        If Not dict.Exists("comments") Or Not dict.Exists("amount") Then GoTo NextHL

        commentsTxt = Replace(LCase$(dict("comments")), "%20", " ")   ' crude decode

        Select Case True
            Case InStr(commentsTxt, "full premium") > 0
                amountNew = Format$(totalPolicyCost, "0.00")
            Case InStr(commentsTxt, "down payment") > 0
                amountNew = Format$(totalDeposit, "0.00")
            Case InStr(commentsTxt, "broker fee") > 0
                amountNew = Format$(brokerFee, "0.00")
            Case Else
                GoTo NextHL
        End Select

        dict("amount") = amountNew   ' **only thing that changes**

        '--- rebuild querystring exactly as we found it ----------------------
        qs = ""
        For Each k In dict.Keys
            qs = qs & k & "=" & dict(k) & "&"
        Next k
        qs = Left$(qs, Len(qs) - 1)   ' trim trailing &

        hl.Address = baseUrl & "?" & qs

NextHL:
    Next hl
End Sub
