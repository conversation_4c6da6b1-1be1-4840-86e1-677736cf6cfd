#r "nuget: DocumentFormat.OpenXml, 2.13.0"
#r "nuget: Newtonsoft.Json, 13.0.1"
#load "1-json-reader.csx"
#load "2-endorsement-extractor.csx" 
#load "3-json-processor.csx"
#load "4-docx-filler.csx"
#load "5-table-handler.csx"
#load "6-docx-saver.csx"
#load "DuplicationConfig.cs"

// docbuild2.csx - Main document building script
using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;

Console.WriteLine("Starting Proposler document build process...");
Console.WriteLine("--------------------------------------------");

// Define paths
string baseDir = Directory.GetCurrentDirectory();
string outputDir = Path.Combine(baseDir, "output");
string jsonPath = Path.Combine(outputDir, "temp.json");
string templatePath = Path.Combine(baseDir, "docbuild-template.docx");
string outputPath = Path.Combine(outputDir, "output.docx");

// Create output directory if it doesn't exist
if (!Directory.Exists(outputDir))
{
    Console.WriteLine($"Creating output directory: {outputDir}");
    Directory.CreateDirectory(outputDir);
}

// Check if template file exists
if (!File.Exists(templatePath))
{
    Console.WriteLine($"ERROR: Template file not found: {templatePath}");
    Console.WriteLine("Please ensure that 'docbuild-template.docx' exists in the current directory.");
    return;
}

try
{
    // Step 1: Read and parse the JSON data
    Console.WriteLine("\nStep 1: Reading JSON data...");
    
    // Check if JSON file exists
    if (!File.Exists(jsonPath))
    {
        Console.WriteLine($"WARNING: JSON file not found: {jsonPath}");
        Console.WriteLine("Creating a minimal JSON file for testing...");
        
        // Create a simple JSON object with test data
        var testData = new JObject
        {
            ["ClientName"] = "TEST CORPORATION, INC.",
            ["ClientAddress1"] = "123 MAIN STREET SUITE 100",
            ["ClientAddress2"] = "ANYTOWN, CA 90210",
            ["ClientAddress3"] = "ATTN: JOHN DOE",
            ["CarrierName"] = "Lloyd's of London",
            ["CarrierRating"] = "A",
            ["EffectiveDate"] = DateTime.Now.ToString("MM/dd/yyyy"),
            ["ExpirationDate"] = DateTime.Now.AddYears(1).ToString("MM/dd/yyyy"),
            ["QuoteValidUntil"] = DateTime.Now.AddMonths(1).ToString("MM/dd/yyyy"),
            ["RenewalOfPolicyNumber"] = "ASGH378373",
            ["Financials"] = new JObject
            {
                ["PurePremiumDollarAmount"] = "1000.00",
                ["MetroRetailBrokerFee"] = "400.00",
                ["MinimumEarnedPercentage"] = "25"
            }
        };
        
        // Write the test JSON to the file
        File.WriteAllText(jsonPath, testData.ToString(Formatting.Indented));
        Console.WriteLine("Created test JSON file with sample data.");
    }
    
    JObject jsonData = JsonReader.ReadJsonFile(jsonPath);
    
    // Step 2: Find and merge additional endorsements
    Console.WriteLine("\nStep 2: Finding and merging additional endorsements...");
    EndorsementExtractor.ExtractEndorsements(jsonData, outputDir);
    EndorsementExtractor.MergeAdditionalEndorsements(jsonData);
    
    // Step 3: Process and prepare the JSON data
    Console.WriteLine("\nStep 3: Processing JSON data...");
    var processedData = JsonProcessor.ProcessJsonData(jsonData);
    
    // Ensure varTotalPurePremium equals fieldPurePremiumDollarAmount
    if (processedData.FieldValues.TryGetValue("PurePremiumDollarAmount", out string purePremium) && !string.IsNullOrEmpty(purePremium))
    {
        // Remove any currency formatting for storage
        purePremium = purePremium.Replace("$", "").Replace(",", "").Trim();
        processedData.FieldValues["TotalPurePremium"] = purePremium;
        Console.WriteLine($"Set TotalPurePremium to equal PurePremiumDollarAmount: {purePremium}");
    }
    
    // Print out all processed fields for debugging
    Console.WriteLine("\nDEBUG: Processed fields:");
    foreach (var field in processedData.FieldValues.OrderBy(f => f.Key))
    {
        Console.WriteLine($"  {field.Key}: '{field.Value}'");
    }
    
    // Specifically check for address fields
    Console.WriteLine("\nDEBUG: Address fields:");
    Console.WriteLine($"  ClientName: '{processedData.FieldValues.GetValueOrDefault("ClientName", "<MISSING>")}'");
    Console.WriteLine($"  ClientAddress1: '{processedData.FieldValues.GetValueOrDefault("ClientAddress1", "<MISSING>")}'");
    Console.WriteLine($"  ClientAddress2: '{processedData.FieldValues.GetValueOrDefault("ClientAddress2", "<MISSING>")}'");
    Console.WriteLine($"  ClientAddress3: '{processedData.FieldValues.GetValueOrDefault("ClientAddress3", "<MISSING>")}'");
    
    // Step 4-6: Create and fill the document
    Console.WriteLine("\nStep 4-6: Creating document...");
    
    // Copy the template to the output file
    DocxProcessor.CreateDocument(templatePath, processedData, outputPath);
    
    // Perform final cleanup
    DocxSaver.PerformCleanup(outputPath);
    
    Console.WriteLine("\nDocument creation complete!");
    Console.WriteLine($"Output saved to: {outputPath}");
}
catch (Exception ex)
{
    Console.WriteLine($"\nERROR: {ex.Message}");
    Console.WriteLine(ex.StackTrace);
}

// Create a bridge between TableHandler and DocxProcessor
public static partial class TableHandler
{
    // Wrapper for DocxProcessor.SetContentControlText to be used from TableHandler
    public static void SetContentControlText(OpenXmlElement sdtElement, string text, bool isFieldPrefix = false, bool isMonetaryValue = false)
    {
        DocxProcessor.SetContentControlText(sdtElement, text, isFieldPrefix, isMonetaryValue);
    }
} 