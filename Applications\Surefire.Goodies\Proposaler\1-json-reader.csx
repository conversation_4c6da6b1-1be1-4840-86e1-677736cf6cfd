// 1-json-reader.csx - Reads and parses the JSON data file
using System;
using System.IO;
using Newtonsoft.Json.Linq;

public static class JsonReader
{
    /// <summary>
    /// Reads and parses a JSON file
    /// </summary>
    /// <param name="jsonPath">Path to the JSON file</param>
    /// <returns>Parsed JObject containing the JSON data</returns>
    public static JObject ReadJsonFile(string jsonPath)
    {
        try
        {
            Console.WriteLine($"Reading JSON data from {jsonPath}");
            if (!File.Exists(jsonPath))
            {
                throw new FileNotFoundException($"JSON file not found: {jsonPath}");
            }

            string jsonContent = File.ReadAllText(jsonPath);
            JObject jsonData = JObject.Parse(jsonContent);
            
            Console.WriteLine("JSON data successfully loaded and parsed");
            return jsonData;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error reading JSON file: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// Get a value from a JObject using a dot-notated path
    /// </summary>
    /// <param name="data">The JObject to search in</param>
    /// <param name="path">Dot-notated path to the value</param>
    /// <returns>The JToken at the specified path, or null if not found</returns>
    public static JToken GetJsonValue(JObject data, string path)
    {
        try
        {
            return data.SelectToken(path);
        }
        catch (Exception)
        {
            return null;
        }
    }
    
    /// <summary>
    /// Gets a JSON value as a string, safely handling null values
    /// </summary>
    /// <param name="data">The JObject to search in</param>
    /// <param name="path">Dot-notated path to the value</param>
    /// <returns>String representation of the value, or empty string if null</returns>
    public static string GetJsonValueAsString(JObject data, string path)
    {
        var token = GetJsonValue(data, path);
        return token?.ToString() ?? string.Empty;
    }
    
    /// <summary>
    /// Try multiple possible paths to get a JSON array
    /// </summary>
    /// <param name="data">The JObject to search in</param>
    /// <param name="primaryPath">Primary path to check</param>
    /// <param name="fallbackPath">Fallback path to check if primary fails</param>
    /// <returns>JArray if found, otherwise null</returns>
    public static JArray TryGetJsonArray(JObject data, string primaryPath, string fallbackPath)
    {
        Console.WriteLine($"Trying to get JSON array from primary path: {primaryPath} or fallback path: {fallbackPath}");
        
        var token = GetJsonValue(data, primaryPath);
        if (token != null && token.Type == JTokenType.Array)
        {
            Console.WriteLine($"Found array at primary path: {primaryPath}");
            return (JArray)token;
        }
        
        Console.WriteLine($"No array found at primary path, trying fallback: {fallbackPath}");
        token = GetJsonValue(data, fallbackPath);
        if (token != null && token.Type == JTokenType.Array)
        {
            Console.WriteLine($"Found array at fallback path: {fallbackPath}");
            return (JArray)token;
        }
        
        Console.WriteLine($"Could not find array at either path: {primaryPath} or {fallbackPath}");
        return null;
    }
} 