// 2-endorsement-extractor.csx - Extracts and merges endorsements
using System;
using System.IO;
using System.Linq;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;

public static class EndorsementExtractor
{
    /// <summary>
    /// Extract endorsements from Form Recognizer output
    /// </summary>
    /// <param name="jsonData">The JSON data to update with endorsements</param>
    /// <param name="outputDir">The output directory containing the Form Recognizer results</param>
    public static void ExtractEndorsements(JObject jsonData, string outputDir)
    {
        try
        {
            Console.WriteLine("Extracting additional endorsements from Form Recognizer output...");
            
            // Find the most recent JSON file in output directory (not temp.json)
            if (!Directory.Exists(outputDir))
            {
                Console.WriteLine("Output directory not found, skipping form recognizer extraction");
                return;
            }
            
            var jsonFiles = Directory.GetFiles(outputDir, "*.json")
                .Where(f => !Path.GetFileName(f).Equals("temp.json", StringComparison.OrdinalIgnoreCase))
                .OrderByDescending(f => new FileInfo(f).LastWriteTime)
                .ToList();
                
            if (jsonFiles.Count == 0)
            {
                Console.WriteLine("No form recognizer JSON files found in output directory");
                return;
            }
            
            string formRecognizerJson = jsonFiles.First();
            Console.WriteLine($"Using most recent Form Recognizer JSON: {formRecognizerJson}");
            
            // Parse the Form Recognizer JSON
            string jsonContent = File.ReadAllText(formRecognizerJson);
            JObject formData = JObject.Parse(jsonContent);
            
            // Create patterns to match endorsement form numbers
            // Pattern to catch form number formats like "MPIL 1083 04 15" or "IL 02 46 09 07"
            var formNumberPattern = new Regex(@"([A-Z]{2,6}\s?\d{2,4}\s?\d{2}\s?\d{2})|([A-Z]{2,6}[- ]?\d{4}[- ]?\d{2}[- ]?\d{2})");
            
            // Create a list to store found endorsements
            var foundEndorsements = new List<JObject>();
            
            // First, try to extract from tables
            var tables = formData["analyzeResult"]?["tables"];
            if (tables != null && tables.Type == JTokenType.Array)
            {
                Console.WriteLine($"Found {tables.Count()} tables in Form Recognizer output");
                
                foreach (var table in tables)
                {
                    string tableId = table["id"]?.ToString() ?? "unknown";
                    Console.WriteLine($"Processing table ID: {tableId}");
                    
                    // Table ID 4 typically contains endorsements
                    bool isEndorsementTable = (tableId == "4");
                    
                    var cells = table["cells"];
                    if (cells != null && cells.Type == JTokenType.Array)
                    {
                        // Group cells by row to analyze them together
                        var rowGroups = cells.GroupBy(cell => (int)cell["rowIndex"]);
                        
                        foreach (var rowGroup in rowGroups)
                        {
                            // Extract text from all cells in the row
                            var rowTexts = rowGroup.Select(cell => cell["content"]?.ToString() ?? "").ToList();
                            
                            // Try to find a form number in any cell
                            string formNumber = null;
                            string endorsementName = null;
                            
                            foreach (var text in rowTexts)
                            {
                                var match = formNumberPattern.Match(text);
                                if (match.Success)
                                {
                                    formNumber = match.Value.Replace(" ", ""); // Remove spaces for consistency
                                    Console.WriteLine($"Found form number: {formNumber}");
                                    break;
                                }
                            }
                            
                            // If we found a form number, look for an endorsement name in the same row
                            if (!string.IsNullOrEmpty(formNumber))
                            {
                                // The endorsement name is likely the longest text in the row that's not the form number
                                endorsementName = rowTexts
                                    .Where(t => !formNumberPattern.IsMatch(t) && t.Length > 3)
                                    .OrderByDescending(t => t.Length)
                                    .FirstOrDefault();
                                    
                                if (!string.IsNullOrEmpty(endorsementName))
                                {
                                    Console.WriteLine($"Found endorsement name: {endorsementName}");
                                    
                                    // Add this endorsement to our list
                                    foundEndorsements.Add(new JObject
                                    {
                                        ["EndorsementNumber"] = formNumber,
                                        ["EndorsementName"] = endorsementName
                                    });
                                }
                                else if (isEndorsementTable) 
                                {
                                    // For endorsement table with form number but no name, use a generic description
                                    string genericName = "Additional Endorsement";
                                    Console.WriteLine($"Using generic name '{genericName}' for form {formNumber} in endorsement table");
                                    
                                    foundEndorsements.Add(new JObject
                                    {
                                        ["EndorsementNumber"] = formNumber,
                                        ["EndorsementName"] = genericName
                                    });
                                }
                            }
                        }
                    }
                }
            }
            
            // If we didn't find any in tables, try looking through the entire document
            if (foundEndorsements.Count == 0)
            {
                Console.WriteLine("No endorsements found in tables, trying document-level search");
                
                // Get all text content from the document
                var documentContent = formData["analyzeResult"]?["content"]?.ToString() ?? "";
                
                // Find all form numbers in the document
                var formMatches = formNumberPattern.Matches(documentContent);
                Console.WriteLine($"Found {formMatches.Count} potential form numbers in document content");
                
                foreach (Match match in formMatches)
                {
                    string formNumber = match.Value.Replace(" ", ""); // Remove spaces
                    Console.WriteLine($"Found form number in document: {formNumber}");
                    
                    // Look for a potential endorsement name before or after the form number
                    int matchIndex = match.Index;
                    int beforeStart = Math.Max(0, matchIndex - 150);
                    int afterEnd = Math.Min(documentContent.Length, matchIndex + match.Length + 150);
                    
                    string before = documentContent.Substring(beforeStart, matchIndex - beforeStart);
                    string after = documentContent.Substring(matchIndex + match.Length, afterEnd - (matchIndex + match.Length));
                    
                    // Look for line breaks or multiple spaces as delimiters
                    string[] beforeLines = before.Split(new string[] { "\r", "\n", "  " }, StringSplitOptions.RemoveEmptyEntries);
                    string[] afterLines = after.Split(new string[] { "\r", "\n", "  " }, StringSplitOptions.RemoveEmptyEntries);
                    
                    string endorsementName = null;
                    
                    // Check the line immediately before or after
                    if (beforeLines.Length > 0)
                    {
                        endorsementName = beforeLines.Last().Trim();
                    }
                    
                    if (string.IsNullOrWhiteSpace(endorsementName) && afterLines.Length > 0)
                    {
                        endorsementName = afterLines.First().Trim();
                    }
                    
                    // Check for reasonably looking endorsement names
                    if (!string.IsNullOrWhiteSpace(endorsementName) && endorsementName.Length > 5)
                    {
                        // Filter out some common false positives
                        if (!endorsementName.Contains("Policy Number") && 
                            !endorsementName.Contains("Form Number") && 
                            !endorsementName.StartsWith("CG") &&
                            !endorsementName.StartsWith("IL") &&
                            !formNumberPattern.IsMatch(endorsementName))
                        {
                            foundEndorsements.Add(new JObject
                            {
                                ["EndorsementNumber"] = formNumber,
                                ["EndorsementName"] = endorsementName
                            });
                        }
                        else
                        {
                            // Use a generic name for false positives
                            foundEndorsements.Add(new JObject
                            {
                                ["EndorsementNumber"] = formNumber,
                                ["EndorsementName"] = "Additional Endorsement"
                            });
                        }
                    }
                    else
                    {
                        // Use a generic name if we couldn't find a valid name
                        foundEndorsements.Add(new JObject
                        {
                            ["EndorsementNumber"] = formNumber,
                            ["EndorsementName"] = "Additional Endorsement"
                        });
                    }
                }
            }
            
            Console.WriteLine($"Found {foundEndorsements.Count} potential endorsements in Form Recognizer output");
            
            if (foundEndorsements.Count == 0)
            {
                Console.WriteLine("No endorsements found, skipping update");
                return;
            }
            
            // Add the found endorsements to the data
            AddEndorsementsToData(jsonData, foundEndorsements);
            
            // Update the temp.json file with the modified data
            string tempJsonPath = Path.Combine(outputDir, "temp.json");
            File.WriteAllText(tempJsonPath, jsonData.ToString(Newtonsoft.Json.Formatting.Indented));
            Console.WriteLine($"Updated temp.json with new endorsements");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error extracting endorsements from Form Recognizer: {ex.Message}");
        }
    }
    
    /// <summary>
    /// Merges additional endorsements from forms.json into the main JSON data
    /// </summary>
    /// <param name="mainData">The JSON data to update with endorsements</param>
    public static void MergeAdditionalEndorsements(JObject mainData)
    {
        try
        {
            string formsJsonPath = Path.Combine(Directory.GetCurrentDirectory(), "forms.json");
            if (!File.Exists(formsJsonPath))
            {
                Console.WriteLine("forms.json not found, skipping additional endorsements");
                return;
            }

            string formsJsonContent = File.ReadAllText(formsJsonPath);
            var formsData = JObject.Parse(formsJsonContent);

            // Get the additional endorsements array
            var additionalEndorsements = formsData["AdditionalEndorsements"];
            if (additionalEndorsements == null || !additionalEndorsements.HasValues)
            {
                Console.WriteLine("No additional endorsements found in forms.json");
                return;
            }
            
            // Create a list to store the endorsements
            var endorsementsList = new List<JObject>();
            foreach (var endorsement in additionalEndorsements)
            {
                string endorsementNumber = endorsement["EndorsementNumber"]?.ToString();
                string endorsementName = endorsement["EndorsementName"]?.ToString() ?? "Additional Endorsement";
                
                if (!string.IsNullOrEmpty(endorsementNumber))
                {
                    endorsementsList.Add(new JObject
                    {
                        ["EndorsementNumber"] = endorsementNumber,
                        ["EndorsementName"] = endorsementName
                    });
                    
                    Console.WriteLine($"Adding endorsement from forms.json: {endorsementNumber} - {endorsementName}");
                }
            }
            
            // Add the endorsements to the data
            AddEndorsementsToData(mainData, endorsementsList);
            
            Console.WriteLine($"Successfully merged endorsements from forms.json");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error merging additional endorsements: {ex.Message}");
        }
    }
    
    /// <summary>
    /// Adds endorsements to the JSON data
    /// </summary>
    /// <param name="jsonData">The JSON data to update</param>
    /// <param name="endorsements">List of endorsements to add</param>
    private static void AddEndorsementsToData(JObject jsonData, List<JObject> endorsements)
    {
        if (endorsements.Count == 0)
            return;
            
        // Get or create the endorsements section in the main data
        JObject insurancePolicy = jsonData["InsurancePolicy"] as JObject ?? jsonData;
        JArray endorsementsArray;
        
        // Check if we have a nested structure
        bool hasNestedStructure = insurancePolicy["Endorsements"]?["Endorsement"] != null;
        
        if (hasNestedStructure)
        {
            var endorsementsSection = insurancePolicy["Endorsements"] as JObject ?? new JObject();
            endorsementsArray = endorsementsSection["Endorsement"] as JArray ?? new JArray();
            
            // Create a HashSet to track existing endorsement numbers
            var existingEndorsementNumbers = new HashSet<string>();
            foreach (var endorsement in endorsementsArray)
            {
                string endorsementNumber = endorsement["EndorsementNumber"]?.ToString();
                if (!string.IsNullOrEmpty(endorsementNumber))
                {
                    existingEndorsementNumbers.Add(endorsementNumber);
                }
            }
            
            // Add new endorsements that don't already exist
            int addedCount = 0;
            foreach (var endorsement in endorsements)
            {
                string endorsementNumber = endorsement["EndorsementNumber"]?.ToString();
                if (!string.IsNullOrEmpty(endorsementNumber) && !existingEndorsementNumbers.Contains(endorsementNumber))
                {
                    endorsementsArray.Add(endorsement);
                    existingEndorsementNumbers.Add(endorsementNumber);
                    addedCount++;
                    Console.WriteLine($"Added new endorsement: {endorsementNumber} - {endorsement["EndorsementName"]}");
                }
            }
            
            // Update the endorsements in the main data
            endorsementsSection["Endorsement"] = endorsementsArray;
            insurancePolicy["Endorsements"] = endorsementsSection;
            
            Console.WriteLine($"Added {addedCount} new endorsements to nested structure");
        }
        else
        {
            // Direct array structure
            endorsementsArray = insurancePolicy["Endorsements"] as JArray ?? new JArray();
            
            // Create a HashSet to track existing endorsement numbers
            var existingEndorsementNumbers = new HashSet<string>();
            foreach (var endorsement in endorsementsArray)
            {
                string endorsementNumber = endorsement["EndorsementNumber"]?.ToString();
                if (!string.IsNullOrEmpty(endorsementNumber))
                {
                    existingEndorsementNumbers.Add(endorsementNumber);
                }
            }
            
            // Add new endorsements that don't already exist
            int addedCount = 0;
            foreach (var endorsement in endorsements)
            {
                string endorsementNumber = endorsement["EndorsementNumber"]?.ToString();
                if (!string.IsNullOrEmpty(endorsementNumber) && !existingEndorsementNumbers.Contains(endorsementNumber))
                {
                    endorsementsArray.Add(endorsement);
                    existingEndorsementNumbers.Add(endorsementNumber);
                    addedCount++;
                    Console.WriteLine($"Added new endorsement: {endorsementNumber} - {endorsement["EndorsementName"]}");
                }
            }
            
            // Update the endorsements in the main data
            insurancePolicy["Endorsements"] = endorsementsArray;
            
            Console.WriteLine($"Added {addedCount} new endorsements to direct array structure");
        }
        
        // Update the main data if we were working with InsurancePolicy
        if (jsonData != insurancePolicy)
        {
            jsonData["InsurancePolicy"] = insurancePolicy;
        }
    }
} 