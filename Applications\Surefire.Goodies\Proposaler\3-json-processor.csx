// 3-json-processor.csx - Processes and cleans JSON data
using System;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;

public class ProcessedData
{
    public JObject JsonData { get; set; }
    public Dictionary<string, string> FieldValues { get; set; }
    public decimal PurePremium { get; set; }
    public decimal BrokerFee { get; set; }
    public decimal TotalTaxesAndFees { get; set; }
    public decimal TotalPolicyCost { get; set; }
    public decimal MinimumEarnedPercentage { get; set; }
    public decimal MinimumEarned { get; set; }
    public decimal TotalDeposit { get; set; }
}

public static class JsonProcessor
{
    /// <summary>
    /// Processes JSON data to prepare it for document creation
    /// </summary>
    /// <param name="jsonData">The JSON data to process</param>
    /// <returns>Processed data with calculated fields</returns>
    public static ProcessedData ProcessJsonData(JObject jsonData)
    {
        Console.WriteLine("Processing JSON data...");
        
        // Create a dictionary to hold all field values
        var fieldValues = new Dictionary<string, string>();
        
        // Use "InsurancePolicy" if available, otherwise use the root
        JObject data = jsonData["InsurancePolicy"] as JObject ?? jsonData;
        
        // Extract important financial values
        decimal purePremium = GetPurePremium(data);
        decimal brokerFee = GetBrokerFee(data);
        decimal taxesAndFees = GetTaxesAndFees(data);
        decimal minimumEarnedPercentage = GetMinimumEarnedPercentage(data);
        
        // Calculate derived values
        taxesAndFees += brokerFee; // Total taxes and fees includes broker fee
        decimal totalPolicyCost = purePremium + taxesAndFees;
        decimal minimumEarned = purePremium * (minimumEarnedPercentage / 100.0M);
        decimal totalDeposit = minimumEarned + taxesAndFees;
        
        Console.WriteLine($"Financial calculations:");
        Console.WriteLine($" - Pure Premium: ${purePremium:F2}");
        Console.WriteLine($" - Broker Fee: ${brokerFee:F2}");
        Console.WriteLine($" - Taxes and Fees: ${taxesAndFees:F2}");
        Console.WriteLine($" - Total Policy Cost: ${totalPolicyCost:F2}");
        Console.WriteLine($" - Minimum Earned Percentage: {minimumEarnedPercentage}%");
        Console.WriteLine($" - Minimum Earned: ${minimumEarned:F2}");
        Console.WriteLine($" - Total Deposit: ${totalDeposit:F2}");
        
        // Store calculated values in the field dictionary
        fieldValues["PurePremiumDollarAmount"] = FormatNumber(purePremium);
        fieldValues["MetroRetailBrokerFee"] = FormatNumber(brokerFee);
        fieldValues["TotalTaxesAndFeesDollarAmount"] = FormatNumber(taxesAndFees);
        fieldValues["TotalPolicyCost"] = FormatNumber(totalPolicyCost);
        fieldValues["MinimumEarnedPercentage"] = minimumEarnedPercentage.ToString("F0");
        fieldValues["MinimumEarnedDollarAmount"] = FormatNumber(minimumEarned);
        fieldValues["TotalDepositDollarAmount"] = FormatNumber(totalDeposit);
        
        // Process all fields from the JSON data
        ProcessFields(data, fieldValues);
        
        // Format client names and addresses with proper capitalization
        FormatClientInformation(fieldValues);
        
        // Format dates to remove leading zeros
        FormatDates(fieldValues);
        
        // Return the processed data
        return new ProcessedData
        {
            JsonData = jsonData,
            FieldValues = fieldValues,
            PurePremium = purePremium,
            BrokerFee = brokerFee,
            TotalTaxesAndFees = taxesAndFees,
            TotalPolicyCost = totalPolicyCost,
            MinimumEarnedPercentage = minimumEarnedPercentage,
            MinimumEarned = minimumEarned,
            TotalDeposit = totalDeposit
        };
    }
    
    /// <summary>
    /// Gets the pure premium amount from the JSON data
    /// </summary>
    private static decimal GetPurePremium(JObject data)
    {
        // Try to get the value from the JSON data
        var purePremiumToken = JsonReader.GetJsonValue(data, "Financials.PurePremiumDollarAmount");
        if (purePremiumToken != null)
        {
            string purePremiumStr = purePremiumToken.ToString().TrimStart('$');
            if (decimal.TryParse(purePremiumStr, out decimal amount))
            {
                Console.WriteLine($"Found pure premium in JSON: ${amount:F2}");
                return amount;
            }
        }
        
        Console.WriteLine("No pure premium found in JSON, defaulting to 0");
        return 0;
    }
    
    /// <summary>
    /// Gets the broker fee from the JSON data
    /// </summary>
    private static decimal GetBrokerFee(JObject data)
    {
        // Try to get the value from the JSON data
        var brokerFeeToken = JsonReader.GetJsonValue(data, "Financials.MetroRetailBrokerFee");
        if (brokerFeeToken != null)
        {
            string brokerFeeStr = brokerFeeToken.ToString().TrimStart('$');
            if (decimal.TryParse(brokerFeeStr, out decimal amount))
            {
                Console.WriteLine($"Found broker fee in JSON: ${amount:F2}");
                return amount;
            }
        }
        
        // Default broker fee
        Console.WriteLine("No broker fee found in JSON, using default: $400.00");
        return 400.00M;
    }
    
    /// <summary>
    /// Gets the minimum earned percentage from the JSON data
    /// </summary>
    private static decimal GetMinimumEarnedPercentage(JObject data)
    {
        // Try to get the value from the JSON data
        var percentageToken = JsonReader.GetJsonValue(data, "Financials.MinimumEarnedPercentage");
        if (percentageToken != null)
        {
            string percentageStr = percentageToken.ToString().TrimEnd('%');
            if (decimal.TryParse(percentageStr, out decimal percentage))
            {
                Console.WriteLine($"Found minimum earned percentage in JSON: {percentage}%");
                return percentage;
            }
        }
        
        // Default minimum earned percentage
        Console.WriteLine("No minimum earned percentage found in JSON, using default: 25%");
        return 25.00M;
    }
    
    /// <summary>
    /// Gets the taxes and fees from the JSON data
    /// </summary>
    private static decimal GetTaxesAndFees(JObject data)
    {
        decimal totalTaxesAndFees = 0;
        
        // Try to get the tax and fee items
        var taxFeeItems = JsonReader.TryGetJsonArray(data, "Financials.TaxFeeItems", "Financials.TaxFeeItems.TaxFeeItem");
        if (taxFeeItems != null)
        {
            foreach (var item in taxFeeItems)
            {
                string amountStr = "";
                
                // Check for TaxFeeItemDollarAmount or Amount
                if (item["TaxFeeItemDollarAmount"] != null)
                {
                    amountStr = item["TaxFeeItemDollarAmount"].ToString();
                }
                else if (item["Amount"] != null)
                {
                    amountStr = item["Amount"].ToString();
                }
                
                // Remove dollar sign if present
                amountStr = amountStr.TrimStart('$');
                
                if (decimal.TryParse(amountStr, out decimal amount))
                {
                    totalTaxesAndFees += amount;
                    Console.WriteLine($"Added tax/fee: ${amount:F2}");
                }
            }
        }
        
        Console.WriteLine($"Total taxes and fees (excluding broker fee): ${totalTaxesAndFees:F2}");
        return totalTaxesAndFees;
    }
    
    /// <summary>
    /// Processes all fields from the JSON data
    /// </summary>
    private static void ProcessFields(JObject data, Dictionary<string, string> fieldValues)
    {
        Console.WriteLine("Processing standard fields...");
        
        // Process common fields (not calculated above)
        ProcessField(data, fieldValues, "ClientName");
        ProcessField(data, fieldValues, "ClientAddressLine1");
        ProcessField(data, fieldValues, "ClientAddressLine2");
        ProcessField(data, fieldValues, "ClientAddressLine3");
        ProcessField(data, fieldValues, "ContactFirstName");
        ProcessField(data, fieldValues, "ContactLastName");
        ProcessField(data, fieldValues, "PrimaryCoverage");
        ProcessField(data, fieldValues, "EffectiveDate");
        ProcessField(data, fieldValues, "ExpirationDate");
        ProcessField(data, fieldValues, "CoverageRetroActiveDate");
        
        // Add missing carrier information
        ProcessField(data, fieldValues, "CarrierName");
        ProcessField(data, fieldValues, "CarrierRating");
        ProcessField(data, fieldValues, "QuoteValidUntil");
        ProcessField(data, fieldValues, "RenewalOfPolicyNumber");
        
        // Add today's date
        fieldValues["TodaysDate"] = DateTime.Now.ToString("M/d/yyyy");
        Console.WriteLine($"Added today's date: {fieldValues["TodaysDate"]}");
        
        // Ensure client address is properly processed
        // Try getting address from ClientAddress fields first, then try ClientDetails.ClientAddress
        if (!fieldValues.ContainsKey("ClientAddress1") || string.IsNullOrEmpty(fieldValues["ClientAddress1"]))
        {
            ProcessField(data, fieldValues, "ClientDetails.ClientAddress.ClientAddressLine1", "ClientAddress1");
            ProcessField(data, fieldValues, "ClientDetails.ClientAddress.ClientAddressLine2", "ClientAddress2");
            ProcessField(data, fieldValues, "ClientDetails.ClientAddress.ClientAddressLine3", "ClientAddress3");
        }
        
        // Special handling for address fields - try different naming conventions
        // Check if we have values and create copies with different key names
        // string[] addressPrefixes = { "Insured", "Named", "Client" };
        // string[] addressSuffixes = { "Address", "Addr", "" };

        // foreach (var prefix in addressPrefixes)
        // {
        //     foreach (var suffix in addressSuffixes)
        //     {
        //         // Create mappings between our standard fields and potential variations
        //         var fieldMappings = new Dictionary<string, string>
        //         {
        //             ["ClientName"] = $"{prefix}Name",
        //             ["ClientAddress1"] = $"{prefix}{suffix}1",
        //             ["ClientAddress2"] = $"{prefix}{suffix}2",
        //             ["ClientAddress3"] = $"{prefix}{suffix}3"
        //         };

        //         // Only copy if source exists and has value
        //         foreach (var mapping in fieldMappings)
        //         {
        //             string sourceKey = mapping.Key;
        //             string targetKey = mapping.Value;
                    
        //             if (fieldValues.TryGetValue(sourceKey, out string value) && !string.IsNullOrEmpty(value))
        //             {
        //                 // Don't overwrite existing values
        //                 if (!fieldValues.ContainsKey(targetKey) || string.IsNullOrEmpty(fieldValues[targetKey]))
        //                 {
        //                     fieldValues[targetKey] = value;
        //                     Console.WriteLine($"Copied {sourceKey}='{value}' to {targetKey}");
        //                 }
        //             }
        //         }
        //     }
        // }
        
        // Process nested fields
        if (data["Coverages"] != null)
        {
            if (data["Coverages"]["Coverage"] is JArray coverageArray && coverageArray.Count > 0)
            {
                // Nested structure
                var coverage = coverageArray[0];
                fieldValues["CoverageName"] = coverage["CoverageName"]?.ToString() ?? "";
                
                // Process limits
                if (coverage["Limits"]?["Limit"] is JArray limits)
                {
                    foreach (var limit in limits)
                    {
                        string name = limit["LimitName"]?.ToString() ?? "";
                        string amount = limit["LimitDollarAmount"]?.ToString() ?? "";
                        Console.WriteLine($"Found limit: {name} = {amount}");
                    }
                }
                
                // Process deductibles
                if (coverage["Deductibles"]?["Deductible"] is JArray deductibles)
                {
                    foreach (var deductible in deductibles)
                    {
                        string name = deductible["DeductibleName"]?.ToString() ?? "";
                        string amount = deductible["DeductibleDollarAmount"]?.ToString() ?? "";
                        Console.WriteLine($"Found deductible: {name} = {amount}");
                    }
                }
            }
            else if (data["Coverages"] is JArray coveragesArray && coveragesArray.Count > 0)
            {
                // Direct array structure
                var coverage = coveragesArray[0];
                fieldValues["CoverageName"] = coverage["CoverageName"]?.ToString() ?? "";
                
                // Process limits
                if (coverage["Limits"] is JArray limits)
                {
                    foreach (var limit in limits)
                    {
                        string name = limit["LimitName"]?.ToString() ?? "";
                        string amount = limit["LimitDollarAmount"]?.ToString() ?? "";
                        Console.WriteLine($"Found limit: {name} = {amount}");
                    }
                }
                
                // Process deductibles
                if (coverage["Deductibles"] is JArray deductibles)
                {
                    foreach (var deductible in deductibles)
                    {
                        string name = deductible["DeductibleName"]?.ToString() ?? "";
                        string amount = deductible["DeductibleDollarAmount"]?.ToString() ?? "";
                        Console.WriteLine($"Found deductible: {name} = {amount}");
                    }
                }
            }
        }
        
        // Handle ClientAddress3 - only use if it contains meaningful content
        if (fieldValues.TryGetValue("ClientAddress3", out string addr3))
        {
            if (!addr3.Contains("ATTN:") && !addr3.Contains("c/o") && !addr3.Contains("C/O"))
            {
                fieldValues["ClientAddress3"] = "";
                Console.WriteLine("Cleared ClientAddress3 as it doesn't contain ATTN: or c/o prefix");
            }
        }
        
        // Handle ClientAddress2 - ensure it contains city, state, and ZIP
        if (fieldValues.TryGetValue("ClientAddress2", out string addr2) && 
            fieldValues.TryGetValue("ClientAddress3", out string zipAddr))
        {
            // Extract ZIP code pattern (5 digits, optionally followed by dash and 4 more digits)
            var zipMatch = Regex.Match(zipAddr, @"\b\d{5}(-\d{4})?\b");
            if (zipMatch.Success)
            {
                string zip = zipMatch.Value;
                
                // If we have a ZIP code and it's not already in ClientAddress2, append it
                if (!string.IsNullOrEmpty(zip) && !addr2.Contains(zip))
                {
                    var stateMatch = Regex.Match(addr2, @"\b[A-Z]{2}\b$");
                    if (stateMatch.Success)
                    {
                        // Insert ZIP after state with a space
                        fieldValues["ClientAddress2"] = addr2 + " " + zip;
                        Console.WriteLine($"Appended ZIP code to ClientAddress2: {fieldValues["ClientAddress2"]}");
                    }
                    else
                    {
                        // Just append ZIP if no state found
                        fieldValues["ClientAddress2"] = addr2 + " " + zip;
                        Console.WriteLine($"Appended ZIP code to ClientAddress2 (no state found): {fieldValues["ClientAddress2"]}");
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// Process a single field from the JSON data
    /// </summary>
    private static void ProcessField(JObject data, Dictionary<string, string> fieldValues, string fieldName)
    {
        ProcessField(data, fieldValues, fieldName, fieldName);
    }
    
    /// <summary>
    /// Process a single field from the JSON data with a custom target field name
    /// </summary>
    /// <param name="data">The JSON data</param>
    /// <param name="fieldValues">Dictionary to store field values</param>
    /// <param name="sourcePath">Source path in the JSON</param>
    /// <param name="targetFieldName">Target field name in the dictionary</param>
    private static void ProcessField(JObject data, Dictionary<string, string> fieldValues, string sourcePath, string targetFieldName)
    {
        //Console.WriteLine($"------------Processing targetFieldName={targetFieldName}");

        // Try to get the value from the direct path
        var token = JsonReader.GetJsonValue(data, sourcePath);
        if (token != null)
        {
            fieldValues[targetFieldName] = token.ToString();
            //Console.WriteLine($"Found {targetFieldName} directly at path: {sourcePath}");
            return;
        }
        
        // Only try common paths if the source path doesn't contain dots (indicating it's a simple field name)
        if (!sourcePath.Contains("."))
        {
            // Try common paths
            var possiblePaths = new List<string>
            {
                $"ClientDetails.ClientAddress.{sourcePath}",
                $"ClientDetails.{sourcePath}",
                $"PolicyDetails.{sourcePath}",
                $"LegalNotices.{sourcePath}",
                $"Coverages.Coverage[0].{sourcePath}",
                $"RatingBasises.RatingBasis[0].{sourcePath}",
                $"Locations.Location[0].{sourcePath}",
                $"Financials.{sourcePath}"
            };
            
            foreach (var path in possiblePaths)
            {
                //Console.WriteLine($"========Trying {targetFieldName} at path={path}");
                token = JsonReader.GetJsonValue(data, path);
                if (token != null)
                {
                    fieldValues[targetFieldName] = token.ToString();
                    Console.WriteLine($"Found {targetFieldName} at path: {path}");
                    return;
                }
            }
        }
        
        // If we still don't have a value, set empty string (but only if the field doesn't already exist)
        if (!fieldValues.ContainsKey(targetFieldName))
        {
            fieldValues[targetFieldName] = "";
            Console.WriteLine($"No value found for {targetFieldName}, setting empty string");
        }
    }
    
    /// <summary>
    /// Format client names and addresses with proper capitalization
    /// </summary>
    private static void FormatClientInformation(Dictionary<string, string> fieldValues)
    {
        string[] fieldsToFormat = { "ClientName", "ClientAddress1", "ClientAddress2", "ClientAddress3", 
                                   "ContactFirstName", "ContactLastName" };
        
        foreach (var field in fieldsToFormat)
        {
            if (fieldValues.TryGetValue(field, out string value))
            {
                fieldValues[field] = ProperCapitalize(value);
                Console.WriteLine($"Formatted {field}: {fieldValues[field]}");
            }
        }
    }
    
    /// <summary>
    /// Format date fields to remove leading zeros
    /// </summary>
    private static void FormatDates(Dictionary<string, string> fieldValues)
    {
        string[] dateFields = { "EffectiveDate", "ExpirationDate", "CoverageRetroActiveDate", "TodaysDate", "QuoteValidUntil" };
        
        foreach (var field in dateFields)
        {
            if (fieldValues.TryGetValue(field, out string dateStr) && !string.IsNullOrEmpty(dateStr))
            {
                if (DateTime.TryParse(dateStr, out DateTime date))
                {
                    // Format without leading zeros for month (M instead of MM)
                    fieldValues[field] = date.ToString("M/d/yyyy");
                    Console.WriteLine($"Formatted date {field}: {fieldValues[field]}");
                }
            }
        }
        
        // Special handling for retroactive date - set to "N/A" if empty
        if (fieldValues.TryGetValue("CoverageRetroActiveDate", out string retroDate) && string.IsNullOrWhiteSpace(retroDate))
        {
            fieldValues["CoverageRetroActiveDate"] = "N/A";
            Console.WriteLine("Set empty CoverageRetroActiveDate to 'N/A'");
        }
    }
    
    /// <summary>
    /// Format text with proper capitalization (Title Case)
    /// </summary>
    private static string ProperCapitalize(string text)
    {
        if (string.IsNullOrEmpty(text))
            return text;
        
        // If text is all uppercase, convert to title case
        if (text == text.ToUpper())
        {
            // Convert to lowercase first
            text = text.ToLower();
            
            // Split by spaces and capitalize first letter of each word
            return System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(text);
        }
        
        // If text is not all uppercase, assume it's already properly formatted
        return text;
    }
    
    /// <summary>
    /// Format a number without a dollar sign for display
    /// </summary>
    private static string FormatNumber(decimal number)
    {
        return number.ToString("F2");
    }
    
    /// <summary>
    /// Format a number with a dollar sign for display
    /// </summary>
    private static string FormatCurrency(decimal number)
    {
        return $"${number:F2}";
    }
} 