// 4-docx-filler.csx - Fills standard fields in the document
using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;

public static partial class DocxProcessor
{
    /// <summary>
    /// Master method to create the document from the template
    /// </summary>
    /// <param name="templatePath">Path to the template file</param>
    /// <param name="processedData">The processed data</param>
    /// <param name="outputPath">Path to save the output file</param>
    public static void CreateDocument(string templatePath, ProcessedData processedData, string outputPath)
    {
        try
        {
            Console.WriteLine($"Creating document from template: {templatePath}");
            
            // Copy the template to the output file
            CopyTemplate(templatePath, outputPath);
            
            // Open the document
            using (var doc = WordprocessingDocument.Open(outputPath, true))
            {
                // Fill standard fields
                FillStandardFields(doc, processedData.FieldValues);
                
                // Handle repeating table rows
                HandleRepeatingTables(doc, processedData.JsonData, processedData.FieldValues);
                
                // Process hyperlinks for payments
                ProcessPaymentLinks(doc, processedData.FieldValues, processedData.TotalDeposit, processedData.TotalPolicyCost);
                
                // Save the document
                doc.MainDocumentPart.Document.Save();
                
                Console.WriteLine("Document successfully created and saved");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating document: {ex.Message}");
            Console.WriteLine(ex.StackTrace);
            throw;
        }
    }
    
    /// <summary>
    /// Copies the template file to the output location
    /// </summary>
    /// <param name="templatePath">Path to the template file</param>
    /// <param name="outputPath">Path to save the output file</param>
    private static void CopyTemplate(string templatePath, string outputPath)
    {
        try
        {
            Console.WriteLine($"Copying template file to: {outputPath}");
            
            // Create a temporary file name in the same directory
            string tempFile = Path.Combine(
                Path.GetDirectoryName(outputPath),
                Path.GetRandomFileName() + ".tmp");
            
            // First copy to the temp file
            File.Copy(templatePath, tempFile, true);
            
            try
            {
                // If destination exists, delete it
                if (File.Exists(outputPath))
                {
                    File.Delete(outputPath);
                }
                
                // Move the temp file to the destination
                File.Move(tempFile, outputPath);
            }
            catch (Exception)
            {
                // If that fails, try again after a delay
                System.Threading.Thread.Sleep(1000);
                
                if (File.Exists(outputPath))
                {
                    File.Delete(outputPath);
                }
                
                File.Move(tempFile, outputPath);
            }
            
            Console.WriteLine("Template file successfully copied");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error copying template file: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// Fills standard fields in the document
    /// </summary>
    /// <param name="doc">The document</param>
    /// <param name="fieldValues">Dictionary of field values</param>
    private static void FillStandardFields(WordprocessingDocument doc, Dictionary<string, string> fieldValues)
    {
        Console.WriteLine("Filling standard fields in document...");
        
        // Log available field values for address fields
        // Console.WriteLine("\nDEBUG: Field values for address fields before filling:");
        // Console.WriteLine($"  ClientName: '{fieldValues.GetValueOrDefault("ClientName", "<MISSING>")}'");
        // Console.WriteLine($"  ClientAddress1: '{fieldValues.GetValueOrDefault("ClientAddress1", "<MISSING>")}'");
        // Console.WriteLine($"  ClientAddress2: '{fieldValues.GetValueOrDefault("ClientAddress2", "<MISSING>")}'");
        // Console.WriteLine($"  ClientAddress3: '{fieldValues.GetValueOrDefault("ClientAddress3", "<MISSING>")}'");
        
        var controls = doc.MainDocumentPart.Document.Descendants<SdtElement>().ToList();
        Console.WriteLine($"Found {controls.Count} content controls in document");
        
        // First, log all content controls that start with "var" to see what's available
        Console.WriteLine("\nDEBUG: All content controls with 'var' prefix:");
        foreach (var ctrl in controls)
        {
            var alias = ctrl.SdtProperties?.GetFirstChild<SdtAlias>();
            if (alias != null)
            {
                string title = alias.Val.Value;
                if (title.StartsWith("var"))
                {
                    Console.WriteLine($"  Content control: {title}");
                }
            }
        }
        
        // Specifically look for client address content controls
        Console.WriteLine("\nDEBUG: Looking for client address content controls:");
        var addressControls = new List<string> { "varClientName", "varClientAddress1", "varClientAddress2", "varClientAddress3" };
        foreach (var addressControl in addressControls)
        {
            bool found = false;
            foreach (var ctrl in controls)
            {
                var alias = ctrl.SdtProperties?.GetFirstChild<SdtAlias>();
                if (alias != null && alias.Val.Value == addressControl)
                {
                    found = true;
                    Console.WriteLine($"  Found {addressControl} content control");
                    break;
                }
            }
            if (!found)
            {
                Console.WriteLine($"  WARNING: Could not find {addressControl} content control");
            }
        }
        
        foreach (var ctrl in controls)
        {
            var alias = ctrl.SdtProperties?.GetFirstChild<SdtAlias>();
            if (alias == null)
                continue;
            
            string title = alias.Val.Value;
            
            // Skip table controls, they'll be handled separately
            if (title.StartsWith("Table."))
                continue;
            
            // Handle variable fields (var prefix)
            if (title.StartsWith("var"))
            {
                string fieldName = title.Substring(3);
                if (fieldValues.TryGetValue(fieldName, out string value))
                {
                    bool isMonetaryValue = fieldName.Contains("DollarAmount") || fieldName.Contains("Fee") || 
                                         fieldName.Contains("Premium") || fieldName.Contains("Cost");
                    
                    SetContentControlText(ctrl, value, false, isMonetaryValue);
                    Console.WriteLine($"Set {title} to: {value}");
                    
                    // Add extra logging for address fields
                    if (fieldName == "ClientName" || fieldName == "ClientAddress1" || 
                        fieldName == "ClientAddress2" || fieldName == "ClientAddress3")
                    {
                        Console.WriteLine($"DEBUG: Set {title} to '{value}'");
                    }
                }
                else
                {
                    Console.WriteLine($"No value found for field: {fieldName}");
                    
                    // Add extra warning for address fields
                    if (fieldName == "ClientName" || fieldName == "ClientAddress1" || 
                        fieldName == "ClientAddress2" || fieldName == "ClientAddress3")
                    {
                        Console.WriteLine($"WARNING: Missing value for address field: {fieldName}");
                    }
                }
            }
            // Handle editable fields (field prefix)
            else if (title.StartsWith("field"))
            {
                string fieldName = title.Substring(5);
                if (fieldValues.TryGetValue(fieldName, out string value))
                {
                    bool isMonetaryValue = fieldName.Contains("DollarAmount") || fieldName.Contains("Fee") || 
                                         fieldName.Contains("Premium") || fieldName.Contains("Cost");
                    
                    SetContentControlText(ctrl, value, true, isMonetaryValue);
                    Console.WriteLine($"Set {title} to: {value}");
                }
                else
                {
                    Console.WriteLine($"No value found for field: {fieldName}");
                }
            }
        }
    }
    
    /// <summary>
    /// Sets text in a content control
    /// </summary>
    /// <param name="sdtElement">The content control element</param>
    /// <param name="text">The text to set</param>
    /// <param name="isFieldPrefix">Whether this is a field prefix control</param>
    /// <param name="isMonetaryValue">Whether this is a monetary value</param>
    public static void SetContentControlText(OpenXmlElement sdtElement, string text, bool isFieldPrefix = false, bool isMonetaryValue = false)
    {
        Console.WriteLine($"-----Setting content control text: {text}");
        // Process monetary values
        if (isMonetaryValue)
        {
            
            text = FormatAmountWithoutDollarSign(text);
          
        }
        text = text.Replace("%", "");
        // Special handling for TaxFeeItemDollarAmount
        if (sdtElement is SdtElement sdtEl)
        {
            var alias = sdtEl.SdtProperties?.GetFirstChild<SdtAlias>();
            if (alias != null && alias.Val.Value == "fieldTaxFeeItemDollarAmount")
            {
                Console.WriteLine($"Special handling for fieldTaxFeeItemDollarAmount: '{text}'");
                text = FormatAmountWithoutDollarSign(text);
                
                // Set content directly to avoid formatting issues
                var sdtContent = sdtEl.GetFirstChild<SdtContentBlock>();
                if (sdtContent != null)
                {
                    sdtContent.RemoveAllChildren();
                    sdtContent.AppendChild(new Run(new Text(text)));
                    return;
                }
            }
        }
    
        // Find all Text elements within the content control
        var textElements = sdtElement.Descendants<Text>().ToList();
        if (textElements.Any())
        {
            // Set the text in the first text element
            textElements.First().Text = text;
            
            // Remove any additional text elements to avoid duplication
            foreach (var extra in textElements.Skip(1).ToList())
            {
                extra.Parent.Remove();
            }
        }
        else
        {
            // If no text elements exist, add a new one
            var sdtContent = sdtElement.Descendants<SdtContentBlock>().FirstOrDefault();
            if (sdtContent != null)
            {
                sdtContent.RemoveAllChildren();
                sdtContent.AppendChild(new Run(new Text(text)));
            }
        }
    }
    
    /// <summary>
    /// Format a monetary value without dollar sign (for field prefix controls)
    /// </summary>
    public static string FormatAmountWithoutDollarSign(string amount)
    {
        if (string.IsNullOrWhiteSpace(amount))
            return "";

        // Remove any existing dollar signs and trim whitespace
        string cleanAmount = amount.Trim().Replace("$", "");
        if (cleanAmount.StartsWith("$"))
        {
            cleanAmount = cleanAmount.Substring(1).Trim();
        }

        // Try to parse as decimal
        if (decimal.TryParse(cleanAmount, out decimal parsedAmount))
        {
            // Check if there are fractional parts
            if (parsedAmount % 1 == 0)
            {
                // No decimals needed, format with commas only
                return parsedAmount.ToString("N0");
            }
            else
            {
                // Format with two decimals and commas
                return parsedAmount.ToString("N2");
            }
        }

        return cleanAmount;
    }

    
    /// <summary>
    /// Format a monetary value with dollar sign (for var prefix controls)
    /// </summary>
    public static string FormatAmountWithDollarSign(string amount)
    {
        if (string.IsNullOrWhiteSpace(amount))
            return "";
            
        // Remove any existing dollar signs
        amount = amount.Trim().TrimStart('$');
        
        // Try to parse as decimal and format with two decimal places and dollar sign
        if (decimal.TryParse(amount, out decimal parsedAmount))
        {
            return $"${parsedAmount:N2}";
        }
        
        // If not parseable but not empty, add dollar sign
        if (!string.IsNullOrEmpty(amount))
        {
            return "$" + amount;
        }
        
        return amount;
    }
    
    /// <summary>
    /// Process payment links in the document
    /// </summary>
    private static void ProcessPaymentLinks(WordprocessingDocument doc, Dictionary<string, string> fieldValues, 
                                         decimal totalDeposit, decimal totalPolicyCost)
    {
        Console.WriteLine("Processing payment links...");
        
        var hyperlinks = doc.MainDocumentPart.Document.Descendants<Hyperlink>().ToList();
        Console.WriteLine($"Found {hyperlinks.Count} hyperlinks in document");
        
        foreach (var hyperlink in hyperlinks)
        {
            try
            {
                // Get relationship ID
                var relationshipId = hyperlink.Id?.Value;
                if (string.IsNullOrEmpty(relationshipId))
                    continue;
                
                // Get current URL
                var relationship = doc.MainDocumentPart.HyperlinkRelationships
                    .FirstOrDefault(r => r.Id == relationshipId);
                    
                if (relationship == null)
                    continue;
                    
                string currentUrl = relationship.Uri.ToString();
                Console.WriteLine($"Processing link with URL: {currentUrl}");
                
                string newUrl = "";
                
                // Check which placeholder URL we're dealing with
                if (currentUrl.Contains("varsurefiredownpaymentlink", StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine("Found down payment link placeholder");
                    
                    // Build the down payment link
                    string clientName = fieldValues.GetValueOrDefault("ClientName", "");
                    string depositAmount = totalDeposit.ToString("F2");
                    string primaryCoverage = fieldValues.GetValueOrDefault("PrimaryCoverage", "");
                    
                    // Sanitize client name and primary coverage - remove all non-alphanumeric characters except spaces
                    clientName = Regex.Replace(clientName, @"[^a-zA-Z0-9\s]", "").Trim();
                    primaryCoverage = Regex.Replace(primaryCoverage, @"[^a-zA-Z0-9\s]", "").Trim();
                    
                    newUrl = $"https://metroinsurance.epaypolicy.com/?payer={Uri.EscapeDataString(clientName)}&emailAddress=&amount={depositAmount}&comments={Uri.EscapeDataString(primaryCoverage)}%20Policy%20Proposal%20Down%20Payment";
                    Console.WriteLine($"Created down payment link: {newUrl}");
                }
                else if (currentUrl.Contains("varsurefirefullpaymentlink", StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine("Found full payment link placeholder");
                    
                    // Build the full payment link
                    string clientName = fieldValues.GetValueOrDefault("ClientName", "");
                    string totalAmount = totalPolicyCost.ToString("F2");
                    string primaryCoverage = fieldValues.GetValueOrDefault("PrimaryCoverage", "");
                    
                    // Sanitize client name and primary coverage
                    clientName = Regex.Replace(clientName, @"[^a-zA-Z0-9\s]", "").Trim();
                    primaryCoverage = Regex.Replace(primaryCoverage, @"[^a-zA-Z0-9\s]", "").Trim();
                    
                    newUrl = $"https://metroinsurance.epaypolicy.com/?payer={Uri.EscapeDataString(clientName)}&emailAddress=&amount={totalAmount}&comments={Uri.EscapeDataString(primaryCoverage)}%20Policy%20Proposal%20Full%20Payment";
                    Console.WriteLine($"Created full payment link: {newUrl}");
                }
                
                // If we have a new URL, update the hyperlink
                if (!string.IsNullOrEmpty(newUrl))
                {
                    // Get original link text
                    string linkText = hyperlink.Descendants<Text>().FirstOrDefault()?.Text ?? "Make a Payment";
                    
                    // Remove old relationship
                    doc.MainDocumentPart.DeleteReferenceRelationship(relationship);
                    
                    // Create new relationship
                    var newRelationship = doc.MainDocumentPart.AddHyperlinkRelationship(
                        new Uri(newUrl, UriKind.Absolute), true);
                    hyperlink.Id = newRelationship.Id;
                    
                    // Create a new run with the original text and formatting
                    var run = new Run(new Text(linkText));
                    
                    // Add formatting properties (bold, underline, blue)
                    var runProperties = new RunProperties(
                        new Bold(),
                        new Underline() { Val = UnderlineValues.Single },
                        new Color() { Val = "0000FF" }
                    );
                    run.PrependChild(runProperties);
                    
                    // Update the hyperlink
                    hyperlink.RemoveAllChildren();
                    hyperlink.Append(run);
                    
                    Console.WriteLine($"Updated hyperlink with new URL: {newUrl}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing hyperlink: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// Sets up duplication configurations for repeating tables
    /// </summary>
    /// <returns>List of duplication configurations</returns>
    private static List<DuplicationConfig> SetupDuplicationConfigs()
    {
        var configs = new List<DuplicationConfig>();
        
        // Add configurations for different repeating tables
        configs.Add(new DuplicationConfig { 
            IdentifierField = "varLimitName", 
            ArrayPath = "Coverages.Coverage[0].Limits.Limit", 
            Fields = new List<string> { "LimitName", "LimitDollarAmount" } 
        });
        
        configs.Add(new DuplicationConfig { 
            IdentifierField = "varDeductibleName", 
            ArrayPath = "Coverages.Coverage[0].Deductibles.Deductible", 
            Fields = new List<string> { "DeductibleName", "DeductibleDollarAmount" } 
        });
        
        configs.Add(new DuplicationConfig { 
            IdentifierField = "varRateClassCode", 
            ArrayPath = "RatingBasises.RatingBasis", 
            Fields = new List<string> { "LocationNumber", "RateClassCode", "RateDescription", "RateBasis", "RateExposure", "NetRate", "RatePremium" } 
        });
        
        configs.Add(new DuplicationConfig { 
            IdentifierField = "varLocationFullAddress", 
            ArrayPath = "Locations.Location", 
            Fields = new List<string> { "LocationNumber", "LocationFullAddress", "LocationDescription" } 
        });
        
        configs.Add(new DuplicationConfig { 
            IdentifierField = "varEndorsementNumber", 
            ArrayPath = "Endorsements.Endorsement", 
            Fields = new List<string> { "EndorsementNumber", "EndorsementName" } 
        });
        
        configs.Add(new DuplicationConfig { 
            IdentifierField = "fieldTaxFeeItemName", 
            ArrayPath = "Financials.TaxFeeItems.TaxFeeItem", 
            Fields = new List<string> { "TaxFeeItemName", "TaxFeeItemDollarAmount" } 
        });
        
        return configs;
    }
    
    /// <summary>
    /// Handles repeating table rows
    /// </summary>
    /// <param name="doc">The document</param>
    /// <param name="jsonData">The JSON data</param>
    /// <param name="fieldValues">Dictionary of field values</param>
    public static void HandleRepeatingTables(WordprocessingDocument doc, JObject jsonData, Dictionary<string, string> fieldValues)
    {
        // Call the TableHandler to process repeating tables
        TableHandler.ProcessRepeatingTables(doc, jsonData, fieldValues);
    }
} 