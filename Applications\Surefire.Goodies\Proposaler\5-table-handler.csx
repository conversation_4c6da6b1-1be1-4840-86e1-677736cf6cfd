// 5-table-handler.csx - Handles repeating table rows
using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Newtonsoft.Json.Linq;

public static partial class TableHandler
{
    /// <summary>
    /// Process repeating tables in the document
    /// </summary>
    /// <param name="doc">The document</param>
    /// <param name="jsonData">The JSON data</param>
    /// <param name="fieldValues">Dictionary of field values</param>
    public static void ProcessRepeatingTables(WordprocessingDocument doc, JObject jsonData, Dictionary<string, string> fieldValues)
    {
        Console.WriteLine("Processing repeating tables...");
        
        // Get duplication configurations
        var duplicationConfigs = SetupDuplicationConfigs();
        
        // Use "InsurancePolicy" if available, otherwise use the root
        JObject data = jsonData["InsurancePolicy"] as JObject ?? jsonData;
        
        // Find all tables in the document
        var tables = doc.MainDocumentPart.Document.Descendants<Table>().ToList();
        Console.WriteLine($"Found {tables.Count} tables in document");
        
        // Find all table rows that require duplication
        var tablesToDuplicate = FindRowsToDuplicate(tables, duplicationConfigs);
        
        // Process duplication rows in descending order (so indexes don't get affected by insertions)
        var sortedDuplicates = tablesToDuplicate
            .OrderByDescending(dup => dup.table.ChildElements.ToList().IndexOf(dup.row))
            .ToList();
            
        // Track which tables we've already processed using a composite key of identifier and table position
        var processedTables = new HashSet<string>();
        
        // Track if we've already processed TaxFeeItems to prevent multiple processing
        bool taxFeeItemProcessed = false;
        
        // Process each table row for duplication
        foreach (var dup in sortedDuplicates)
        {
            // Generate a unique key based on the identifier field and the table position in document
            int tableIndex = doc.MainDocumentPart.Document.Body.Descendants<Table>().ToList().IndexOf(dup.table);
            int rowIndex = dup.table.ChildElements.ToList().IndexOf(dup.row);
            string uniqueKey = $"{dup.config.IdentifierField}_{tableIndex}_{rowIndex}";
            
            // Skip if we've already processed this specific table instance
            if (!processedTables.Add(uniqueKey))
            {
                Console.WriteLine($"Skipping duplicate processing for {dup.config.IdentifierField} at table index {tableIndex}, row {rowIndex}");
                continue;
            }
            
            Console.WriteLine($"Processing repeating table for {dup.config.IdentifierField} at table index {tableIndex}, row {rowIndex}");
            
            // Special handling for TaxFeeItems - only process one instance
            if (dup.config.IdentifierField == "fieldTaxFeeItemName")
            {
                // Skip if we've already processed TaxFeeItems once
                if (taxFeeItemProcessed)
                {
                    Console.WriteLine("Skipping additional TaxFeeItem table - already processed");
                    continue;
                }
                
                ProcessTaxFeeItemTable(data, dup.table, dup.row, dup.config);
                taxFeeItemProcessed = true;
                continue;
            }
            
            // Get the array of items for this table
            var array = JsonReader.TryGetJsonArray(data, dup.config.ArrayPath, dup.config.ArrayPath);
            if (array == null || array.Count == 0)
            {
                Console.WriteLine($"No items found for {dup.config.IdentifierField}");
                continue;
            }
            
            Console.WriteLine($"Found {array.Count} items for {dup.config.IdentifierField}");
            
            // Clone the template row before removing it
            var templateRow = (TableRow)dup.row.CloneNode(true);
            
            // Check if row has a parent before removing it
            if (dup.row.Parent != null)
            {
                dup.row.Remove();
            }
            else
            {
                Console.WriteLine($"Warning: Template row for {dup.config.IdentifierField} doesn't have a parent, it may have been removed already");
            }
            
            // Add a new row for each item in the array
            int rowsAdded = 0;
            for (int i = 0; i < array.Count; i++)
            {
                var item = array[i] as JObject;
                Console.WriteLine($"Processing item {i} for {dup.config.IdentifierField}");
                
                // Clone the template row
                var newRow = (TableRow)templateRow.CloneNode(true);
                
                // Special handling for varLimitName - clear second column content only for repeated rows
                if (dup.config.IdentifierField == "varLimitName" || dup.config.IdentifierField == "varDeductibleName")
                {
                    var cells = newRow.Elements<TableCell>().ToList();
                    if (cells.Count > 1 && rowsAdded > 0)
                    {
                        // Clear all content from the second cell
                        cells[1].RemoveAllChildren<Paragraph>();
                        cells[1].Append(new Paragraph());
                    }
                }
                
                // Process each content control in the new row
                foreach (var ctrl in newRow.Descendants<SdtElement>())
                {
                    var alias = ctrl.SdtProperties?.GetFirstChild<SdtAlias>();
                    if (alias == null)
                        continue;
                        
                    string title = alias.Val.Value;
                    
                    // Process content controls with matching field prefix (var or field)
                    string prefix = title.StartsWith("var") ? "var" : "field";
                    if (title.StartsWith(prefix))
                    {
                        // Extract field name (remove var/field prefix)
                        string fieldName = title.Substring(prefix.Length);
                        
                        // Get the field value from the item
                        string value = GetFieldValueFromItem(item, fieldName);
                        if (!string.IsNullOrEmpty(value))
                        {
                            // Determine if it's a monetary value
                            bool isMonetaryValue = fieldName.Contains("DollarAmount") || fieldName.Contains("Fee") || 
                                                fieldName.Contains("Premium") || fieldName.Contains("Cost") || 
                                                fieldName.Contains("Amount");
                                                
                            // Set the text with appropriate formatting
                            SetContentControlText(ctrl, value, title.StartsWith("field"), isMonetaryValue);
                            Console.WriteLine($"  Set {fieldName} to {value} in row {i}");
                        }
                    }
                }
                
                // Insert the new row at the current position
                try
                {
                    if (rowIndex <= dup.table.ChildElements.Count)
                    {
                        dup.table.InsertAt(newRow, rowIndex++);
                        rowsAdded++;
                    }
                    else
                    {
                        // If somehow the index is out of bounds, append to end
                        dup.table.Append(newRow);
                        rowIndex = dup.table.ChildElements.Count;
                        rowsAdded++;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error inserting row at index {rowIndex}: {ex.Message}");
                    try
                    {
                        dup.table.Append(newRow);
                        rowsAdded++;
                    }
                    catch (Exception innerEx)
                    {
                        Console.WriteLine($"Fatal error appending row: {innerEx.Message}");
                    }
                }
            }
            
            Console.WriteLine($"Added {rowsAdded} rows for {dup.config.IdentifierField}");
            
            // Apply special table formatting (like vertical cell merging) if needed
            ApplySpecialTableFormatting(dup.table, dup.config.IdentifierField, rowIndex - rowsAdded, rowsAdded);
        }
    }
    
    /// <summary>
    /// Special processing for TaxFeeItem tables which have 2 columns instead of 3
    /// </summary>
    /// <param name="data">The JSON data</param>
    /// <param name="table">The table containing the TaxFeeItem row</param>
    /// <param name="row">The template row to duplicate</param>
    /// <param name="config">The duplication configuration</param>
    private static void ProcessTaxFeeItemTable(JObject data, Table table, TableRow row, DuplicationConfig config)
    {
        Console.WriteLine("Using special handler for TaxFeeItem table with 2 columns");
        
        // Dump some debug info about the JSON structure
        Console.WriteLine($"JSON contains Financials: {data.ContainsKey("Financials")}");
        
        // Try to get the data array using any of the defined paths
        JArray arrayToProcess = null;
        
        // First try the primary path
        arrayToProcess = JsonReader.TryGetJsonArray(data, config.ArrayPath, config.ArrayPath);
        if (arrayToProcess?.Count > 0)
        {
            Console.WriteLine($"Found {arrayToProcess.Count} items using primary path: {config.ArrayPath}");
        }
        else
        {
            // Try all alternative paths if defined
            if (config.AlternativePaths != null && config.AlternativePaths.Count > 0)
            {
                foreach (var altPath in config.AlternativePaths)
                {
                    arrayToProcess = JsonReader.TryGetJsonArray(data, altPath, altPath);
                    if (arrayToProcess?.Count > 0)
                    {
                        Console.WriteLine($"Found {arrayToProcess.Count} items using alternative path: {altPath}");
                        break;
                    }
                }
            }
        }
        
        // If we still don't have data, try to extract directly from the JSON structure
        if (arrayToProcess == null || arrayToProcess.Count == 0)
        {
            if (data.ContainsKey("Financials"))
            {
                var financials = data["Financials"] as JObject;
                if (financials != null)
                {
                    Console.WriteLine($"Financials contains TaxFeeItems: {financials.ContainsKey("TaxFeeItems")}");
                    
                    // Try different potential structures for TaxFeeItems
                    var directItems = financials["TaxFeeItems"] as JArray;
                    var itemsObject = financials["TaxFeeItems"] as JObject;
                    var nestedItems = financials["TaxFeeItems"]?["TaxFeeItem"] as JArray;
                    
                    if (directItems?.Count > 0)
                    {
                        Console.WriteLine($"Using direct items array: {directItems.Count} items");
                        arrayToProcess = directItems;
                    }
                    else if (nestedItems?.Count > 0)
                    {
                        Console.WriteLine($"Using nested items array: {nestedItems.Count} items");
                        arrayToProcess = nestedItems;
                    }
                    else if (itemsObject != null)
                    {
                        // Try to convert object to array if it has numeric properties
                        var convertedArray = new JArray();
                        foreach (var prop in itemsObject.Properties())
                        {
                            if (int.TryParse(prop.Name, out _))
                            {
                                convertedArray.Add(prop.Value);
                            }
                        }
                        
                        if (convertedArray.Count > 0)
                        {
                            Console.WriteLine($"Converted object to array with {convertedArray.Count} items");
                            arrayToProcess = convertedArray;
                        }
                    }
                    
                    // Try TaxAndSurcharges if TaxFeeItems doesn't exist
                    if (arrayToProcess == null && financials.ContainsKey("TaxAndSurcharges"))
                    {
                        arrayToProcess = financials["TaxAndSurcharges"] as JArray;
                        if (arrayToProcess?.Count > 0)
                        {
                            Console.WriteLine($"Found {arrayToProcess.Count} items in TaxAndSurcharges");
                        }
                    }
                    
                    // Try TaxAndFees if others don't exist
                    if (arrayToProcess == null && financials.ContainsKey("TaxAndFees"))
                    {
                        var taxAndFees = financials["TaxAndFees"] as JObject;
                        if (taxAndFees != null && taxAndFees.ContainsKey("TaxFee"))
                        {
                            arrayToProcess = taxAndFees["TaxFee"] as JArray;
                            if (arrayToProcess?.Count > 0)
                            {
                                Console.WriteLine($"Found {arrayToProcess.Count} items in TaxAndFees.TaxFee");
                            }
                        }
                    }
                }
            }
            
            // Try the root TaxAndFees if it exists
            if (arrayToProcess == null && data.ContainsKey("TaxAndFees"))
            {
                var taxAndFees = data["TaxAndFees"] as JObject;
                if (taxAndFees != null && taxAndFees.ContainsKey("TaxFee"))
                {
                    arrayToProcess = taxAndFees["TaxFee"] as JArray;
                    if (arrayToProcess?.Count > 0)
                    {
                        Console.WriteLine($"Found {arrayToProcess.Count} items in root TaxAndFees.TaxFee");
                    }
                }
            }
        }
        
        // If we still have no data, abort processing
        if (arrayToProcess == null || arrayToProcess.Count == 0)
        {
            Console.WriteLine("No TaxFeeItems found in any expected location");
            // Dump the first level of JSON keys to help debug
            Console.WriteLine("Available top-level JSON keys: " + string.Join(", ", data.Properties().Select(p => p.Name)));
            return;
        }
        
        // Process the array we found
        ProcessTaxFeeItemArray(table, row, arrayToProcess);
    }
    
    /// <summary>
    /// Process a TaxFeeItem array into table rows
    /// </summary>
    private static void ProcessTaxFeeItemArray(Table table, TableRow row, JArray array)
    {
        // Clone the template row before removing it
        int rowIndex = table.ChildElements.ToList().IndexOf(row);
        var templateRow = (TableRow)row.CloneNode(true);
        
        // Check if row has a parent before trying to remove it
        if (row.Parent != null)
        {
            row.Remove();
        }
        else
        {
            Console.WriteLine("Warning: Template row doesn't have a parent, it may have been removed already");
        }
        
        // If rowIndex is invalid, try to find a reasonable position to insert rows
        if (rowIndex < 0)
        {
            Console.WriteLine("Warning: Could not determine row index in table, will append to the end");
            rowIndex = table.ChildElements.Count;
        }
        
        // Log how many items we're processing (for debugging)
        //Console.WriteLine($"Processing {array.Count} TaxFeeItems into table at row index {rowIndex}");
        
        // Count how many rows we add to verify correct processing
        int rowsAdded = 0;
        
        // Add a new row for each tax/fee item
        for (int i = 0; i < array.Count; i++)
        {
            var item = array[i] as JObject;
            if (item == null)
            {
                Console.WriteLine($"Warning: Item {i} is not a JObject, skipping");
                continue;
            }
            
            // Safe preview of JSON item - handle empty or null JSON
            // string jsonPreview = item.ToString(Newtonsoft.Json.Formatting.None);
            // if (string.IsNullOrEmpty(jsonPreview))
            // {
            //     Console.WriteLine($"Processing TaxFeeItem {i}: [Empty JSON]");
            // }
            // else
            // {
            //     Console.WriteLine($"Processing TaxFeeItem {i}: {jsonPreview.Substring(0, Math.Min(100, jsonPreview.Length))}...");
            // }
            
            // Clone the template row
            var newRow = (TableRow)templateRow.CloneNode(true);
            
            // Get cells - TaxFeeItem table has only 2 columns
            var cells = newRow.Elements<TableCell>().ToList();
            if (cells.Count < 2)
            {
                Console.WriteLine("Warning: TaxFeeItem row does not have at least 2 cells");
                continue;
            }
            
            // Track if we successfully populated any fields
            bool foundAnyFields = false;
            
            // Process each content control in the new row
            foreach (var ctrl in newRow.Descendants<SdtElement>())
            {
                var alias = ctrl.SdtProperties?.GetFirstChild<SdtAlias>();
                if (alias == null)
                    continue;
                    
                string title = alias.Val.Value;
                Console.WriteLine($"  Found content control with alias: {title}");
                
                // Process field controls (TaxFeeItems use field prefix)
                if (title.StartsWith("field"))
                {
                    // Extract field name (remove field prefix)
                    string fieldName = title.Substring(5);
                    
                    // Get value - check for special TaxFeeItem naming
                    string value = "";
                    
                    if (fieldName == "TaxFeeItemName")
                    {
                        // Try different possible property names for the name
                        value = item["Name"]?.ToString() ?? 
                               item["TaxFeeName"]?.ToString() ?? 
                               item["Description"]?.ToString() ?? 
                               item["TaxFeeItemName"]?.ToString() ?? 
                               item["TaxFeeItem"]?["Name"]?.ToString() ?? 
                               item["TaxFeeItem"]?["TaxFeeName"]?.ToString() ?? 
                               item["TaxFeeItem"]?["Description"]?.ToString() ?? 
                               item["TaxFeeItem"]?["TaxFeeItemName"]?.ToString() ?? "";
                    }
                    else if (fieldName == "TaxFeeItemDollarAmount")
                    {
                        // Try different possible property names for the amount
                        value = item["Amount"]?.ToString() ?? 
                               item["Premium"]?.ToString() ?? 
                               item["Value"]?.ToString() ?? 
                               item["TaxFeeItemDollarAmount"]?.ToString() ?? 
                               item["TaxFeeItem"]?["Amount"]?.ToString() ?? 
                               item["TaxFeeItem"]?["Premium"]?.ToString() ?? 
                               item["TaxFeeItem"]?["Value"]?.ToString() ?? 
                               item["TaxFeeItem"]?["TaxFeeItemDollarAmount"]?.ToString() ?? "";
                    }
                    else
                    {
                        // Try direct property or nested property
                        value = item[fieldName]?.ToString() ?? 
                                item["TaxFeeItem"]?[fieldName]?.ToString() ?? "";
                    }
                    
                    Console.WriteLine($"  For field {fieldName}, found value: '{value}'");
                    
                    if (!string.IsNullOrEmpty(value))
                    {
                        foundAnyFields = true;
                        
                        // Determine if it's a monetary value
                        bool isMonetaryValue = fieldName.Contains("DollarAmount") || fieldName.Contains("Fee") || 
                                            fieldName.Contains("Premium") || fieldName.Contains("Cost") || 
                                            fieldName.Contains("Amount");
                                            
                        // Set the text with appropriate formatting - always using field formatting for TaxFeeItems
                        SetContentControlText(ctrl, value, true, isMonetaryValue);
                        Console.WriteLine($"  Set {fieldName} to {value} in row {i}");
                    }
                }
            }
            
            if (!foundAnyFields)
            {
                Console.WriteLine($"Warning: No fields could be populated for TaxFeeItem {i}");
                continue; // Skip adding this row
            }
            
            try
            {
                // Always insert at the target rowIndex position and increment for the next row
                // This ensures rows are inserted in sequence at the original position
                if (rowIndex <= table.ChildElements.Count)
                {
                    table.InsertAt(newRow, rowIndex);
                    rowIndex++; // Increment for next insertion
                    rowsAdded++;
                }
                else
                {
                    // If rowIndex is somehow out of bounds, append to the end
                    table.Append(newRow);
                    rowIndex = table.ChildElements.Count;
                    rowsAdded++;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error inserting row at index {rowIndex}: {ex.Message}");
                try 
                {
                    // Fallback - try to append to the end of the table
                    table.Append(newRow);
                    rowsAdded++;
                } 
                catch (Exception innerEx) 
                {
                    Console.WriteLine($"Fatal error appending row: {innerEx.Message}");
                }
            }
        }
        
        Console.WriteLine($"Finished processing TaxFeeItems - added {rowsAdded} rows out of {array.Count} items");
    }
    
    /// <summary>
    /// Sets up duplication configurations for repeating tables
    /// </summary>
    /// <returns>List of duplication configurations</returns>
    private static List<DuplicationConfig> SetupDuplicationConfigs()
    {
        var configs = new List<DuplicationConfig>();
        
        // Add configurations for different repeating tables
        configs.Add(new DuplicationConfig { 
            IdentifierField = "varLimitName", 
            ArrayPath = "Coverages.Coverage[0].Limits.Limit", 
            Fields = new List<string> { "LimitName", "LimitDollarAmount" } 
        });
        
        configs.Add(new DuplicationConfig { 
            IdentifierField = "varDeductibleName", 
            ArrayPath = "Coverages.Coverage[0].Deductibles.Deductible", 
            Fields = new List<string> { "DeductibleName", "DeductibleDollarAmount" } 
        });
        
        configs.Add(new DuplicationConfig { 
            IdentifierField = "varRateClassCode", 
            ArrayPath = "RatingBasises.RatingBasis", 
            Fields = new List<string> { "LocationNumber", "RateClassCode", "RateDescription", "RateBasis", "RateExposure", "NetRate", "RatePremium" } 
        });
        
        configs.Add(new DuplicationConfig { 
            IdentifierField = "varLocationFullAddress", 
            ArrayPath = "Locations.Location", 
            Fields = new List<string> { "LocationNumber", "LocationFullAddress", "LocationDescription" } 
        });
        
        configs.Add(new DuplicationConfig { 
            IdentifierField = "varEndorsementNumber", 
            ArrayPath = "Endorsements.Endorsement", 
            Fields = new List<string> { "EndorsementNumber", "EndorsementName" } 
        });
        
        // Enhanced configuration for TaxFeeItem with different possible paths
        configs.Add(new DuplicationConfig { 
            IdentifierField = "fieldTaxFeeItemName", 
            ArrayPath = "Financials.TaxFeeItems", 
            // Add alternative paths that will be tried if primary path doesn't exist
            AlternativePaths = new List<string> { 
                "Financials.TaxFeeItems.TaxFeeItem", 
                "Financials.TaxAndSurcharges", 
                "Financials.TaxAndFees",
                "TaxAndFees.TaxFee"
            },
            Fields = new List<string> { 
                "TaxFeeItemName", "TaxFeeName", "Name", "Description",
                "TaxFeeItemDollarAmount", "Amount", "Premium", "Value" 
            } 
        });
        
        return configs;
    }
    
    /// <summary>
    /// Find rows that need to be duplicated in tables
    /// </summary>
    /// <param name="tables">List of tables in the document</param>
    /// <param name="configs">List of duplication configurations</param>
    /// <returns>List of tables and rows to duplicate with their configuration</returns>
    private static List<(Table table, TableRow row, DuplicationConfig config)> FindRowsToDuplicate(
        List<Table> tables, List<DuplicationConfig> configs)
    {
        var tablesToDuplicate = new List<(Table table, TableRow row, DuplicationConfig config)>();
        
        Console.WriteLine($"Looking for rows to duplicate in {tables.Count} tables with {configs.Count} configs");
        
        // Log the configuration identifiers we're looking for
        foreach (var config in configs)
        {
            Console.WriteLine($"Searching for identifier: {config.IdentifierField}");
        }
        
        int tableIndex = 0;
        foreach (var table in tables)
        {
            Console.WriteLine($"Examining table {tableIndex}");
            var rows = table.Descendants<TableRow>().ToList();
            Console.WriteLine($"  Table has {rows.Count} rows");
            
            int rowIndex = 0;
            foreach (var row in rows)
            {
                int controlCount = row.Descendants<SdtElement>().Count();
                Console.WriteLine($"  Row {rowIndex} has {controlCount} content controls");
                
                // Log all content control aliases in this row
                foreach (var control in row.Descendants<SdtElement>())
                {
                    var alias = control.SdtProperties?.GetFirstChild<SdtAlias>();
                    if (alias != null && alias.Val != null && alias.Val.HasValue)
                    {
                        Console.WriteLine($"    Control with alias: {alias.Val.Value}");
                    }
                }
                
                foreach (var config in configs)
                {
                    // Look for content controls with the identifier field
                    var sdt = row.Descendants<SdtElement>()
                        .FirstOrDefault(x =>
                        {
                            var alias = x.SdtProperties?.GetFirstChild<SdtAlias>();
                            return alias != null && 
                                   alias.Val != null && 
                                   alias.Val.HasValue && 
                                   alias.Val.Value == config.IdentifierField;
                        });

                    if (sdt != null)
                    {
                        int foundRowIndex = table.ChildElements.ToList().IndexOf(row);
                        Console.WriteLine($"  MATCH: Found row with {config.IdentifierField} in table at index {foundRowIndex}");
                        tablesToDuplicate.Add((table, row, config));
                    }
                }
                
                rowIndex++;
            }
            
            tableIndex++;
        }
        
        Console.WriteLine($"Found {tablesToDuplicate.Count} rows to duplicate total");
        return tablesToDuplicate;
    }
    
    /// <summary>
    /// Get a field value from a JSON object
    /// </summary>
    /// <param name="item">The JSON object</param>
    /// <param name="fieldName">The field name</param>
    /// <returns>The field value as a string</returns>
    private static string GetFieldValueFromItem(JObject item, string fieldName)
    {
        // Check for direct property match
        if (item.ContainsKey(fieldName))
        {
            return item[fieldName]?.ToString() ?? "";
        }
        
        // Handle special cases for different field types
        switch (fieldName)
        {
            case "TaxFeeItemName":
                return item["Name"]?.ToString() ?? 
                       item["TaxFeeName"]?.ToString() ?? 
                       item["Description"]?.ToString() ?? 
                       item["TaxFeeItemName"]?.ToString() ?? 
                       item["TaxFeeItem"]?["Name"]?.ToString() ?? 
                       item["TaxFeeItem"]?["TaxFeeName"]?.ToString() ?? 
                       item["TaxFeeItem"]?["Description"]?.ToString() ?? 
                       item["TaxFeeItem"]?["TaxFeeItemName"]?.ToString() ?? "";
                
            case "TaxFeeItemDollarAmount":
                return item["Amount"]?.ToString() ?? 
                       item["Premium"]?.ToString() ?? 
                       item["Value"]?.ToString() ?? 
                       item["TaxFeeItemDollarAmount"]?.ToString() ?? 
                       item["TaxFeeItem"]?["Amount"]?.ToString() ?? 
                       item["TaxFeeItem"]?["Premium"]?.ToString() ?? 
                       item["TaxFeeItem"]?["Value"]?.ToString() ?? 
                       item["TaxFeeItem"]?["TaxFeeItemDollarAmount"]?.ToString() ?? "";
                
            case "LimitName":
                return item["Name"]?.ToString() ?? 
                       item["LimitName"]?.ToString() ?? 
                       item["Description"]?.ToString() ?? "";
                
            case "LimitDollarAmount":
                return item["Amount"]?.ToString() ?? 
                       item["Value"]?.ToString() ?? 
                       item["LimitAmount"]?.ToString() ?? 
                       item["LimitDollarAmount"]?.ToString() ?? "";
                
            case "DeductibleName":
                return item["Name"]?.ToString() ?? 
                       item["DeductibleName"]?.ToString() ?? 
                       item["Description"]?.ToString() ?? "";
                
            case "DeductibleDollarAmount":
                return item["Amount"]?.ToString() ?? 
                       item["Value"]?.ToString() ?? 
                       item["DeductibleAmount"]?.ToString() ?? 
                       item["DeductibleDollarAmount"]?.ToString() ?? "";
                
            case "RateClassCode":
                return item["ClassCode"]?.ToString() ?? 
                       item["RateClassCode"]?.ToString() ?? 
                       item["Code"]?.ToString() ?? "";
                
            case "RateDescription":
                return item["Description"]?.ToString() ?? 
                       item["RateDescription"]?.ToString() ?? 
                       item["ClassDescription"]?.ToString() ?? "";
                
            case "LocationFullAddress":
                // Try combined address first
                string address = item["FullAddress"]?.ToString() ?? 
                                item["LocationFullAddress"]?.ToString() ?? "";
                
                // If no combined address, try to build from components
                if (string.IsNullOrEmpty(address))
                {
                    string street = item["Address"]?.ToString() ?? 
                                   item["StreetAddress"]?.ToString() ?? 
                                   item["Street"]?.ToString() ?? "";
                    
                    string city = item["City"]?.ToString() ?? "";
                    string state = item["State"]?.ToString() ?? 
                                  item["StateCode"]?.ToString() ?? "";
                    string zip = item["Zip"]?.ToString() ?? 
                                item["ZipCode"]?.ToString() ?? 
                                item["PostalCode"]?.ToString() ?? "";
                    
                    // Combine components
                    if (!string.IsNullOrEmpty(street))
                    {
                        address = street;
                        if (!string.IsNullOrEmpty(city))
                        {
                            address += ", " + city;
                        }
                        if (!string.IsNullOrEmpty(state))
                        {
                            address += ", " + state;
                        }
                        if (!string.IsNullOrEmpty(zip))
                        {
                            address += " " + zip;
                        }
                    }
                }
                
                return address;
                
            default:
                // For any other field, try nested properties
                foreach (var prop in item.Properties())
                {
                    // Check if this property is a JObject and might contain our field
                    var nestedObj = prop.Value as JObject;
                    if (nestedObj != null && nestedObj.ContainsKey(fieldName))
                    {
                        return nestedObj[fieldName]?.ToString() ?? "";
                    }
                }
                break;
        }
        
        return "";
    }
    
    /// <summary>
    /// Apply special formatting to tables like vertical cell merging
    /// </summary>
    /// <param name="table">The table</param>
    /// <param name="identifierField">The identifier field</param>
    /// <param name="startRowIndex">The starting row index</param>
    /// <param name="rowCount">The number of rows</param>
    private static void ApplySpecialTableFormatting(Table table, string identifierField, int startRowIndex, int rowCount)
    {
        // Apply vertical merging for limits table
        if (identifierField == "varLimitName")
        {
            Console.WriteLine($"Applying vertical merge for limits table-------------------------{rowCount}-{startRowIndex}-{identifierField}");
            //ApplyVerticalMerging(table, "Limits", startRowIndex, rowCount);
        }
        // Apply vertical merging for deductibles table
        else if (identifierField == "varDeductibleName")
        {
            Console.WriteLine($"Applying vertical merge for deductibles table-------------------------{rowCount}-{startRowIndex}-{identifierField}");
            //ApplyVerticalMerging(table, "Deductibles", startRowIndex, rowCount);
        }
    }
    
    /// <summary>
    /// Apply vertical merging to a table
    /// </summary>
    /// <param name="table">The table</param>
    /// <param name="labelText">The label text for the merge</param>
    /// <param name="startRowIndex">The starting row index</param>
    /// <param name="rowCount">The number of rows</param>
    private static void ApplyVerticalMerging(Table table, string labelText, int startRowIndex, int rowCount)
    {
        try
        {
            Console.WriteLine($"Applying vertical merge for {labelText} with {rowCount} rows");
            
            // Get all rows in the table
            var allTableRows = table.Elements<TableRow>().ToList();
            
            // Find the header row (row containing the label)
            int headerRowIndex = -1;
            
            for (int j = 0; j < allTableRows.Count; j++)
            {
                if (j == startRowIndex - 1 && startRowIndex > 0) // The row just before our inserted rows
                {
                    // Check if this row contains our label
                    var row = allTableRows[j];
                    var cells = row.Elements<TableCell>().ToList();
                    
                    if (cells.Count > 1)
                    {
                        var cellText = cells[1].InnerText;
                        if (cellText.Contains(labelText))
                        {
                            headerRowIndex = j;
                            Console.WriteLine($"Found {labelText} header at row {j}");
                            break;
                        }
                    }
                }
            }
            
            // If header found, apply vertical merging
            if (headerRowIndex >= 0)
            {
                Console.WriteLine($"Will merge cells from row {headerRowIndex} to {startRowIndex + rowCount - 1}");
                
                for (int j = headerRowIndex; j < startRowIndex + rowCount; j++)
                {
                    if (j >= allTableRows.Count)
                    {
                        Console.WriteLine($"Warning: Row index {j} is out of bounds");
                        continue;
                    }
                    
                    TableRow currentRow = allTableRows[j];
                    var cells = currentRow.Elements<TableCell>().ToList();
                    
                    if (cells.Count > 1)
                    {
                        TableCell secondCell = cells[1];
                        
                        // Get or create cell properties
                        var tcPr = secondCell.GetFirstChild<TableCellProperties>();
                        if (tcPr == null)
                        {
                            tcPr = new TableCellProperties();
                            secondCell.PrependChild(tcPr);
                        }
                        
                        // Remove any existing vertical merge
                        foreach (var vmerge in tcPr.Elements<VerticalMerge>().ToList())
                        {
                            vmerge.Remove();
                        }
                        
                        if (j == headerRowIndex)
                        {
                            // First cell: start the merge
                            tcPr.Append(new VerticalMerge { Val = MergedCellValues.Restart });
                            
                            // Set the text to the label
                            var paragraphs = secondCell.Elements<Paragraph>().ToList();
                            if (paragraphs.Any())
                            {
                                var firstPara = paragraphs.First();
                                var runs = firstPara.Elements<Run>().ToList();
                                if (runs.Any())
                                {
                                    var firstRun = runs.First();
                                    var texts = firstRun.Elements<Text>().ToList();
                                    if (texts.Any())
                                    {
                                        texts.First().Text = labelText;
                                    }
                                    else
                                    {
                                        firstRun.Append(new Text(labelText));
                                    }
                                }
                                else
                                {
                                    firstPara.Append(new Run(new Text(labelText)));
                                }
                            }
                            else
                            {
                                secondCell.Append(new Paragraph(new Run(new Text(labelText))));
                            }
                        }
                        else
                        {
                            // Subsequent cells: continue the merge
                            tcPr.Append(new VerticalMerge());
                            
                            // Clear content in merged cells
                            secondCell.RemoveAllChildren<Paragraph>();
                            secondCell.Append(new Paragraph());
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error applying vertical merge: {ex.Message}");
        }
    }

    /// <summary>
    /// Set the text content of a content control
    /// </summary>
    /// <param name="sdtElement">The content control</param>
    /// <param name="value">The value to set</param>
    /// <param name="isField">Whether this is a field (vs. var)</param>
    /// <param name="isMonetaryValue">Whether this value should be formatted as currency</param>
    public static void SetContentControlText(SdtElement sdtElement, string value, bool isField, bool isMonetaryValue = false)
    {
        try
        {
            if (string.IsNullOrEmpty(value))
                return;
                
            // Get alias
            var alias = sdtElement.SdtProperties?.GetFirstChild<SdtAlias>();
            string aliasValue = alias?.Val?.Value ?? "unknown";
                
            // Format monetary values with dollar sign and commas only for var controls
            // Field controls should receive raw numeric values without formatting
            if (isMonetaryValue && !isField)
            {
                if (decimal.TryParse(value, out decimal amount))
                {
                    value = amount.ToString("C");
                    Console.WriteLine($"  Formatted {aliasValue} as currency: {value}");
                }
            }

            // Get the content of the content control
            var sdtContentItems = sdtElement.Descendants<SdtContentRun>().FirstOrDefault() ??
                                 sdtElement.Descendants<SdtContentBlock>().FirstOrDefault() as OpenXmlElement;
            if (sdtContentItems == null)
            {
                Console.WriteLine($"  WARNING: Could not find content for {aliasValue}");
                return;
            }

            // Get the runs in the content control
            var runs = sdtContentItems.Descendants<Run>().ToList();
            if (!runs.Any())
            {
                // If no runs, create one
                Console.WriteLine($"  No runs found for {aliasValue}, creating new run");
                var paragraph = sdtContentItems.GetFirstChild<Paragraph>() ?? 
                               sdtContentItems.AppendChild(new Paragraph());
                
                paragraph.AppendChild(new Run(new Text(value)));
                return;
            }

            // Replace the text in the first run and remove other runs
            var firstRun = runs.First();
            var textElements = firstRun.Elements<Text>().ToList();
            
            if (textElements.Any())
            {
                var firstText = textElements.First();
                firstText.Text = value;
                Console.WriteLine($"  Set {aliasValue} to '{value}'");
                
                // Remove additional text elements
                foreach (var text in textElements.Skip(1))
                {
                    text.Remove();
                }
            }
            else
            {
                // No text elements, add one
                firstRun.AppendChild(new Text(value));
                Console.WriteLine($"  Added new text for {aliasValue}: '{value}'");
            }
            
            // Remove additional runs
            foreach (var run in runs.Skip(1))
            {
                run.Remove();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error setting content control text: {ex.Message}");
        }
    }
}