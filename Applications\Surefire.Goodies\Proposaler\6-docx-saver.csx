// 6-docx-saver.csx - Saves the document and performs final cleanup
using System;
using System.IO;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

public static class DocxSaver
{
    /// <summary>
    /// Saves the document and performs final cleanup
    /// </summary>
    /// <param name="doc">The document</param>
    public static void SaveDocument(WordprocessingDocument doc)
    {
        Console.WriteLine("Saving document...");
        
        try
        {
            // Save the main document part
            doc.MainDocumentPart.Document.Save();
            
            // Save the entire document
            doc.Save();
            
            Console.WriteLine("Document saved successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving document: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// Performs cleanup after document creation
    /// </summary>
    /// <param name="outputPath">Path to the saved document</param>
    public static void PerformCleanup(string outputPath)
    {
        Console.WriteLine("Performing final cleanup...");
        
        try
        {
            // Validate that the document exists
            if (!File.Exists(outputPath))
            {
                Console.WriteLine($"Warning: Output file not found at {outputPath}");
                return;
            }
            
            // Get file info for logging
            var fileInfo = new FileInfo(outputPath);
            Console.WriteLine($"Document size: {fileInfo.Length} bytes");
            Console.WriteLine($"Document last modified: {fileInfo.LastWriteTime}");
            
            Console.WriteLine("Cleanup completed successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during cleanup: {ex.Message}");
        }
    }
} 