using System.Collections.Generic;

/// <summary>
/// Configuration for table duplication with repeating rows
/// </summary>
public class DuplicationConfig
{
    /// <summary>
    /// The identifier field name used to locate the row to duplicate
    /// </summary>
    public string IdentifierField { get; set; }
    
    /// <summary>
    /// The primary JSON path to the array of items
    /// </summary>
    public string ArrayPath { get; set; }
    
    /// <summary>
    /// Alternative JSON paths to try if the primary path doesn't exist
    /// </summary>
    public List<string> AlternativePaths { get; set; } = new List<string>();
    
    /// <summary>
    /// The field names to look for in each item
    /// </summary>
    public List<string> Fields { get; set; }
} 