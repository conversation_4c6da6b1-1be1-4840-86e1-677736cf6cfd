# Proposler - Insurance Proposal Document Generator

## Overview
Proposler is an automated document generation system designed to streamline the creation of insurance proposals. It converts PDF documents into structured Word documents using a combination of Azure Document Intelligence for data extraction and a .NET-based document generation system.

## How It Works

### 1. PDF Input
- Users drop a PDF file onto the shortcut/batch file
- Only include relevant proposal pages
- Remove any unnecessary pages before processing

### 2. Data Extraction (docextract.py)
- Uses Azure Document Intelligence to extract text content
- Converts extracted data to JSON format
- Minifies the JSON output
- Saves to `output/temp.json`

### 3. Document Generation (0-main.csx)
- Reads the JSON data
- Processes standard fields (client info, dates, amounts)
- Handles repeating tables (limits, deductibles, endorsements)
- Applies special formatting
- Generates final Word document using template

## System Requirements

### Python Requirements
A requirements.txt file is not included as this script is in constant development, but you can use the below as the reccomended pip packages
- azure-ai-formrecognizer>=3.2.0
- python-dotenv>=0.19.0
- PyPDF2>=3.0.0
- openai>=1.0.0
- tqdm>=4.62.0
- colorama>=0.4.4 

### Python Script Parameters
The `docextract.py` script accepts the following parameters:

1. **input_path**
   - Path to the PDF file or JSON file to be processed
   - Required parameter
   - Example: `python docextract.py "path/to/proposal.pdf"`

2. **bypass_form_recognizer**
   - Whether to bypass the Azure Form Recognizer step
   - Optional parameter
   - Useful for testing with existing JSON data
   - Example: `python docextract.py "path/to/proposal.pdf" --bypass-form-recognizer`

3. **build_doc**
   - Whether to build a document using the CSX script
   - Optional parameter
   - Defaults to True
   - Set to False to only extract data without generating the Word document
   - Example: `python docextract.py "path/to/proposal.pdf" --no-build-doc`

### .NET Requirements
- .NET Core 3.1 or higher
- dotnet-script tool installed globally

## Template Fields

### Standard Fields (Non-Repeating)
🔴 = Non-editable  
🟢 = User-editable

#### Client Information
- 🟢 `varClientName` - Client's business name
- 🟢 `varClientAddress1` - Primary address line
- 🟢 `varClientAddress2` - Secondary address line
- 🟢 `varClientAddress3` - Additional address line
- 🟢 `varCarrierName` - Insurance carrier name

#### Policy Information
- 🟢 `varPolicyNumber` - Policy number
- 🟢 `varRenewalOfPolicyNumber` - Previous policy number for renewals
- 🟢 `varEffectiveDate` - Policy effective date
- 🟢 `varExpirationDate` - Policy expiration date
- 🟢 `varTodaysDate` - Document generation date
- 🟢 `varQuoteValidUntil` - Quote validity date

#### Financial Information
- 🟢 `varPurePremiumDollarAmount` - Pure premium amount
- 🟢 `varTotalPurePremium` - Total pure premium
- 🟢 `varMetroRetailBrokerFee` - Broker fee
- 🔴 `varTotalTaxesAndFeesDollarAmount` - Total taxes and fees
- 🔴 `varTotalPolicyCost` - Total policy cost

### Field Calculations
The following calculations are performed automatically:

1. **Total Pure Premium**
   - Set equal to `PurePremiumDollarAmount`
   - Example: If `PurePremiumDollarAmount` is $10,234.00, then 🔴 `varTotalPurePremium` = $10,234.00

2. **Total Taxes and Fees**
   - Sum of all `TaxFeeItemDollarAmount` values plus 🟢 `MetroRetailBrokerFee`
   - Example:
     ```
     TaxFeeItems:
     - Broker Fee: $450.00
     - Policy Fee: $100.00
     - Surplus Lines Tax: $310.02
     - Stamping Office Fee: $18.60
     - Administrative Fee: $100.00
     TotalTaxesAndFeesDollarAmount = $450.00 + $100.00 + $310.02 + $18.60 + $100.00 = $978.62
     ```

3. **Total Policy Cost**
   - Sum of 🔴 `TotalPurePremium` and 🔴 `TotalTaxesAndFeesDollarAmount`
   - Example: $10,234.00 + $978.62 = $11,212.62

4. **Minimum Earned Amount**
   - Calculated as `MinimumEarnedPercentage` of `PurePremiumDollarAmount`
   - Default percentage is 25% if not specified
   - Example: 25% of $10,234.00 = $2,558.50

5. **Total Deposit**
   - Sum of `MinimumEarnedAmount` and `TotalTaxesAndFeesDollarAmount`
   - Example: $2,558.50 + $978.62 = $3,537.12

### Repeating Tables

#### Limits Table
- 🟢 `varLimitName` - Coverage limit name
- 🟢 `varLimitDollarAmount` - Coverage limit amount

#### Deductibles Table
- 🟢 `varDeductibleName` - Deductible name
- 🟢 `varDeductibleDollarAmount` - Deductible amount

#### Rate Class Table
- 🟢 `varLocationNumber` - Location identifier
- 🟢 `varRateClassCode` - Class code
- 🟢 `varRateDescription` - Class description
- 🟢 `varRateBasis` - Rate basis
- 🟢 `varRateExposure` - Exposure amount
- 🟢 `varNetRate` - Net rate
- 🟢 `varRatePremium` - Premium amount

#### Tax and Fee Table
- 🟢 `fieldTaxFeeItemName` - Tax/fee description
- 🟢 `fieldTaxFeeItemDollarAmount` - Tax/fee amount

#### Endorsements Table
- 🟢 `varEndorsementNumber` - Endorsement code
- 🟢 `varEndorsementName` - Endorsement description

## Development Notes

### Special Handlers
- TaxFeeItem tables use a special handler due to 2-column structure
- Vertical cell merging is implemented for limits and deductibles tables
- Currency formatting is applied only to var controls (not field controls)

### Alternative Paths
The system checks multiple JSON paths for certain data:

TaxFeeItems can be found in:
```
Financials.TaxFeeItems
Financials.TaxFeeItems.TaxFeeItem
Financials.TaxAndSurcharges
Financials.TaxAndFees
TaxAndFees.TaxFee
```

### Debug Flags
[Add any debug flags or environment variables that control program behavior]

## Template Maintenance
1. Do not modify fields marked with 🔴
2. Fields marked with 🟢 can be edited for formatting
3. Maintain table structures and content control names
4. Keep cell merging and formatting consistent

## Known Limitations
1. Address fields must exist in template
2. Table structures must match expected format
3. Content control names must match exactly

## Troubleshooting
1. Check `output/temp.json` for extracted data
2. Verify PDF contains required information
3. Ensure template contains all required fields
4. Check console output for processing errors

## Support
[Add support contact information]