#r "nuget: DocumentFormat.OpenXml, 2.13.0"
#r "nuget: Newtonsoft.Json, 13.0.1"

using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;

// ------------------------------
// Duplication configuration structure.
public class DuplicationConfig
{
    public string IdentifierField { get; set; }
    public string ArrayPath { get; set; }
    public List<string> Fields { get; set; }
    public List<string> AlternativePaths { get; set; } = new List<string>();
}

// ------------------------------
// Utility functions.
public static class Utils
{
    // Recursively flattens a JSON structure.
    public static Dictionary<string, JToken> FlattenJson(JToken token, string prefix = "")
    {
        var result = new Dictionary<string, JToken>();
        if (token.Type == JTokenType.Object)
        {
            foreach (var prop in token.Children<JProperty>())
            {
                string newPrefix = string.IsNullOrEmpty(prefix) ? prop.Name : prefix + "." + prop.Name;
                foreach (var child in FlattenJson(prop.Value, newPrefix))
                {
                    result[child.Key] = child.Value;
                }
            }
        }
        else if (token.Type == JTokenType.Array)
        {
            int index = 0;
            foreach (var item in token.Children())
            {
                string newPrefix = prefix + $"[{index}]";
                foreach (var child in FlattenJson(item, newPrefix))
                {
                    result[child.Key] = child.Value;
                }
                index++;
            }
        }
        else
        {
            result[prefix] = token;
        }
        return result;
    }

    // Gets a JSON token using a dot-notated path.
    public static JToken GetJsonValue(JObject data, string path)
    {
        try
        {
            var token = data.SelectToken(path);
            return token;
        }
        catch (Exception)
        {
            return null;
        }
    }

    // Gets a JSON value as a string, handling null values
    public static string GetJsonValueAsString(JObject data, string path)
    {
        var token = GetJsonValue(data, path);
        return token?.ToString();
    }

    // Formats a monetary value without dollar sign (for field prefix controls)
    public static string FormatAmountWithoutDollarSign(string amount)
    {
        Console.WriteLine($"FormatAmountWithoutDollarSign called with: '{amount}'");
        
        if (string.IsNullOrWhiteSpace(amount))
        {
            Console.WriteLine("  Amount is null or empty, returning empty string");
            return "";
        }
        
        // Remove any existing dollar signs and trim whitespace
        string cleanAmount = amount.Trim();
        if (cleanAmount.StartsWith("$"))
        {
            Console.WriteLine("  Removing dollar sign");
            cleanAmount = cleanAmount.Substring(1).Trim();
        }
        
        // Try to parse as decimal and format with two decimal places
        if (decimal.TryParse(cleanAmount, out decimal parsedAmount))
        {
            string formatted = parsedAmount.ToString("0.00");
            Console.WriteLine($"  Parsed as decimal and formatted to: '{formatted}'");
            return formatted;
        }
        
        Console.WriteLine($"  Could not parse as decimal, returning cleaned amount: '{cleanAmount}'");
        return cleanAmount;
    }
    
    // Formats a monetary value with dollar sign (for var prefix controls)
    public static string FormatAmountWithDollarSign(string amount)
    {
        // Remove any existing dollar signs
        amount = amount.Trim().TrimStart('$');
        
        // Try to parse as decimal and format with two decimal places and dollar sign
        if (decimal.TryParse(amount, out decimal parsedAmount))
        {
            return $"${parsedAmount:N2}";
        }
        
        // If not parseable but not empty, add dollar sign
        if (!string.IsNullOrEmpty(amount))
        {
            return "$" + amount;
        }
        
        return amount;
    }

    // Try multiple possible paths to get a JSON array
    public static JArray TryGetJsonArray(JObject data, string primaryPath, string fallbackPath)
    {
        Console.WriteLine($"Trying to get JSON array from primary path: {primaryPath} or fallback path: {fallbackPath}");
        
        var token = GetJsonValue(data, primaryPath);
        if (token != null && token.Type == JTokenType.Array)
        {
            Console.WriteLine($"Found array at primary path: {primaryPath}");
            return (JArray)token;
        }
        
        Console.WriteLine($"No array found at primary path: {primaryPath}, trying fallback: {fallbackPath}");
        token = GetJsonValue(data, fallbackPath);
        if (token != null && token.Type == JTokenType.Array)
        {
            Console.WriteLine($"Found array at fallback path: {fallbackPath}");
            return (JArray)token;
        }
        
        // Special case for TaxFeeItems - if we failed to find it at the normal paths,
        // try additional approaches
        if (primaryPath.Contains("TaxFeeItems") || fallbackPath.Contains("TaxFeeItems"))
        {
            Console.WriteLine("Trying special handling for TaxFeeItems using recursive search...");
            return FindTaxFeeItems(data);
        }
        
        Console.WriteLine($"Could not find array at either path: {primaryPath} or {fallbackPath}");
        return null;
    }

    // Format date strings to remove leading zeros from months
    public static string FormatDateString(string dateStr)
    {
        if (string.IsNullOrEmpty(dateStr))
            return dateStr;
            
        // Try to parse the date string
        if (DateTime.TryParse(dateStr, out DateTime date))
        {
            // Format with NO leading zeros for month (M instead of MM)
            return date.ToString("M/dd/yyyy");
        }
        
        return dateStr; // Return original if parsing fails
    }

    // Sets all text within the given content control element.
    public static void SetContentControlText(OpenXmlElement sdtElement, string text, bool isFieldPrefix = false, bool isMonetaryValue = false)
	{
	    string originalText = text; // Store original text for logging
	    
	    // For fieldTaxFeeItemDollarAmount, always handle specially to ensure no dollar sign
	    if (sdtElement is SdtElement sdtEl)
	    {
	        var alias = sdtEl.SdtProperties?.GetFirstChild<SdtAlias>();
	        if (alias != null && alias.Val.Value == "fieldTaxFeeItemDollarAmount")
	        {
	            // Always strip dollar sign for fieldTaxFeeItemDollarAmount
	            Console.WriteLine($"SPECIAL HANDLER for fieldTaxFeeItemDollarAmount: '{text}'");
	            isFieldPrefix = true;
	            isMonetaryValue = true;
	            
	            // First, strip any existing dollar sign
	            if (!string.IsNullOrWhiteSpace(text) && text.Contains("$"))
	            {
	                Console.WriteLine($"  - Found dollar sign in fieldTaxFeeItemDollarAmount value");
	                text = text.Replace("$", "").Trim();
	            }
	            
	            // Format as a decimal if possible
	            if (decimal.TryParse(text, out decimal amount))
	            {
	                text = amount.ToString("0.00");
	                Console.WriteLine($"  - Formatted to: '{text}'");
	            }
	            
	            // Set the content control text directly without additional formatting to avoid double dollar signs
	            var sdtContent = sdtEl.GetFirstChild<SdtContentBlock>();
	            if (sdtContent != null)
	            {
	                Console.WriteLine($"  - Setting content directly via SdtContentBlock");
	                sdtContent.RemoveAllChildren();
	                sdtContent.AppendChild(new Run(new Text(text)));
	                
	                // Print modified console output with ACTUAL value
	                if (originalText != text)
	                {
	                    Console.WriteLine($"  - ACTUAL value set (no dollar sign): '{text}'");
	                }
	                return; // Skip the rest of the processing
	            }
	        }
	    }
	    
	    // Process monetary values
	    if (isMonetaryValue)
	    {
	        if (isFieldPrefix)
	        {
	            // For "field" prefix controls, format without dollar sign
	            string preFormatted = text;
	            text = FormatAmountWithoutDollarSign(text);
	            
	            // Print additional info for debugging
	            if (preFormatted != text)
	            {
	                Console.WriteLine($"Formatted monetary value without dollar sign: '{preFormatted}' -> '{text}'");
	            }
	        }
	        else
	        {
	            // For "var" prefix controls, format with dollar sign
	            string preFormatted = text;
	            text = FormatAmountWithDollarSign(text);
	            
	            // Print additional info for debugging
	            if (preFormatted != text)
	            {
	                Console.WriteLine($"Formatted monetary value with dollar sign: '{preFormatted}' -> '{text}'");
	            }
	        }
	    }
	
		var textElements = sdtElement.Descendants<Text>().ToList();
		if (textElements.Any())
		{
			// Set the text in the first text element
			textElements.First().Text = text;
			// Remove any additional text elements to avoid duplication.
			foreach (var extra in textElements.Skip(1).ToList())
			{
				extra.Parent.Remove();
			}
		}
		else
		{
			var sdtContent = sdtElement.Descendants<SdtContentBlock>().FirstOrDefault();
			if (sdtContent != null)
			{
				// Clear existing content and add a single run with the text.
				sdtContent.RemoveAllChildren();
				sdtContent.AppendChild(new Run(new Text(text)));
			}
		}
	}

    // Performs final tweaks to the document content
    public static void FinalTweaks(WordprocessingDocument doc, Dictionary<string, string> fieldValues, JObject data)
    {
        var controls = doc.MainDocumentPart.Document.Descendants<SdtElement>().ToList();
        decimal totalTaxesAndFees = 0;
        decimal brokerFee = 0;
        decimal purePremium = 0;
        decimal minimumEarnedPercentage = 25.0M; // Default to 25% if not specified

        // First pass - collect values from editable fields
        foreach (var ctrl in controls)
        {
            var alias = ctrl.SdtProperties?.GetFirstChild<SdtAlias>();
            if (alias?.Val?.Value == null) continue;
            
            string title = alias.Val.Value;

            // Get pure premium amount from the editable field
            if (title == "fieldPurePremiumDollarAmount")
            {
                // First try to get the value from the JSON data
                var jsonValue = Utils.GetJsonValue((JObject)data, "Financials.PurePremiumDollarAmount");
                if (jsonValue != null)
                {
                    string amountStr = jsonValue.ToString().TrimStart('$');
                    if (decimal.TryParse(amountStr, out decimal amount))
                    {
                        purePremium = amount;
                        Console.WriteLine($"Set pure premium amount from JSON: ${purePremium:F2}");
                        // Set the value in the editable field
                        SetContentControlText(ctrl, amountStr, true, true);
                    }
                }
                // If JSON value not found, try to read from the field
                else
                {
                    var text = ctrl.Descendants<Text>().FirstOrDefault();
                    if (text != null && !string.IsNullOrWhiteSpace(text.Text))
                    {
                        if (decimal.TryParse(text.Text.TrimStart('$'), out decimal amount))
                        {
                            purePremium = amount;
                            Console.WriteLine($"Found pure premium amount in field: ${purePremium:F2}");
                        }
                    }
                }
            }

            // Get tax/fee amounts from all editable fields
            if (title == "fieldTaxFeeItemDollarAmount")
            {
                var text = ctrl.Descendants<Text>().FirstOrDefault();
                if (text != null && !string.IsNullOrWhiteSpace(text.Text))
                {
                    if (decimal.TryParse(text.Text.TrimStart('$'), out decimal amount))
                    {
                        totalTaxesAndFees += amount;
                        Console.WriteLine($"Added tax/fee amount: ${amount:F2}");
                    }
                }
            }

            // Get broker fee from the editable field
            if (title == "fieldMetroRetailBrokerFee")
            {
                var text = ctrl.Descendants<Text>().FirstOrDefault();
                if (text != null && !string.IsNullOrWhiteSpace(text.Text))
                {
                    if (decimal.TryParse(text.Text.TrimStart('$'), out decimal amount))
                    {
                        brokerFee = amount;
                        Console.WriteLine($"Found broker fee: ${brokerFee:F2}");
                    }
                }
                else
                {
                    // Default broker fee if not specified
                    brokerFee = 400.00M;
                    Console.WriteLine($"Using default broker fee: ${brokerFee:F2}");
                }
            }

            // Get minimum earned percentage from the editable field
            if (title == "fieldMinimumEarnedPercentage")
            {
                var text = ctrl.Descendants<Text>().FirstOrDefault();
                if (text != null && !string.IsNullOrWhiteSpace(text.Text))
                {
                    string percentText = text.Text.TrimEnd('%');
                    if (decimal.TryParse(percentText, out decimal percentage))
                    {
                        minimumEarnedPercentage = percentage;
                        Console.WriteLine($"Found minimum earned percentage: {minimumEarnedPercentage}%");
                    }
                }
            }
        }

        // Calculate final values
        totalTaxesAndFees += brokerFee; // Add broker fee to total taxes and fees
        decimal totalPolicyCost = purePremium + totalTaxesAndFees;
        
        // Calculate minimum earned amount based on percentage of pure premium
        decimal minimumEarned = purePremium * (minimumEarnedPercentage / 100.0M);
        Console.WriteLine($"Calculated minimum earned amount: ${minimumEarned:F2} ({minimumEarnedPercentage}% of ${purePremium:F2})");
        
        // Calculate total deposit (minimum earned premium plus all taxes/fees)
        decimal totalDeposit = minimumEarned + totalTaxesAndFees;
        Console.WriteLine($"Calculated total deposit: ${totalDeposit:F2} (${minimumEarned:F2} + ${totalTaxesAndFees:F2})");

        // Second pass - update computed values
        foreach (var ctrl in controls)
        {
            var alias = ctrl.SdtProperties?.GetFirstChild<SdtAlias>();
            if (alias?.Val?.Value == null) continue;
            
            string title = alias.Val.Value;
            if (!title.StartsWith("var")) continue;

            // Handle retroactive date
            if (title == "varCoverageRetroActiveDate")
            {
                var text = ctrl.Descendants<Text>().FirstOrDefault();
                if (text != null && string.IsNullOrWhiteSpace(text.Text))
                {
                    SetContentControlText(ctrl, "N/A");
                    Console.WriteLine("Set varCoverageRetroActiveDate to N/A (empty value)");
                }
            }

            // Replace "Included" with "Incl."
            if (title == "varRatePremium" || title == "varNetRate" || 
                title == "varRateExposure" || title == "varRateBasis")
            {
                var text = ctrl.Descendants<Text>().FirstOrDefault();
                if (text != null && text.Text == "Included")
                {
                    SetContentControlText(ctrl, "Incl.");
                    Console.WriteLine($"Replaced 'Included' with 'Incl.' in {title}");
                }
            }

            // Set total pure premium
            if (title == "varTotalPurePremium")
            {
                // Remove the $ sign from the value
                string value = $"{purePremium:F2}";
                SetContentControlText(ctrl, value, true, true);
                Console.WriteLine($"Set varTotalPurePremium to {value}");
            }

            // Set total taxes and fees
            if (title == "varTotalTaxesAndFeesDollarAmount")
            {
                // Remove the $ sign from the value
                string value = $"{totalTaxesAndFees:F2}";
                SetContentControlText(ctrl, value, true, true);
                Console.WriteLine($"Set varTotalTaxesAndFeesDollarAmount to {value}");
            }

            // Set total policy cost
            if (title == "varTotalPolicyCost")
            {
                // Remove the $ sign from the value
                string value = $"{totalPolicyCost:F2}";
                SetContentControlText(ctrl, value, true, true);
                Console.WriteLine($"Set varTotalPolicyCost to {value}");
            }
            
            // Set minimum earned percentage
            if (title == "varMinimumEarnedPercentage")
            {
                // Remove the % sign from the value
                string value = $"{minimumEarnedPercentage:F0}";
                SetContentControlText(ctrl, value);
                Console.WriteLine($"Set varMinimumEarnedPercentage to {value}");
            }

            // Set minimum earned dollar amount
            if (title == "varMinimumEarnedDollarAmount")
            {
                // Remove the $ sign from the value
                string value = $"{minimumEarned:F2}";
                SetContentControlText(ctrl, value, true, true);
                Console.WriteLine($"Set varMinimumEarnedDollarAmount to {value}");
            }

            // Set total deposit
            if (title == "varTotalDepositDollarAmount")
            {
                // Remove the $ sign from the value
                string value = $"{totalDeposit:F2}";
                SetContentControlText(ctrl, value, true, true);
                Console.WriteLine($"Set varTotalDepositDollarAmount to {value}");
            }
            
            // Set broker fee value
            if (title == "varMetroRetailBrokerFee")
            {
                // Remove the $ sign from the value
                string value = $"{brokerFee:F2}";
                SetContentControlText(ctrl, value, true, true);
                Console.WriteLine($"Set varMetroRetailBrokerFee to {value}");
            }

            // Handle date formatting for effective and expiration dates
            if (title == "varEffectiveDate" || title == "varExpirationDate")
            {
                var text = ctrl.Descendants<Text>().FirstOrDefault();
                if (text != null && !string.IsNullOrWhiteSpace(text.Text))
                {
                    if (DateTime.TryParse(text.Text, out DateTime date))
                    {
                        // Format date without leading zeros for month and day
                        string formattedDate = date.ToString("M/d/yyyy");
                        SetContentControlText(ctrl, formattedDate);
                        Console.WriteLine($"Formatted {title} from {text.Text} to {formattedDate}");
                    }
                }
            }
        }

        // Process payment links
        Console.WriteLine("\nProcessing payment links...");
        var hyperlinks = doc.MainDocumentPart.Document.Descendants<DocumentFormat.OpenXml.Wordprocessing.Hyperlink>().ToList();
        Console.WriteLine($"Found {hyperlinks.Count} hyperlinks in document");

        // First try: direct hyperlink text search
        foreach (var hyperlink in hyperlinks)
        {
            try
            {
                Console.WriteLine("\nAnalyzing hyperlink:");
                var texts = hyperlink.Descendants<Text>().ToList();
                Console.WriteLine($"- Found {texts.Count} text elements in hyperlink");
                
                // Log all text elements for debugging
                foreach (var text in texts)
                {
                    Console.WriteLine($"- Link text: '{text.Text}'");
                }

                // Get the current URL
                var relationshipId = hyperlink.Id?.Value;
                string currentUrl = "";
                if (!string.IsNullOrEmpty(relationshipId))
                {
                    var relationship = doc.MainDocumentPart.HyperlinkRelationships
                        .FirstOrDefault(r => r.Id == relationshipId);
                    if (relationship != null)
                    {
                        currentUrl = relationship.Uri.ToString();
                        Console.WriteLine($"Current URL: {currentUrl}");
                    }
                }

                // Check for placeholder URLs in the text
                var linkTextElement = hyperlink.Descendants<Text>().FirstOrDefault();
                if (linkTextElement == null)
                {
                    Console.WriteLine("No text element found in hyperlink");
                    continue;
                }

                string linkText = linkTextElement.Text;
                Console.WriteLine($"Processing link text: {linkText}");
                string newUrl = "";

                // Check which placeholder URL we're dealing with
                if (currentUrl.Contains("varsurefiredownpaymentlink", StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine("Found down payment link placeholder");
                    // Build the down payment link
                    string clientName = fieldValues.GetValueOrDefault("ClientName", "");
                    string depositAmount = totalDeposit.ToString("F2");
                    string primaryCoverage = fieldValues.GetValueOrDefault("PrimaryCoverage", "");

                    // Sanitize client name and primary coverage - remove all non-alphanumeric characters except spaces
                    clientName = System.Text.RegularExpressions.Regex.Replace(clientName, @"[^a-zA-Z0-9\s]", "").Trim();
                    primaryCoverage = System.Text.RegularExpressions.Regex.Replace(primaryCoverage, @"[^a-zA-Z0-9\s]", "").Trim();

                    newUrl = $"https://metroinsurance.epaypolicy.com/?payer={Uri.EscapeDataString(clientName)}&emailAddress=&amount={depositAmount}&comments={Uri.EscapeDataString(primaryCoverage)}%20Policy%20Proposal%20Down%20Payment";
                    Console.WriteLine($"Created down payment link: {newUrl}");
                }
                else if (currentUrl.Contains("varsurefirefullpaymentlink", StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine("Found full payment link placeholder");
                    // Build the full payment link
                    string clientName = fieldValues.GetValueOrDefault("ClientName", "");
                    string totalAmount = totalPolicyCost.ToString("F2");
                    string primaryCoverage = fieldValues.GetValueOrDefault("PrimaryCoverage", "");

                    // Sanitize client name and primary coverage - remove all non-alphanumeric characters except spaces
                    clientName = System.Text.RegularExpressions.Regex.Replace(clientName, @"[^a-zA-Z0-9\s]", "").Trim();
                    primaryCoverage = System.Text.RegularExpressions.Regex.Replace(primaryCoverage, @"[^a-zA-Z0-9\s]", "").Trim();

                    newUrl = $"https://metroinsurance.epaypolicy.com/?payer={Uri.EscapeDataString(clientName)}&emailAddress=&amount={totalAmount}&comments={Uri.EscapeDataString(primaryCoverage)}%20Policy%20Proposal%20Full%20Payment";
                    Console.WriteLine($"Created full payment link: {newUrl}");
                }

                // If we have a new URL, update the hyperlink
                if (!string.IsNullOrEmpty(newUrl))
                {
                    try
                    {
                        // Remove any existing relationship
                        if (!string.IsNullOrEmpty(relationshipId))
                        {
                            var existingRel = doc.MainDocumentPart.HyperlinkRelationships
                                .FirstOrDefault(r => r.Id == relationshipId);
                            if (existingRel != null)
                            {
                                doc.MainDocumentPart.DeleteReferenceRelationship(existingRel);
                                Console.WriteLine("Deleted existing hyperlink relationship");
                            }
                        }

                        // Create new relationship
                        var newRelationship = doc.MainDocumentPart.AddHyperlinkRelationship(
                            new Uri(newUrl, UriKind.Absolute),
                            true);
                        hyperlink.Id = newRelationship.Id;
                        Console.WriteLine($"Created new hyperlink relationship with ID: {newRelationship.Id}");

                        // Create a new run with the original text and formatting
                        var run = new Run(new Text(linkText));
                        
                        // Add formatting properties
                        var runProperties = new RunProperties(
                            new Bold(),
                            new Underline() { Val = UnderlineValues.Single },
                            new Color() { Val = "0000FF" }  // RGB color for blue (00-00-FF)
                        );
                        run.PrependChild(runProperties);

                        // Update the hyperlink
                        hyperlink.RemoveAllChildren();
                        hyperlink.Append(run);
                        Console.WriteLine("Updated hyperlink text and structure with formatting");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error updating hyperlink relationship: {ex.Message}");
                        Console.WriteLine(ex.StackTrace);
                    }
                }
                else
                {
                    Console.WriteLine("No new URL generated for this hyperlink");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing hyperlink: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
        }

        // Force save the document part
        doc.MainDocumentPart.Document.Save();
        Console.WriteLine("Saved document after updating hyperlinks");
    }

    // Merges additional endorsements from forms.json into the main JSON data
    public static void MergeAdditionalEndorsements(JObject mainData)
    {
        try
        {
            string formsJsonPath = Path.Combine(Directory.GetCurrentDirectory(), "forms.json");
            if (!File.Exists(formsJsonPath))
            {
                Console.WriteLine("forms.json not found, skipping additional endorsements");
                return;
            }

            string formsJsonContent = File.ReadAllText(formsJsonPath);
            var formsData = JObject.Parse(formsJsonContent);

            // Get the additional endorsements array
            var additionalEndorsements = formsData["AdditionalEndorsements"];
            if (additionalEndorsements == null || !additionalEndorsements.HasValues)
            {
                Console.WriteLine("No additional endorsements found in forms.json");
                return;
            }

            // Get or create the endorsements section in the main data
            JObject insurancePolicy = mainData["InsurancePolicy"] as JObject ?? mainData;
            JObject endorsementsSection;
            JArray endorsementsArray;

            bool hasNestedStructure = insurancePolicy["Endorsements"]?["Endorsement"] != null;

            if (hasNestedStructure)
            {
                endorsementsSection = insurancePolicy["Endorsements"] as JObject ?? new JObject();
                endorsementsArray = endorsementsSection["Endorsement"] as JArray ?? new JArray();
            }
            else
            {
                endorsementsSection = insurancePolicy["Endorsements"] as JObject ?? new JObject();
                endorsementsArray = insurancePolicy["Endorsements"] as JArray ?? new JArray();
            }

            // Create a HashSet to track existing endorsement numbers
            var existingEndorsementNumbers = new HashSet<string>();
            foreach (var endorsement in endorsementsArray)
            {
                string endorsementNumber = endorsement["EndorsementNumber"]?.ToString();
                if (!string.IsNullOrEmpty(endorsementNumber))
                {
                    existingEndorsementNumbers.Add(endorsementNumber);
                }
            }

            // Add new endorsements that don't already exist
            foreach (var endorsement in additionalEndorsements)
            {
                string endorsementNumber = endorsement["EndorsementNumber"]?.ToString();
                if (!string.IsNullOrEmpty(endorsementNumber) && !existingEndorsementNumbers.Contains(endorsementNumber))
                {
                    endorsementsArray.Add(new JObject
                    {
                        ["EndorsementNumber"] = endorsementNumber,
                        ["EndorsementName"] = endorsement["EndorsementName"]
                    });
                    existingEndorsementNumbers.Add(endorsementNumber);
                    Console.WriteLine($"Added endorsement {endorsementNumber}: {endorsement["EndorsementName"]}");
                }
            }

            // Update the endorsements in the main data
            if (hasNestedStructure)
            {
                endorsementsSection["Endorsement"] = endorsementsArray;
                insurancePolicy["Endorsements"] = endorsementsSection;
            }
            else
            {
                insurancePolicy["Endorsements"] = endorsementsArray;
            }

            if (mainData != insurancePolicy)
            {
                mainData["InsurancePolicy"] = insurancePolicy;
            }

            Console.WriteLine($"Successfully merged {existingEndorsementNumbers.Count} total endorsements");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error merging additional endorsements: {ex.Message}");
        }
    }

    // Add this method to the Utils class
    public static void SafeCopyFile(string source, string destination)
    {
        Console.WriteLine($"Safely copying file {source} to {destination}");
        
        // Create a temporary file name in the same directory
        string tempFile = Path.Combine(
            Path.GetDirectoryName(destination),
            Path.GetRandomFileName() + ".tmp");
        
        try
        {
            // First copy to the temp file
            File.Copy(source, tempFile, true);
            Console.WriteLine($"Copied to temporary file: {tempFile}");
            
            // Then try to replace the destination
            try
            {
                if (File.Exists(destination))
                {
                    File.Delete(destination);
                    Console.WriteLine($"Deleted existing destination file");
                }
                File.Move(tempFile, destination);
                Console.WriteLine($"Moved temporary file to destination");
            }
            catch (Exception ex)
            {
                // If we failed, try one more time with a delay
                Console.WriteLine($"First attempt failed: {ex.Message}. Retrying after delay...");
                System.Threading.Thread.Sleep(1000);
                if (File.Exists(destination))
                {
                    File.Delete(destination);
                    Console.WriteLine($"Deleted existing destination file on second attempt");
                }
                File.Move(tempFile, destination);
                Console.WriteLine($"Moved temporary file to destination on second attempt");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during safe file copy: {ex.Message}");
            
            // Clean up temp file if it exists
            if (File.Exists(tempFile))
            {
                try
                {
                    File.Delete(tempFile);
                    Console.WriteLine($"Cleaned up temporary file after error");
                }
                catch
                {
                    // Ignore errors in cleanup
                }
            }
            
            throw; // Re-throw the exception
        }
    }

    // Format text with proper capitalization (Title Case)
    public static string ProperCapitalize(string text)
    {
        if (string.IsNullOrEmpty(text))
            return text;
        
        // If text is all uppercase, convert to title case
        if (text == text.ToUpper())
        {
            // Convert to lowercase first
            text = text.ToLower();
            
            // Split by spaces and capitalize first letter of each word
            return System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(text);
        }
        
        // If text is not all uppercase, assume it's already properly formatted
        return text;
    }

    // Function to recursively search for TaxFeeItems
    public static JArray FindTaxFeeItems(JObject data)
    {
        var result = new JArray();
        
        Console.WriteLine("Recursively searching for TaxFeeItems...");
        
        // Try some common direct paths first
        string[] commonPaths = {
            "InsurancePolicy.Financials.TaxFeeItems",
            "Financials.TaxFeeItems",
            "InsurancePolicy.Financials.TaxFeeItems.TaxFeeItem",
            "Financials.TaxFeeItems.TaxFeeItem"
        };
        
        foreach (string path in commonPaths)
        {
            var token = GetJsonValue(data, path);
            if (token != null)
            {
                Console.WriteLine($"Found token at path: {path}, Type: {token.Type}");
                
                if (token.Type == JTokenType.Array)
                {
                    Console.WriteLine($"Found array of {token.Count()} items at {path}");
                    
                    // If it's directly a TaxFeeItems array, use it
                    foreach (var item in token)
                    {
                        if (item is JObject obj)
                        {
                            // Check if this looks like a tax/fee item
                            if (obj["TaxFeeItemName"] != null || obj["TaxFeeItemDollarAmount"] != null || 
                                obj["Name"] != null || obj["Amount"] != null)
                            {
                                result.Add(item);
                                Console.WriteLine($"Added tax/fee item: {item}");
                            }
                        }
                    }
                    
                    if (result.Count > 0)
                    {
                        Console.WriteLine($"Successfully found {result.Count} tax/fee items");
                        return result;
                    }
                }
                else if (token.Type == JTokenType.Object)
                {
                    var taxFeeItem = token["TaxFeeItem"];
                    if (taxFeeItem != null && taxFeeItem.Type == JTokenType.Array)
                    {
                        Console.WriteLine($"Found TaxFeeItem array within object");
                        return (JArray)taxFeeItem;
                    }
                }
            }
        }
        
        // If we still haven't found anything, try to search recursively
        // for individual tax/fee items
        SearchForTaxFeeItemsRecursive(data, result);
        
        if (result.Count > 0)
        {
            Console.WriteLine($"Found {result.Count} tax/fee items through recursive search");
            return result;
        }
        
        Console.WriteLine("Could not find any TaxFeeItems");
        return result; // Return empty array if nothing found
    }
    
    private static void SearchForTaxFeeItemsRecursive(JToken token, JArray result, string path = "")
    {
        if (token == null)
            return;
            
        if (token.Type == JTokenType.Object)
        {
            var obj = (JObject)token;
            
            // Check if this object looks like a tax/fee item
            if ((obj["TaxFeeItemName"] != null || obj["Name"] != null) && 
                (obj["TaxFeeItemDollarAmount"] != null || obj["Amount"] != null))
            {
                // Create a standardized TaxFeeItem object
                var standardItem = new JObject();
                standardItem["TaxFeeItemName"] = obj["TaxFeeItemName"] ?? obj["Name"];
                standardItem["TaxFeeItemDollarAmount"] = obj["TaxFeeItemDollarAmount"] ?? obj["Amount"];
                
                Console.WriteLine($"Found TaxFeeItem at path {path}: {standardItem["TaxFeeItemName"]} = {standardItem["TaxFeeItemDollarAmount"]}");
                result.Add(standardItem);
                return; // Don't recurse further if we found a match
            }
            
            // Continue recursion for each property
            foreach (var prop in obj.Properties())
            {
                SearchForTaxFeeItemsRecursive(prop.Value, result, path + "." + prop.Name);
            }
        }
        else if (token.Type == JTokenType.Array)
        {
            // Check each array item
            for (int i = 0; i < token.Count(); i++)
            {
                SearchForTaxFeeItemsRecursive(token[i], result, path + $"[{i}]");
            }
        }
    }

    // Find endorsements in Form Recognizer output
    public static void ExtractEndorsementsFromFormRecognizer(JObject jsonData)
    {
        try
        {
            Console.WriteLine("Extracting additional endorsements from Form Recognizer output...");
            
            // Find the most recent JSON file in output directory (not temp.json)
            string baseDir = Directory.GetCurrentDirectory();
            string outputDir = Path.Combine(baseDir, "output");
            
            if (!Directory.Exists(outputDir))
            {
                Console.WriteLine("Output directory not found, skipping form recognizer extraction");
                return;
            }
            
            var jsonFiles = Directory.GetFiles(outputDir, "*.json")
                .Where(f => !Path.GetFileName(f).Equals("temp.json", StringComparison.OrdinalIgnoreCase))
                .OrderByDescending(f => new FileInfo(f).LastWriteTime)
                .ToList();
                
            if (jsonFiles.Count == 0)
            {
                Console.WriteLine("No form recognizer JSON files found in output directory");
                return;
            }
            
            string formRecognizerJson = jsonFiles.First();
            Console.WriteLine($"Using most recent Form Recognizer JSON: {formRecognizerJson}");
            
            // Parse the Form Recognizer JSON
            string jsonContent = File.ReadAllText(formRecognizerJson);
            JObject formData = JObject.Parse(jsonContent);
            
            // Create patterns to match endorsement form numbers and names
            // Updated pattern to catch more form number formats like "MPIL 1083 04 15"
            var formNumberPattern = new Regex(@"([A-Z]{2,6}\s?\d{2,4}\s?\d{2}\s?\d{2})|([A-Z]{2,6}[- ]?\d{4}[- ]?\d{2}[- ]?\d{2})");
            
            // Create a list to store found endorsements
            var foundEndorsements = new List<JObject>();
            
            // First, try to extract from tables
            var tables = formData["analyzeResult"]?["tables"];
            if (tables != null && tables.Type == JTokenType.Array)
            {
                Console.WriteLine($"Found {tables.Count()} tables in Form Recognizer output");
                
                foreach (var table in tables)
                {
                    string tableId = table["id"]?.ToString() ?? "unknown";
                    Console.WriteLine($"Processing table ID: {tableId}");
                    
                    // Special handling for table ID 4 which contains endorsements
                    bool isEndorsementTable = (tableId == "4");
                    if (isEndorsementTable)
                    {
                        Console.WriteLine("Found endorsement table (ID 4) - using special handling");
                    }
                    
                    var cells = table["cells"];
                    if (cells != null && cells.Type == JTokenType.Array)
                    {
                        // Group cells by row to analyze them together
                        var rowGroups = cells.GroupBy(cell => (int)cell["rowIndex"]);
                        
                        foreach (var rowGroup in rowGroups)
                        {
                            // Check if this row has a form number
                            string formNumber = null;
                            string endorsementName = null;
                            
                            // Extract text from all cells in the row
                            var rowTexts = rowGroup.Select(cell => cell["content"]?.ToString() ?? "").ToList();
                            
                            // For debugging
                            Console.WriteLine($"Row: {string.Join(" | ", rowTexts)}");
                            
                            // Try to find a form number in any cell
                            foreach (var text in rowTexts)
                            {
                                var match = formNumberPattern.Match(text);
                                if (match.Success)
                                {
                                    formNumber = match.Value.Replace(" ", ""); // Remove spaces for consistency
                                    Console.WriteLine($"Found form number: {formNumber}");
                                    break;
                                }
                            }
                            
                            // If we found a form number, look for an endorsement name in the same row
                            if (!string.IsNullOrEmpty(formNumber))
                            {
                                if (isEndorsementTable)
                                {
                                    // For table ID 4, the endorsement name is likely in a specific column
                                    // If there are multiple cells and the first is the form number, the second might be the name
                                    if (rowTexts.Count > 1)
                                    {
                                        // Try to find a cell that's not the form number and has significant length
                                        endorsementName = rowTexts
                                            .Where(t => !formNumberPattern.IsMatch(t) && t.Length > 3)
                                            .OrderByDescending(t => t.Length)
                                            .FirstOrDefault();
                                    }
                                }
                                else
                                {
                                    // For other tables, use the standard approach
                                    // The endorsement name is likely the longest text in the row that's not the form number
                                    endorsementName = rowTexts
                                        .Where(t => !formNumberPattern.IsMatch(t) && t.Length > 3)
                                        .OrderByDescending(t => t.Length)
                                        .FirstOrDefault();
                                }
                                    
                                if (!string.IsNullOrEmpty(endorsementName))
                                {
                                    Console.WriteLine($"Found endorsement name: {endorsementName}");
                                    
                                    // Add this endorsement to our list
                                    foundEndorsements.Add(new JObject
                                    {
                                        ["EndorsementNumber"] = formNumber,
                                        ["EndorsementName"] = endorsementName
                                    });
                                }
                                else
                                {
                                    Console.WriteLine($"Found form number {formNumber} but could not find endorsement name in the same row");
                                    
                                    // For endorsement table with form number but no name, use a generic description
                                    if (isEndorsementTable)
                                    {
                                        string genericName = "Additional Endorsement";
                                        Console.WriteLine($"Using generic name '{genericName}' for form {formNumber} in endorsement table");
                                        
                                        foundEndorsements.Add(new JObject
                                        {
                                            ["EndorsementNumber"] = formNumber,
                                            ["EndorsementName"] = genericName
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // If we didn't find any in tables, try looking through the entire document
            if (foundEndorsements.Count == 0)
            {
                Console.WriteLine("No endorsements found in tables, trying document-level search");
                
                // Get all text content from the document
                var documentContent = formData["analyzeResult"]?["content"]?.ToString() ?? "";
                
                // Find all form numbers in the document
                var formMatches = formNumberPattern.Matches(documentContent);
                Console.WriteLine($"Found {formMatches.Count} potential form numbers in document content");
                
                foreach (Match match in formMatches)
                {
                    string formNumber = match.Value.Replace(" ", ""); // Remove spaces
                    Console.WriteLine($"Found form number in document: {formNumber}");
                    
                    // Look for a potential endorsement name before or after the form number
                    // (Within a reasonable character distance)
                    int matchIndex = match.Index;
                    int beforeStart = Math.Max(0, matchIndex - 150); // Increased from 100 to 150
                    int afterEnd = Math.Min(documentContent.Length, matchIndex + match.Length + 150); // Increased from 100 to 150
                    
                    string before = documentContent.Substring(beforeStart, matchIndex - beforeStart);
                    string after = documentContent.Substring(matchIndex + match.Length, afterEnd - (matchIndex + match.Length));
                    
                    // Look for line breaks or multiple spaces as delimiters
                    string[] beforeLines = before.Split(new string[] { "\r", "\n", "  " }, StringSplitOptions.RemoveEmptyEntries);
                    string[] afterLines = after.Split(new string[] { "\r", "\n", "  " }, StringSplitOptions.RemoveEmptyEntries);
                    
                    string endorsementName = null;
                    
                    // Check the line immediately before
                    if (beforeLines.Length > 0)
                    {
                        // Get the last line before the form number
                        endorsementName = beforeLines.Last().Trim();
                        Console.WriteLine($"Potential endorsement name from line before: '{endorsementName}'");
                    }
                    
                    // If no name found before, check the line immediately after
                    if (string.IsNullOrWhiteSpace(endorsementName) && afterLines.Length > 0)
                    {
                        // Get the first line after the form number
                        endorsementName = afterLines.First().Trim();
                        Console.WriteLine($"Potential endorsement name from line after: '{endorsementName}'");
                    }
                    
                    // Check for reasonably looking endorsement names
                    if (!string.IsNullOrWhiteSpace(endorsementName) && endorsementName.Length > 5)
                    {
                        // Filter out some common false positives
                        if (!endorsementName.Contains("Policy Number") && 
                            !endorsementName.Contains("Form Number") && 
                            !endorsementName.StartsWith("CG") &&
                            !endorsementName.StartsWith("IL") &&
                            !formNumberPattern.IsMatch(endorsementName))
                        {
                            Console.WriteLine($"Found endorsement name: {endorsementName}");
                            
                            foundEndorsements.Add(new JObject
                            {
                                ["EndorsementNumber"] = formNumber,
                                ["EndorsementName"] = endorsementName
                            });
                        }
                        else
                        {
                            Console.WriteLine($"Filtered out endorsement name '{endorsementName}' as it appears to be a false positive");
                            
                            // Use a generic name if we couldn't find a valid name
                            foundEndorsements.Add(new JObject
                            {
                                ["EndorsementNumber"] = formNumber,
                                ["EndorsementName"] = "Additional Endorsement"
                            });
                        }
                    }
                    else
                    {
                        Console.WriteLine($"Could not find a valid endorsement name for form number {formNumber}");
                        
                        // Use a generic name if we couldn't find a valid name
                        foundEndorsements.Add(new JObject
                        {
                            ["EndorsementNumber"] = formNumber,
                            ["EndorsementName"] = "Additional Endorsement"
                        });
                    }
                }
            }
            
            Console.WriteLine($"Found {foundEndorsements.Count} potential endorsements in Form Recognizer output");
            
            if (foundEndorsements.Count == 0)
            {
                Console.WriteLine("No endorsements found, skipping update");
                return;
            }
            
            // Get or create the endorsements section in the main data
            JObject insurancePolicy = jsonData["InsurancePolicy"] as JObject ?? jsonData;
            JObject endorsementsSection;
            JArray endorsementsArray;

            bool hasNestedStructure = insurancePolicy["Endorsements"]?["Endorsement"] != null;

            if (hasNestedStructure)
            {
                endorsementsSection = insurancePolicy["Endorsements"] as JObject ?? new JObject();
                endorsementsArray = endorsementsSection["Endorsement"] as JArray ?? new JArray();
            }
            else
            {
                endorsementsSection = insurancePolicy["Endorsements"] as JObject ?? new JObject();
                endorsementsArray = insurancePolicy["Endorsements"] as JArray ?? new JArray();
            }

            // Create a HashSet to track existing endorsement numbers
            var existingEndorsementNumbers = new HashSet<string>();
            foreach (var endorsement in endorsementsArray)
            {
                string endorsementNumber = endorsement["EndorsementNumber"]?.ToString();
                if (!string.IsNullOrEmpty(endorsementNumber))
                {
                    existingEndorsementNumbers.Add(endorsementNumber);
                }
            }

            // Add new endorsements that don't already exist
            int addedCount = 0;
            foreach (var endorsement in foundEndorsements)
            {
                string endorsementNumber = endorsement["EndorsementNumber"]?.ToString();
                if (!string.IsNullOrEmpty(endorsementNumber) && !existingEndorsementNumbers.Contains(endorsementNumber))
                {
                    endorsementsArray.Add(new JObject
                    {
                        ["EndorsementNumber"] = endorsementNumber,
                        ["EndorsementName"] = endorsement["EndorsementName"]
                    });
                    existingEndorsementNumbers.Add(endorsementNumber);
                    addedCount++;
                    Console.WriteLine($"Added new endorsement {endorsementNumber}: {endorsement["EndorsementName"]}");
                }
            }

            // Update the endorsements in the main data
            if (hasNestedStructure)
            {
                endorsementsSection["Endorsement"] = endorsementsArray;
                insurancePolicy["Endorsements"] = endorsementsSection;
            }
            else
            {
                insurancePolicy["Endorsements"] = endorsementsArray;
            }

            if (jsonData != insurancePolicy)
            {
                jsonData["InsurancePolicy"] = insurancePolicy;
            }

            Console.WriteLine($"Successfully added {addedCount} new endorsements from Form Recognizer");
            
            // Update the temp.json file with the modified data
            string tempJsonPath = Path.Combine(outputDir, "temp.json");
            File.WriteAllText(tempJsonPath, jsonData.ToString(Newtonsoft.Json.Formatting.Indented));
            Console.WriteLine($"Updated temp.json with new endorsements");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error extracting endorsements from Form Recognizer: {ex.Message}");
            Console.WriteLine(ex.StackTrace);
        }
    }
}

// ------------------------------
// Processing class.
public static class Processor
{
    public static void FillDocxTemplate(string templatePath, JObject variables, string outputPath)
    {
        Console.WriteLine("Processing variables for DOCX:");
        Console.WriteLine($"Template path: {templatePath}");

        // Use "InsurancePolicy" if available.
        JToken data = variables.ContainsKey("InsurancePolicy") ? variables["InsurancePolicy"] : variables;

        // Check for nested Coverage structure and adjust paths accordingly
        bool hasNestedStructure = false;
        
        // Check if we have the nested structure by looking for multiple indicators
        bool hasCoverageNesting = false;
        bool hasLocationNesting = false;
        bool hasRatingBasisNesting = false;
        bool hasEndorsementNesting = false;
        
        // Log the TaxFeeItems structure for debugging
        if (data["Financials"] != null && data["Financials"]["TaxFeeItems"] != null)
        {
            var taxFeeItems = data["Financials"]["TaxFeeItems"];
            if (taxFeeItems.Type == JTokenType.Array)
            {
                Console.WriteLine($"Found TaxFeeItems array with {taxFeeItems.Count()} items");
                for (int i = 0; i < taxFeeItems.Count(); i++)
                {
                    var item = taxFeeItems[i];
                    Console.WriteLine($"TaxFeeItem[{i}]:");
                    foreach (var prop in item.Children<JProperty>())
                    {
                        Console.WriteLine($"  - {prop.Name}: {prop.Value}");
                    }
                }
            }
        }
        
        // Check for Coverages.Coverage structure
        if (data["Coverages"] != null && data["Coverages"]["Coverage"] != null)
        {
            var coverage = data["Coverages"]["Coverage"];
            if (coverage.Type == JTokenType.Array && coverage.Count() > 0)
            {
                var firstCoverage = coverage[0];
                if (firstCoverage["Limits"] != null && firstCoverage["Limits"]["Limit"] != null &&
                    firstCoverage["Deductibles"] != null && firstCoverage["Deductibles"]["Deductible"] != null)
                {
                    hasCoverageNesting = true;
                    Console.WriteLine("Detected nested Coverages.Coverage[].Limits.Limit structure");
                }
            }
        }
        
        // Check for Locations.Location structure
        if (data["Locations"] != null && data["Locations"]["Location"] != null)
        {
            var locations = data["Locations"]["Location"];
            if (locations.Type == JTokenType.Array && locations.Count() > 0)
            {
                hasLocationNesting = true;
                Console.WriteLine("Detected nested Locations.Location structure");
            }
        }
        
        // Check for RatingBasises.RatingBasis structure
        if (data["RatingBasises"] != null && data["RatingBasises"]["RatingBasis"] != null)
        {
            var ratingBasis = data["RatingBasises"]["RatingBasis"];
            if (ratingBasis.Type == JTokenType.Array && ratingBasis.Count() > 0)
            {
                hasRatingBasisNesting = true;
                Console.WriteLine("Detected nested RatingBasises.RatingBasis structure");
            }
        }
        
        // Check for Endorsements.Endorsement structure
        if (data["Endorsements"] != null && data["Endorsements"]["Endorsement"] != null)
        {
            var endorsements = data["Endorsements"]["Endorsement"];
            if (endorsements.Type == JTokenType.Array && endorsements.Count() > 0)
            {
                hasEndorsementNesting = true;
                Console.WriteLine("Detected nested Endorsements.Endorsement structure");
            }
        }
        
        // If any of the structures are nested, consider the whole structure nested
        hasNestedStructure = hasCoverageNesting || hasLocationNesting || hasRatingBasisNesting || hasEndorsementNesting;
        Console.WriteLine($"Using nested structure: {hasNestedStructure}");

        // Duplication configurations.
        var duplicationConfigs = new List<DuplicationConfig>();
        
        // Add configurations based on the detected structure
        if (hasNestedStructure)
        {
            duplicationConfigs.Add(new DuplicationConfig { 
                IdentifierField = "varLimitName", 
                ArrayPath = "Coverages.Coverage[0].Limits.Limit", 
                Fields = new List<string> { "LimitName", "LimitDollarAmount" } 
            });
            
            duplicationConfigs.Add(new DuplicationConfig { 
                IdentifierField = "varDeductibleName", 
                ArrayPath = "Coverages.Coverage[0].Deductibles.Deductible", 
                Fields = new List<string> { "DeductibleName", "DeductibleDollarAmount" } 
            });
            
            duplicationConfigs.Add(new DuplicationConfig { 
                IdentifierField = "varRateClassCode", 
                ArrayPath = "RatingBasises.RatingBasis", 
                Fields = new List<string> { "LocationNumber", "RateClassCode", "RateDescription", "RateBasis", "RateExposure", "NetRate", "RatePremium" } 
            });
            
            duplicationConfigs.Add(new DuplicationConfig { 
                IdentifierField = "varLocationFullAddress", 
                ArrayPath = "Locations.Location", 
                Fields = new List<string> { "LocationNumber", "LocationFullAddress", "LocationDescription" } 
            });
        }
        else
        {
            duplicationConfigs.Add(new DuplicationConfig { 
                IdentifierField = "varLimitName", 
                ArrayPath = "Coverages[0].Limits", 
                Fields = new List<string> { "LimitName", "LimitDollarAmount" } 
            });
            
            duplicationConfigs.Add(new DuplicationConfig { 
                IdentifierField = "varDeductibleName", 
                ArrayPath = "Coverages[0].Deductibles", 
                Fields = new List<string> { "DeductibleName", "DeductibleDollarAmount" } 
            });
            
            duplicationConfigs.Add(new DuplicationConfig { 
                IdentifierField = "varRateClassCode", 
                ArrayPath = "RatingBasises", 
                Fields = new List<string> { "LocationNumber", "RateClassCode", "RateDescription", "RateBasis", "RateExposure", "NetRate", "RatePremium" } 
            });
            
            duplicationConfigs.Add(new DuplicationConfig { 
                IdentifierField = "varLocationFullAddress", 
                ArrayPath = "Locations", 
                Fields = new List<string> { "LocationNumber", "LocationFullAddress", "LocationDescription" } 
            });
        }
        
        // Add the endorsements configuration that doesn't depend on the Coverage structure
        duplicationConfigs.Add(new DuplicationConfig { 
            IdentifierField = "varEndorsementNumber", 
            ArrayPath = hasNestedStructure ? "Endorsements.Endorsement" : "Endorsements", 
            Fields = new List<string> { "EndorsementNumber", "EndorsementName" } 
        });
        
        // Add configuration for Taxes and Fees section
        duplicationConfigs.Add(new DuplicationConfig { 
            IdentifierField = "fieldTaxFeeItemName", 
            ArrayPath = hasNestedStructure ? "Financials.TaxFeeItems.TaxFeeItem" : "Financials.TaxFeeItems", 
            Fields = new List<string> { "TaxFeeItemName", "TaxFeeItemDollarAmount" } 
        });

        // Copy the template to the output file.
		Console.WriteLine($"Copying file {templatePath} to the output folder: {outputPath}");
        Utils.SafeCopyFile(templatePath, outputPath);
		Console.WriteLine("Success...");
        try
        {
			Console.WriteLine("Reading word template...");
            using (var doc = WordprocessingDocument.Open(outputPath, true))
            {
				Console.WriteLine("And we're in...");
                var document = doc.MainDocumentPart.Document;

                // Get all content controls.
                var controls = document.Descendants<SdtElement>().ToList();
                Console.WriteLine($"Found {controls.Count} content controls");

                // Flatten JSON for lookup.
                var flattenedData = Utils.FlattenJson(data);

                // --- Duplication Processing ---
                // Locate all table rows that require duplication.
                var tables = document.Descendants<Table>().ToList();
                var tablesToDuplicate = new List<(Table table, TableRow row, DuplicationConfig config)>();

                foreach (var table in tables)
                {
                    var rows = table.Descendants<TableRow>().ToList();
                    foreach (var row in rows)
                    {
                        foreach (var config in duplicationConfigs)
                        {
                            // Only look at content controls that have an SdtAlias (Title) property
                            var sdt = row.Descendants<SdtElement>()
                                .FirstOrDefault(x =>
                                {
                                    var alias = x.SdtProperties?.GetFirstChild<SdtAlias>();
                                    // Only check the Title (alias) property, ignore Tag
                                    return alias != null && 
                                           alias.Val != null && 
                                           alias.Val.HasValue && 
                                           alias.Val.Value == config.IdentifierField;
                                });

                            if (sdt != null)
                            {
                                // For TaxFeeItems, only process if this is the correct table
                                if (config.IdentifierField == "fieldTaxFeeItemName")
                                {
                                    // Instead of directly using primaryPath/fallbackPath, store diagnostic info
                                    string diagPath1 = "Financials.TaxFeeItems";
                                    string diagPath2 = "Financials.TaxFeeItems.TaxFeeItem";
                                    
                                    // Additional logging of the TaxFeeItems structure
                                    var taxFeeItemsToken1 = Utils.GetJsonValue((JObject)data, diagPath1);
                                    var taxFeeItemsToken2 = Utils.GetJsonValue((JObject)data, diagPath2);
                                    var taxFeeItemsToken3 = Utils.GetJsonValue((JObject)data, "Financials");
                                    
                                    Console.WriteLine("DEBUG: TaxFeeItems structure check:");
                                    Console.WriteLine($"- Path '{diagPath1}' exists: {taxFeeItemsToken1 != null}, Type: {taxFeeItemsToken1?.Type.ToString() ?? "N/A"}");
                                    Console.WriteLine($"- Path '{diagPath2}' exists: {taxFeeItemsToken2 != null}, Type: {taxFeeItemsToken2?.Type.ToString() ?? "N/A"}");
                                    Console.WriteLine($"- Path 'Financials' exists: {taxFeeItemsToken3 != null}, Type: {taxFeeItemsToken3?.Type.ToString() ?? "N/A"}");
                                    
                                    if (taxFeeItemsToken1 != null && taxFeeItemsToken1.Type == JTokenType.Array)
                                    {
                                        Console.WriteLine($"'{diagPath1}' is an array with {taxFeeItemsToken1.Count()} items");
                                        foreach (var item in taxFeeItemsToken1)
                                        {
                                            if (item is JObject obj)
                                            {
                                                Console.WriteLine($"- Item properties: {string.Join(", ", obj.Properties().Select(p => p.Name))}");
                                            }
                                        }
                                    }
                                    
                                    Console.WriteLine("Processing TaxFeeItems for duplication in table");
                                    
                                    // Verify we have a two-column table
                                    var rowCells = row.Elements<TableCell>().ToList();
                                    Console.WriteLine($"Table row has {rowCells.Count} columns");
                                    
                                    // Search for content controls in the row to verify we have the right structure
                                    var contentControls = row.Descendants<SdtElement>()
                                        .Where(x => x.SdtProperties?.GetFirstChild<SdtAlias>() != null)
                                        .Select(x => x.SdtProperties?.GetFirstChild<SdtAlias>()?.Val?.Value)
                                        .ToList();
                                    
                                    Console.WriteLine($"Content controls in row: {string.Join(", ", contentControls)}");
                                }
                                
                                int rowIndex = table.ChildElements.ToList().IndexOf(row);
                                Console.WriteLine($"Found row with {config.IdentifierField} in table at index {rowIndex}");
                                tablesToDuplicate.Add((table, row, config));
                            }
                        }
                    }
                }

                // Process duplication rows in descending order so insertions do not affect earlier indices.
                var sortedDuplicates = tablesToDuplicate
                    .OrderByDescending(dup => dup.table.ChildElements.ToList().IndexOf(dup.row))
                    .ToList();

                // Track which types of items we've processed to avoid duplicates
                var processedTypes = new HashSet<string>();

                foreach (var dup in sortedDuplicates)
                {
                    // Skip if we've already processed this type of item
                    if (!processedTypes.Add(dup.config.IdentifierField))
                    {
                        Console.WriteLine($"Skipping duplicate processing for {dup.config.IdentifierField}");
                        continue;
                    }

                    // Try to get the array using both possible paths
                    string fallbackPath = dup.config.ArrayPath;
                    string primaryPath = dup.config.ArrayPath;
                    
                    // If we're using a nested structure, try both nested and non-nested paths
                    if (hasNestedStructure && dup.config.IdentifierField == "varRateClassCode")
                    {
                        primaryPath = "RatingBasises.RatingBasis";
                        fallbackPath = "RatingBasises";
                    }
                    else if (hasNestedStructure && dup.config.IdentifierField == "varLocationFullAddress")
                    {
                        primaryPath = "Locations.Location";
                        fallbackPath = "Locations";
                    }
                    else if (hasNestedStructure && dup.config.IdentifierField == "varEndorsementNumber")
                    {
                        primaryPath = "Endorsements.Endorsement";
                        fallbackPath = "Endorsements";
                    }
                    else if (dup.config.IdentifierField == "fieldTaxFeeItemName")
                    {
                        // For TaxFeeItems, we use different paths with extra logging
                        primaryPath = "Financials.TaxFeeItems";
                        fallbackPath = "Financials.TaxFeeItems.TaxFeeItem";
                        
                        // Additional logging of the TaxFeeItems structure
                        var taxFeeItemsToken1 = Utils.GetJsonValue((JObject)data, primaryPath);
                        var taxFeeItemsToken2 = Utils.GetJsonValue((JObject)data, fallbackPath);
                        var taxFeeItemsToken3 = Utils.GetJsonValue((JObject)data, "Financials");
                        
                        Console.WriteLine("DEBUG: TaxFeeItems structure check:");
                        Console.WriteLine($"- Path '{primaryPath}' exists: {taxFeeItemsToken1 != null}, Type: {taxFeeItemsToken1?.Type.ToString() ?? "N/A"}");
                        Console.WriteLine($"- Path '{fallbackPath}' exists: {taxFeeItemsToken2 != null}, Type: {taxFeeItemsToken2?.Type.ToString() ?? "N/A"}");
                        Console.WriteLine($"- Path 'Financials' exists: {taxFeeItemsToken3 != null}, Type: {taxFeeItemsToken3?.Type.ToString() ?? "N/A"}");
                        
                        if (taxFeeItemsToken1 != null && taxFeeItemsToken1.Type == JTokenType.Array)
                        {
                            Console.WriteLine($"'{primaryPath}' is an array with {taxFeeItemsToken1.Count()} items");
                            foreach (var item in taxFeeItemsToken1)
                            {
                                if (item is JObject obj)
                                {
                                    Console.WriteLine($"- Item properties: {string.Join(", ", obj.Properties().Select(p => p.Name))}");
                                }
                            }
                        }
                        
                        Console.WriteLine("Processing TaxFeeItems for duplication in table");
                        
                        // Verify we have a valid table row structure
                        var rowCells = dup.row.Elements<TableCell>().ToList();
                        Console.WriteLine($"Table row has {rowCells.Count} columns");
                        
                        // Search for content controls in the row to verify we have the right structure
                        var contentControls = dup.row.Descendants<SdtElement>()
                            .Where(x => x.SdtProperties?.GetFirstChild<SdtAlias>() != null)
                            .Select(x => x.SdtProperties?.GetFirstChild<SdtAlias>()?.Val?.Value)
                            .ToList();
                        
                        Console.WriteLine($"Content controls in row: {string.Join(", ", contentControls)}");
                    }
                    
                    var array = Utils.TryGetJsonArray((JObject)data, primaryPath, fallbackPath);
                    if (array != null)
                    {
                        Console.WriteLine($"Processing array at path {primaryPath} or {fallbackPath} with {array.Count} items");
                        
                        int rowIndex = dup.table.ChildElements.ToList().IndexOf(dup.row);
                        var templateRow = (TableRow)dup.row.CloneNode(true);
                        dup.row.Remove();
                        for (int i = 0; i < array.Count; i++)
                        {
                            var item = array[i] as JObject;
                            Console.WriteLine($"Processing item {i} in {primaryPath}:");
                            foreach (var prop in item.Properties())
                            {
                                Console.WriteLine($"  - {prop.Name}: {prop.Value}");
                            }
                            
                            var newRow = (TableRow)templateRow.CloneNode(true);
                            // Process each content control in the new row.
                            foreach (var ctrl in newRow.Descendants<SdtElement>())
                            {
                                var alias = ctrl.SdtProperties?.GetFirstChild<SdtAlias>();
                                if (alias == null)
                                    continue;
                                string title = alias.Val.Value;
                                if (title.StartsWith("var") || title.StartsWith("field"))
                                {
                                    string fieldName = title.StartsWith("var") ? title.Substring(3) : title.Substring(5);
                                    if (item.ContainsKey(fieldName))
                                    {
                                        string value = item[fieldName]?.ToString();
                                        
                                        // Special handling for TaxFeeItemDollarAmount
                                        Utils.SetContentControlText(ctrl, value);
                                        Console.WriteLine($"  Set {fieldName} to {value} in duplicated row {i}");
                                    }
                                    else if (dup.config.IdentifierField == "fieldTaxFeeItemName")
                                    {
                                        // Special handling for TaxFeeItems two-column structure
                                        if (fieldName == "TaxFeeItemName")
                                        {
                                            string name = item["Name"]?.ToString() ?? 
                                                          item["TaxFeeItemName"]?.ToString() ?? 
                                                          "";
                                            
                                            // If still empty, try looking at deeper structure
                                            if (string.IsNullOrEmpty(name) && item["TaxFeeItem"] != null)
                                            {
                                                var taxFeeItem = item["TaxFeeItem"];
                                                if (taxFeeItem is JObject taxFeeItemObj)
                                                {
                                                    name = taxFeeItemObj["Name"]?.ToString() ?? 
                                                           taxFeeItemObj["TaxFeeItemName"]?.ToString() ?? 
                                                           "";
                                                }
                                            }
                                            
                                            Console.WriteLine($"  Setting TaxFeeItemName to '{name}' in row {i}");
                                            Utils.SetContentControlText(ctrl, name);
                                        }
                                        else if (fieldName == "TaxFeeItemDollarAmount")
                                        {
                                            string amount = item["Amount"]?.ToString() ?? 
                                                            item["TaxFeeItemDollarAmount"]?.ToString() ?? 
                                                            "";
                                                                
                                            // If still empty, try looking at deeper structure
                                            if (string.IsNullOrEmpty(amount) && item["TaxFeeItem"] != null)
                                            {
                                                var taxFeeItem = item["TaxFeeItem"];
                                                if (taxFeeItem is JObject taxFeeItemObj)
                                                {
                                                    amount = taxFeeItemObj["Amount"]?.ToString() ?? 
                                                             taxFeeItemObj["TaxFeeItemDollarAmount"]?.ToString() ?? 
                                                             "";
                                                }
                                            }
                                            
                                            Console.WriteLine($"  Original TaxFeeItemDollarAmount value: '{amount}'");
                                            
                                            // Format amount WITHOUT dollar sign for fieldTaxFeeItemDollarAmount
                                            string displayAmount = amount;
                                            if (!string.IsNullOrWhiteSpace(displayAmount))
                                            {
                                                // Force use the FormatAmountWithoutDollarSign method
                                                displayAmount = Utils.FormatAmountWithoutDollarSign(displayAmount);
                                                Console.WriteLine($"  After FormatAmountWithoutDollarSign: '{displayAmount}'");
                                            }
                                            
                                            Console.WriteLine($"  Setting TaxFeeItemDollarAmount to '{displayAmount}' in row {i} (no dollar sign)");
                                            
                                            try {
                                                // BYPASS the utility method entirely and set text directly
                                                var run = new Run(new Text(displayAmount));
                                                
                                                // Find SdtContentBlock and replace its contents
                                                var sdtContent = ctrl.GetFirstChild<SdtContentBlock>();
                                                if (sdtContent != null)
                                                {
                                                    Console.WriteLine($"  Found SdtContentBlock, clearing and setting text directly to '{displayAmount}'");
                                                    sdtContent.RemoveAllChildren();
                                                    sdtContent.AppendChild(run);
                                                    // Override the regular console output
                                                    Console.WriteLine($"  Set TaxFeeItemDollarAmount to '{displayAmount}' in duplicated row {i} (NO DOLLAR SIGN)");
                                                }
                                                else
                                                {
                                                    Console.WriteLine($"  ERROR: Could not find SdtContentBlock");
                                                    
                                                    // Try an alternative approach using document.MainDocumentPart
                                                    var sdtRun = ctrl.Descendants<SdtRun>().FirstOrDefault();
                                                    if (sdtRun != null)
                                                    {
                                                        Console.WriteLine($"  Found SdtRun, attempting to set text");
                                                        var sdtRunContent = sdtRun.SdtContentRun;
                                                        if (sdtRunContent != null)
                                                        {
                                                            sdtRunContent.RemoveAllChildren();
                                                            sdtRunContent.AppendChild(run);
                                                            Console.WriteLine($"  Set text directly in SdtContentRun to '{displayAmount}'");
                                                            Console.WriteLine($"  Set TaxFeeItemDollarAmount to '{displayAmount}' in duplicated row {i} (NO DOLLAR SIGN)");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        Console.WriteLine($"  FINAL FALLBACK: Using SetContentControlText with isFieldPrefix=true, isMonetaryValue=true");
                                                        Utils.SetContentControlText(ctrl, displayAmount, true, true);
                                                        Console.WriteLine($"  Set TaxFeeItemDollarAmount to '{displayAmount}' in duplicated row {i} (Dollar sign handling delegated)");
                                                    }
                                                }
                                                
                                                // Double-check what was actually set
                                                var resultText = ctrl.Descendants<Text>().FirstOrDefault()?.Text ?? "MISSING";
                                                Console.WriteLine($"  VERIFICATION - Text now in control: '{resultText}'");
                                            }
                                            catch (Exception ex)
                                            {
                                                Console.WriteLine($"  ERROR: Could not set TaxFeeItemDollarAmount: {ex.Message}");
                                                Console.WriteLine($"  Stack trace: {ex.StackTrace}");
                                                
                                                // Last resort fallback
                                                Console.WriteLine($"  FINAL FALLBACK: Using SetContentControlText with isFieldPrefix=true, isMonetaryValue=true");
                                                Utils.SetContentControlText(ctrl, displayAmount, true, true);
                                                Console.WriteLine($"  Set TaxFeeItemDollarAmount to '{displayAmount}' in duplicated row {i} (Dollar sign handling delegated)");
                                            }
                                        }
                                    }
                                    else
                                    {
                                        Console.WriteLine($"  Warning: Field {fieldName} not found in item {i}");
                                    }
                                }
                            }
                            dup.table.InsertAt(newRow, rowIndex + i);
                        }
                        
                        // Apply vertical merging for Limits table if needed
                        if (dup.config.IdentifierField == "varLimitName" && array.Count > 0)
                        {
                            Console.WriteLine($"Applying vertical merge for Limits table with {array.Count} rows");
                            
                            try
                            {
                                // Get all rows in the table
                                var allTableRows = dup.table.Elements<TableRow>().ToList();
                                Console.WriteLine($"Total rows in table: {allTableRows.Count}");
                                
                                // Find the row that contains "Limits" text in the second column
                                int limitsHeaderRowIndex = -1;
                                int deductiblesHeaderRowIndex = -1;
                                
                                // First, find the Limits header row and Deductibles header row
                                for (int j = 0; j < allTableRows.Count; j++)
                                {
                                    var row = allTableRows[j];
                                    var cells = row.Elements<TableCell>().ToList();
                                    
                                    if (cells.Count > 1)
                                    {
                                        var cellText = cells[1].InnerText;
                                        Console.WriteLine($"Row {j}, Cell 2 text: '{cellText}'");
                                        
                                        // Also check for content controls with alias 'spacer1'
                                        var spacerControl = row.Descendants<SdtElement>()
                                            .FirstOrDefault(x => {
                                                var alias = x.SdtProperties?.GetFirstChild<SdtAlias>();
                                                return alias != null && alias.Val.Value == "spacer1";
                                            });
                                        
                                        if (spacerControl != null)
                                        {
                                            Console.WriteLine($"Found spacer row at index {j}");
                                            // If we find a spacer row between limits and deductibles, 
                                            // we'll stop the merge before this row
                                            if (limitsHeaderRowIndex != -1 && deductiblesHeaderRowIndex == -1)
                                            {
                                                deductiblesHeaderRowIndex = j;
                                            }
                                        }
                                        else if (cellText.Contains("Limits") && limitsHeaderRowIndex == -1)
                                        {
                                            limitsHeaderRowIndex = j;
                                            Console.WriteLine($"Found Limits header at row {j}");
                                        }
                                        else if (cellText.Contains("Deductible") && deductiblesHeaderRowIndex == -1)
                                        {
                                            deductiblesHeaderRowIndex = j;
                                            Console.WriteLine($"Found Deductibles header at row {j}");
                                        }
                                    }
                                }
                                
                                // If we found the limits header but not deductibles, use the end of the array
                                if (limitsHeaderRowIndex >= 0 && deductiblesHeaderRowIndex == -1)
                                {
                                    // Just merge the rows we added (the limits rows)
                                    deductiblesHeaderRowIndex = rowIndex + array.Count;
                                    Console.WriteLine($"No Deductibles header found, will merge to row {deductiblesHeaderRowIndex}");
                                }
                                
                                // If we found both headers, merge all cells between them
                                if (limitsHeaderRowIndex >= 0 && deductiblesHeaderRowIndex > limitsHeaderRowIndex)
                                {
                                    Console.WriteLine($"Will merge cells from row {limitsHeaderRowIndex} to {deductiblesHeaderRowIndex - 1}");
                                    
                                    // Apply vertical merge to all rows in the range
                                    for (int j = limitsHeaderRowIndex; j < deductiblesHeaderRowIndex; j++)
                                    {
                                        if (j >= allTableRows.Count)
                                        {
                                            Console.WriteLine($"Warning: Row index {j} is out of bounds");
                                            continue;
                                        }
                                        
                                        TableRow currentRow = allTableRows[j];
                                        var cells = currentRow.Elements<TableCell>().ToList();
                                        
                                        if (cells.Count > 1)
                                        {
                                            TableCell secondCell = cells[1];
                                            
                                            // Ensure the cell has TableCellProperties
                                            var tcPr = secondCell.GetFirstChild<TableCellProperties>();
                                            if (tcPr == null)
                                            {
                                                tcPr = new TableCellProperties();
                                                secondCell.PrependChild(tcPr);
                                            }
                                            
                                            // Remove any existing VerticalMerge elements
                                            foreach (var vmerge in tcPr.Elements<DocumentFormat.OpenXml.Wordprocessing.VerticalMerge>().ToList())
                                            {
                                                vmerge.Remove();
                                            }
                                            
                                            if (j == limitsHeaderRowIndex)
                                            {
                                                // First cell: start the merge
                                                Console.WriteLine($"Starting vertical merge at row {j}");
                                                tcPr.Append(new DocumentFormat.OpenXml.Wordprocessing.VerticalMerge() 
                                                { 
                                                    Val = DocumentFormat.OpenXml.Wordprocessing.MergedCellValues.Restart 
                                                });
                                                
                                                // Also set the text to "Limits" in case it's not already
                                                var paragraphs = secondCell.Elements<Paragraph>().ToList();
                                                if (!paragraphs.Any())
                                                {
                                                    secondCell.Append(new Paragraph(new Run(new Text("Limits"))));
                                                }
                                                else
                                                {
                                                    var firstPara = paragraphs.First();
                                                    var runs = firstPara.Elements<Run>().ToList();
                                                    if (!runs.Any())
                                                    {
                                                        firstPara.Append(new Run(new Text("Limits")));
                                                    }
                                                    else
                                                    {
                                                        var firstRun = runs.First();
                                                        var texts = firstRun.Elements<Text>().ToList();
                                                        if (!texts.Any())
                                                        {
                                                            firstRun.Append(new Text("Limits"));
                                                        }
                                                        else
                                                        {
                                                            texts.First().Text = "Limits";
                                                        }
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                // Subsequent cells: continue the merge
                                                Console.WriteLine($"Continuing vertical merge at row {j}");
                                                tcPr.Append(new DocumentFormat.OpenXml.Wordprocessing.VerticalMerge());
                                                
                                                // Clear the text content in merged cells
                                                secondCell.RemoveAllChildren<Paragraph>();
                                                secondCell.Append(new Paragraph());
                                            }
                                        }
                                        else
                                        {
                                            Console.WriteLine($"Warning: Row {j} doesn't have enough cells for merging");
                                        }
                                    }
                                }
                                else
                                {
                                    Console.WriteLine($"Could not find both Limits and Deductibles headers. Limits: {limitsHeaderRowIndex}, Deductibles: {deductiblesHeaderRowIndex}");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error during vertical merging: {ex.Message}");
                                Console.WriteLine(ex.StackTrace);
                            }
                        }

                        // Apply vertical merging for Deductibles table if needed
                        if (dup.config.IdentifierField == "varDeductibleName" && array.Count > 0)
                        {
                            Console.WriteLine($"Applying vertical merge for Deductibles table with {array.Count} rows");
                            
                            try
                            {
                                // Get all rows in the table
                                var allTableRows = dup.table.Elements<TableRow>().ToList();
                                Console.WriteLine($"Total rows in table: {allTableRows.Count}");
                                
                                // Find the row that contains "Deductible" text in the second column
                                int deductiblesHeaderRowIndex = -1;
                                int endRowIndex = -1;
                                
                                // First, find the Deductibles header row and any boundary row
                                for (int j = 0; j < allTableRows.Count; j++)
                                {
                                    var row = allTableRows[j];
                                    var cells = row.Elements<TableCell>().ToList();
                                    
                                    if (cells.Count > 1)
                                    {
                                        var cellText = cells[1].InnerText;
                                        Console.WriteLine($"Row {j}, Cell 2 text: '{cellText}'");
                                        
                                        // Check for content controls with alias that might indicate a boundary
                                        var boundaryControl = row.Descendants<SdtElement>()
                                            .FirstOrDefault(x => {
                                                var alias = x.SdtProperties?.GetFirstChild<SdtAlias>();
                                                return alias != null && 
                                                    (alias.Val.Value == "spacer2" || 
                                                     alias.Val.Value.StartsWith("varRate") || 
                                                     alias.Val.Value.StartsWith("varLocation"));
                                            });
                                        
                                        if (boundaryControl != null)
                                        {
                                            Console.WriteLine($"Found boundary row at index {j}");
                                            // If we find a boundary row after deductibles, 
                                            // we'll stop the merge before this row
                                            if (deductiblesHeaderRowIndex != -1 && endRowIndex == -1)
                                            {
                                                endRowIndex = j;
                                            }
                                        }
                                        else if (cellText.Contains("Deductible") && deductiblesHeaderRowIndex == -1)
                                        {
                                            deductiblesHeaderRowIndex = j;
                                            Console.WriteLine($"Found Deductibles header at row {j}");
                                        }
                                    }
                                }
                                
                                // If we found the deductibles header but not an end boundary, use the end of the array
                                if (deductiblesHeaderRowIndex >= 0 && endRowIndex == -1)
                                {
                                    // Just merge the rows we added (the deductibles rows)
                                    endRowIndex = rowIndex + array.Count;
                                    Console.WriteLine($"No boundary row found, will merge to row {endRowIndex}");
                                }
                                
                                // If we found the header and end boundary, merge all cells between them
                                if (deductiblesHeaderRowIndex >= 0 && endRowIndex > deductiblesHeaderRowIndex)
                                {
                                    Console.WriteLine($"Will merge cells from row {deductiblesHeaderRowIndex} to {endRowIndex - 1}");
                                    
                                    // Apply vertical merge to all rows in the range
                                    for (int j = deductiblesHeaderRowIndex; j < endRowIndex; j++)
                                    {
                                        if (j >= allTableRows.Count)
                                        {
                                            Console.WriteLine($"Warning: Row index {j} is out of bounds");
                                            continue;
                                        }
                                        
                                        TableRow currentRow = allTableRows[j];
                                        var cells = currentRow.Elements<TableCell>().ToList();
                                        
                                        if (cells.Count > 1)
                                        {
                                            TableCell secondCell = cells[1];
                                            
                                            // Ensure the cell has TableCellProperties
                                            var tcPr = secondCell.GetFirstChild<TableCellProperties>();
                                            if (tcPr == null)
                                            {
                                                tcPr = new TableCellProperties();
                                                secondCell.PrependChild(tcPr);
                                            }
                                            
                                            // Remove any existing VerticalMerge elements
                                            foreach (var vmerge in tcPr.Elements<DocumentFormat.OpenXml.Wordprocessing.VerticalMerge>().ToList())
                                            {
                                                vmerge.Remove();
                                            }
                                            
                                            if (j == deductiblesHeaderRowIndex)
                                            {
                                                // First cell: start the merge
                                                Console.WriteLine($"Starting vertical merge at row {j}");
                                                tcPr.Append(new DocumentFormat.OpenXml.Wordprocessing.VerticalMerge() 
                                                { 
                                                    Val = DocumentFormat.OpenXml.Wordprocessing.MergedCellValues.Restart 
                                                });
                                                
                                                // Also set the text to "Deductibles" in case it's not already
                                                var paragraphs = secondCell.Elements<Paragraph>().ToList();
                                                if (!paragraphs.Any())
                                                {
                                                    secondCell.Append(new Paragraph(new Run(new Text("Deductibles"))));
                                                }
                                                else
                                                {
                                                    var firstPara = paragraphs.First();
                                                    var runs = firstPara.Elements<Run>().ToList();
                                                    if (!runs.Any())
                                                    {
                                                        firstPara.Append(new Run(new Text("Deductibles")));
                                                    }
                                                    else
                                                    {
                                                        var firstRun = runs.First();
                                                        var texts = firstRun.Elements<Text>().ToList();
                                                        if (!texts.Any())
                                                        {
                                                            firstRun.Append(new Text("Deductibles"));
                                                        }
                                                        else
                                                        {
                                                            texts.First().Text = "Deductibles";
                                                        }
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                // Subsequent cells: continue the merge
                                                Console.WriteLine($"Continuing vertical merge at row {j}");
                                                tcPr.Append(new DocumentFormat.OpenXml.Wordprocessing.VerticalMerge());
                                                
                                                // Clear the text content in merged cells
                                                secondCell.RemoveAllChildren<Paragraph>();
                                                secondCell.Append(new Paragraph());
                                            }
                                        }
                                        else
                                        {
                                            Console.WriteLine($"Warning: Row {j} doesn't have enough cells for merging");
                                        }
                                    }
                                }
                                else
                                {
                                    Console.WriteLine($"Could not find both Deductibles header and end boundary. Deductibles: {deductiblesHeaderRowIndex}, End: {endRowIndex}");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error during vertical merging for deductibles: {ex.Message}");
                                Console.WriteLine(ex.StackTrace);
                            }
                        }
                    }
                }
                // --- End Duplication Processing ---

                // --- Process Remaining Content Controls ---
                Console.WriteLine("Processing remaining content controls...");
                
				// Create a dictionary to store the value for each field.
				var fieldValues = new Dictionary<string, string>();

				Console.WriteLine("Processing remaining content controls...");
				foreach (var ctrl in controls)
				{
					var alias = ctrl.SdtProperties?.GetFirstChild<SdtAlias>();
					if (alias == null)
						continue;
					
					string title = alias.Val.Value;
					if (title.StartsWith("Table."))
						continue;
					
					if (title.StartsWith("var"))
					{
						string fieldName = title.Substring(3);
						string value;
						// If the value has already been computed, reuse it.
						if (!fieldValues.TryGetValue(fieldName, out value))
						{
							// Attempt to get the value from the flattened JSON.
							if (flattenedData.ContainsKey(fieldName))
							{
								value = flattenedData[fieldName].ToString();
							}
							else
							{
								var possiblePaths = new List<string>
								{
									$"ClientDetails.{fieldName}",
									$"ClientDetails.ClientAddress.{fieldName}",
									$"PolicyDetails.{fieldName}",
									$"LegalNotices.{fieldName}"
								};
								
								// Add paths based on the detected structure
								if (hasNestedStructure)
								{
									possiblePaths.AddRange(new string[]
									{
										$"Coverages.Coverage[0].{fieldName}",
										$"Coverages.Coverage[0].Limits.Limit[0].{fieldName}",
										$"Coverages.Coverage[0].Deductibles.Deductible[0].{fieldName}",
										$"RatingBasises.RatingBasis[0].{fieldName}",
										$"Locations.Location[0].{fieldName}",
										$"Endorsements.Endorsement[0].{fieldName}"
									});
								}
								else
								{
									possiblePaths.AddRange(new string[]
									{
										$"Coverages[0].{fieldName}",
										$"Coverages[0].Limits[0].{fieldName}",
										$"Coverages[0].Deductibles[0].{fieldName}",
										$"RatingBasises[0].{fieldName}",
										$"Locations[0].{fieldName}",
										$"Endorsements[0].{fieldName}"
									});
								}
								
								// Add common paths that don't depend on the Coverage structure
								possiblePaths.AddRange(new string[]
								{
									$"Financials.{fieldName}"
								});
								
								foreach (var path in possiblePaths)
								{
									var token = Utils.GetJsonValue((JObject)data, path);
									if (token != null)
									{
										value = token.ToString();
										Console.WriteLine($"Found value for {fieldName} at path: {path}");
										break;
									}
								}
							}
							
							// Special handling for date fields - ensure leading zeros for months
							if (fieldName.Contains("Date") || fieldName.EndsWith("Date") || fieldName.StartsWith("Date"))
							{
								value = Utils.FormatDateString(value);
								Console.WriteLine($"Formatted date value for {fieldName}: {value}");
							}
							
							// Special handling for CoverageName when using nested structure
							if (fieldName == "CoverageName" && string.IsNullOrEmpty(value) && hasNestedStructure)
							{
								// Try to get it directly from the nested structure
								var coverageToken = Utils.GetJsonValue((JObject)data, "Coverages.Coverage[0].CoverageName");
								if (coverageToken != null)
								{
									value = coverageToken.ToString();
									Console.WriteLine($"Retrieved CoverageName from nested structure: {value}");
								}
							}
							
							// Special handling for RatingBasis fields when using nested structure
							if ((fieldName.StartsWith("Rate") || fieldName == "LocationNumber" || fieldName == "GrossRate" || fieldName == "NetRate") 
								&& string.IsNullOrEmpty(value) && hasNestedStructure)
							{
								// Try to get it directly from the nested structure
								var ratingToken = Utils.GetJsonValue((JObject)data, $"RatingBasises.RatingBasis[0].{fieldName}");
								if (ratingToken != null)
								{
									value = ratingToken.ToString();
									Console.WriteLine($"Retrieved {fieldName} from nested RatingBasis structure: {value}");
								}
							}
							
							// Special handling for Location fields when using nested structure
							if ((fieldName.StartsWith("Location") || fieldName == "LocationDescription") 
								&& string.IsNullOrEmpty(value) && hasNestedStructure)
							{
								// Try to get it directly from the nested structure
								var locationToken = Utils.GetJsonValue((JObject)data, $"Locations.Location[0].{fieldName}");
								if (locationToken != null)
								{
									value = locationToken.ToString();
									Console.WriteLine($"Retrieved {fieldName} from nested Location structure: {value}");
								}
							}
							
							// Special handling for TaxFeeItem fields
							if (fieldName.StartsWith("TaxFeeItem") && string.IsNullOrEmpty(value))
							{
								// Try to get it from the TaxFeeItems array
								Console.WriteLine($"Looking for {fieldName} in TaxFeeItems structure");
								var taxFeeItemsToken = Utils.GetJsonValue((JObject)data, "Financials.TaxFeeItems[0]");
								if (taxFeeItemsToken != null && taxFeeItemsToken is JObject taxFeeItem)
								{
									Console.WriteLine($"Found TaxFeeItems[0] node. Structure: {taxFeeItem.ToString(Newtonsoft.Json.Formatting.Indented)}");
									
									// First try direct property
									if (taxFeeItem.ContainsKey(fieldName))
									{
										value = taxFeeItem[fieldName].ToString();
										Console.WriteLine($"Retrieved {fieldName} from TaxFeeItems[0]: {value}");
									}
									// If not found, try one level deeper (for nested structures)
									else if (taxFeeItem.ContainsKey("TaxFeeItem"))
									{
										var nestedItem = taxFeeItem["TaxFeeItem"];
										Console.WriteLine($"Found TaxFeeItems[0].TaxFeeItem node. Type: {nestedItem.Type}, Value: {nestedItem.ToString()}");
										
										if (nestedItem is JObject nestedObj && nestedObj.ContainsKey(fieldName))
										{
											value = nestedObj[fieldName].ToString();
											Console.WriteLine($"Retrieved {fieldName} from TaxFeeItems[0].TaxFeeItem: {value}");
										}
										// Also check for Name/Amount pattern in the nested object
										else if (fieldName == "TaxFeeItemName" && nestedItem is JObject nameObj && nameObj.ContainsKey("Name"))
										{
											value = nameObj["Name"].ToString();
											Console.WriteLine($"Retrieved TaxFeeItemName from TaxFeeItems[0].TaxFeeItem.Name: {value}");
										}
										else if (fieldName == "TaxFeeItemDollarAmount" && nestedItem is JObject amountObj && amountObj.ContainsKey("Amount"))
										{
											value = amountObj["Amount"].ToString();
											Console.WriteLine($"Retrieved TaxFeeItemDollarAmount from TaxFeeItems[0].TaxFeeItem.Amount: {value}");
										}
									}
									// Also check for Name/Amount pattern at the top level
									else if (fieldName == "TaxFeeItemName" && taxFeeItem.ContainsKey("Name"))
									{
										value = taxFeeItem["Name"].ToString();
										Console.WriteLine($"Retrieved TaxFeeItemName from TaxFeeItems[0].Name: {value}");
									}
									else if (fieldName == "TaxFeeItemDollarAmount" && taxFeeItem.ContainsKey("Amount"))
									{
										value = taxFeeItem["Amount"].ToString();
										Console.WriteLine($"Retrieved TaxFeeItemDollarAmount from TaxFeeItems[0].Amount: {value}");
									}
									else
									{
										Console.WriteLine($"Could not find {fieldName} in any expected location in the TaxFeeItems structure");
									}
								}
								else
								{
									Console.WriteLine($"TaxFeeItems[0] node not found or not a JObject. Value: {taxFeeItemsToken?.ToString() ?? "null"}");
								}
							}
							
							// Special handling for ClientAddress3 - only use if it contains meaningful content
							if (fieldName == "ClientAddress3")
							{
								// Always clear ClientAddress3 unless it contains ATTN: or c/o
								if (!value.Contains("ATTN:") && !value.Contains("c/o") && !value.Contains("C/O"))
								{
									value = "";
									Console.WriteLine($"Clearing ClientAddress3 as it doesn't contain ATTN: or c/o prefix");
								}
							}
							
							// Special handling for ClientAddress2 - ensure it contains city, state, and ZIP
							if (fieldName == "ClientAddress2")
							{
								// Try to get the ZIP code from ClientAddress3 if it exists
								string zip = "";
								if (flattenedData.ContainsKey("ClientAddress3"))
								{
									string addr3 = flattenedData["ClientAddress3"].ToString();
									// Extract ZIP code pattern (5 digits, optionally followed by dash and 4 more digits)
									var zipMatch = System.Text.RegularExpressions.Regex.Match(addr3, @"\b\d{5}(-\d{4})?\b");
									if (zipMatch.Success)
									{
										zip = zipMatch.Value;
										Console.WriteLine($"Extracted ZIP code {zip} from ClientAddress3");
									}
								}
								
								// If we have a ZIP code and it's not already in ClientAddress2, append it
								if (!string.IsNullOrEmpty(zip) && !value.Contains(zip))
								{
									// Check if value already ends with a state abbreviation (2 uppercase letters)
									var stateMatch = System.Text.RegularExpressions.Regex.Match(value, @"\b[A-Z]{2}\b$");
									if (stateMatch.Success)
									{
										// Insert ZIP after state with a space
										value = value + " " + zip;
										Console.WriteLine($"Appended ZIP code to ClientAddress2: {value}");
									}
									else
									{
										// Just append ZIP if no state found
										value = value + " " + zip;
										Console.WriteLine($"Appended ZIP code to ClientAddress2 (no state found): {value}");
									}
								}
							}
							
							// Special handling for client name and address fields - proper capitalization
							if (fieldName == "ClientName" || fieldName.StartsWith("ClientAddress") || 
								fieldName == "ContactFirstName" || fieldName == "ContactLastName")
							{
								value = Utils.ProperCapitalize(value);
								Console.WriteLine($"Applied proper capitalization to {fieldName}: {value}");
							}
							
							// Store the computed value.
							fieldValues[fieldName] = value;
							Console.WriteLine($"Setting value for {title}: {value}");
						}
						else
						{
							Console.WriteLine($"Reusing stored value for {title}: {value}");
						}
						// Update the control with the value.
						Utils.SetContentControlText(ctrl, value);
					}
				}
                // --- End Processing Remaining Content Controls ---

                // After processing remaining content controls, call FinalTweaks
                Utils.FinalTweaks(doc, fieldValues, data as JObject ?? new JObject());

                document.Save();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error opening DOCX file: " + ex.Message);
            Console.WriteLine("Please verify that the template file is a valid DOCX document.");
            return;
        }

        Console.WriteLine($"Document saved to {outputPath}");
    }
}

// ------------------------------
// Program entry point.
public static class Program
{
    public static void Main()
    {
        // Hard-coded paths.
        string baseDir = Directory.GetCurrentDirectory();
        string jsonPath = Path.Combine(baseDir, "output", "temp.json");
        string templatePath = Path.Combine(baseDir, "docbuild-template.docx");
        string outputPath = Path.Combine(baseDir, "output", "output.docx");

        // Create output directory if it doesn't exist
        Directory.CreateDirectory(Path.Combine(baseDir, "output"));

        string jsonContent = File.ReadAllText(jsonPath);
        JObject jsonData = JObject.Parse(jsonContent);

        // Extract additional endorsements from Form Recognizer output
        Utils.ExtractEndorsementsFromFormRecognizer(jsonData);

        // Merge additional endorsements before processing the document
        Utils.MergeAdditionalEndorsements(jsonData);

        Processor.FillDocxTemplate(templatePath, jsonData, outputPath);
    }
}

// Execute the entry point.
Program.Main();
