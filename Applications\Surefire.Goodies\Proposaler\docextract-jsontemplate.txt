{"InsurancePolicy": {"ClientDetails": {"ClientAddress": {"ClientAddressLine1": {"Description": "Street address or PO Box"}, "ClientAddressLine2": {"Description": "City and State"}, "ClientAddressLine3": {"Description": "ZIP code"}}, "ClientName": {"Description": "Full name of the insured business or entity"}, "ContactFirstName": {"Description": "Name of the business owner or primary contact"}, "TodaysDate": {"Description": "Date this document was generated"}}, "Coverages": {"Coverage": [{"CoverageName": {"Description": "The major lines of insurance in this quote/policy/proposal (e.g. General Liability, Contractors Pollution Coverage, Work Comp, Employer's Liability, etc.)"}, "CoverageRetroActiveDate": {"Description": "If Claims Made, the retroactive date of coverage"}, "CoverageType": {"Description": "Type of coverage trigger (e.g., Per Occurrence, Claims Made)"}, "Deductibles": {"Deductible": [{"DeductibleDollarAmount": {"Description": "Dollar amount of deductible"}, "DeductibleName": {"Description": "Name of deductible type (e.g., <PERSON>, General Liability)"}}]}, "Limits": {"Limit": [{"LimitDollarAmount": {"Description": "Limit in dollar amount which is usually anywhere from $10,000 to $10,000,000"}, "LimitName": {"Description": "Name of the coverage limit", "ExamplesForGeneralLiability": "Each Occurrence, General Aggregate, Products and Completed Operations, Damage to Premises Rented, Personal and Advertising Injury, etc."}}]}}]}, "Endorsements": {"Description": "ALL policy endorsements should be captured here. Extract EVERY endorsement mentioned in the document.", "Endorsement": [{"EndorsementName": {"Description": "The full name of the endorsement"}, "EndorsementNumber": {"Description": "The form number of the endorsement (e.g., CG 20 10 04 13)"}}]}, "Financials": {"MinimumEarnedDollarAmount": {"Description": "Minimum dollar amount earned by carrier, if blank, calculate using the MinimumEarnedPercentage against the PurePremiumDollarAmount"}, "MinimumEarnedPercentage": {"Description": "Minimum earned premium percentage, often 10-25%"}, "PurePremiumDollarAmount": {"Description": "Base or 'pure' premium before taxes and fees"}, "TaxFeeItems": {"Description": "ALL fees, taxes, and charges must be extracted as individual items. This is CRITICAL for document generation. Examples include State Tax, Surplus Lines Tax, Stamping Fee, Policy Fee, Broker Fee, etc.", "TaxFeeItem": [{"TaxFeeItemDollarAmount": {"Description": "The exact dollar amount of the tax or fee (including any $ sign)"}, "TaxFeeItemName": {"Description": "The exact name of the tax/fee as it appears in the document (e.g., Surplus Lines Tax, State Tax, Policy Fee, Broker Fee, etc.)"}}]}, "TotalDepositDollarAmount": {"Description": "Deposit required for policy inception"}, "TotalDownPayment": {"Description": "Down payment including all taxes, fees, and premium percentage"}, "TotalPolicyCost": {"Description": "Grand total cost of policy including all premium, taxes, and fees"}, "TotalTaxesAndFeesDollarAmount": {"Description": "Total taxes and fees charged"}}, "LegalNotices": {"AdditionalDisclaimers": {"Description": "Additional important information regarding the policy"}, "AdditionalTermsAndConditions": {"Description": "Legal terms, conditions, and disclaimers of the policy"}, "PrerequisitesNeededToBind": {"Description": "Items needed by the underwriter prior to, during or after binding. (e.g., Signed and Dated ACORD Applications, Financial Statements, D-1 Form, SL-2 Form, Signed and Completed Supplemental Questionnaire, etc.)"}}, "Locations": {"Location": [{"LocationDescription": {"Description": "Name or context of the location. e.g., Headquarters, Primary, Mailing Address, etc."}, "LocationFullAddress": {"Description": "Full address of insured location"}, "LocationNumber": {"Description": "Location identifier"}}]}, "PolicyDetails": {"CarrierName": {"Description": "Insurance carrier providing coverage (not wholesaler or broker)"}, "CarrierRating": {"Description": "Financial strength rating of the carrier (e.g., A, A+, etc.)"}, "EffectiveDate": {"Description": "Date the policy becomes active"}, "ExpirationDate": {"Description": "Date the policy expires"}, "PolicyTermLength": {"Description": "The term length of the policy (It is always either 12 Months or 6 Months)"}, "PrimaryCoverage": {"Description": "The primary name of the coverage quoted (e.g., General Liability, Professional Liability, Worker's Compensation, Umbrella, Excess, etc.)"}, "QuoteValidUntil": {"Description": "The date that the quote is valid until"}, "RenewalOfPolicyNumber": {"Description": "The previous term's expiring policy number"}}, "RatingBasises": {"RatingBasis": [{"GrossRate": {"Description": "Rate after surcharges, fees, and adjustments (e.g., 2.23 for General Liability)"}, "LocationNumber": {"Description": "Location identifier (e.g., 1, 2)"}, "NetRate": {"Description": "Base rate applied before surcharges (e.g., 1.34 for General Liability)"}, "NumberOfFullTimeEmployees": {"Description": "Number of full-time employees (only for Work Comp)"}, "NumberOfPartTimeEmployees": {"Description": "Number of part-time employees (only for Work Comp)"}, "RateBasis": {"Description": "How the rate is applied (e.g., Per $1k, Gross Sales)"}, "RateClassCode": {"Description": "Class Code for rating that can be a proprietary code, WCIRB work comp class code, SIC code, NAIC code, etc (e.g., 3267-01, 378302)"}, "RateDescription": {"Description": "Class description (e.g., Rentals Stores - Not Otherwise Classified)"}, "RateExposure": {"Description": "Exposure amount (e.g., $50,000 for Gross Sales, $345k for Subcontracting Expenses)"}, "RatePremium": {"Description": "Total premium calculated from rate and exposure (e.g., $2,345)"}}]}}}