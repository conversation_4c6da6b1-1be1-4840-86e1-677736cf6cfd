import os
import json
import tempfile
import re
import time
import logging
import subprocess
import shutil
from datetime import datetime
from dotenv import load_dotenv
from azure.ai.formrecognizer import DocumentAnalysisClient
from azure.core.credentials import AzureKeyCredential
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
from openai import OpenAI
import tqdm
import colorama
from colorama import Fore, Style

# Initialize colorama for colored terminal output
colorama.init()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(".debug/extractor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Azure Form Recognizer credentials
FORM_ENDPOINT = os.getenv("AZURE_FORM_RECOGNIZER_ENDPOINT")
FORM_KEY = os.getenv("AZURE_FORM_RECOGNIZER_KEY")

# OpenAI API credentials
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# Initialize OpenAI client with the new API
openai_client = OpenAI(api_key=OPENAI_API_KEY)

# Initialize Azure Form Recognizer client
document_analysis_client = DocumentAnalysisClient(
    endpoint=FORM_ENDPOINT, credential=AzureKeyCredential(FORM_KEY)
) if FORM_ENDPOINT and FORM_KEY else None

def print_step(step_number, step_description):
    """
    Print a formatted step header to the console.
    
    :param step_number: The step number.
    :param step_description: Description of the step.
    """
    print(f"\n{Fore.GREEN}=== STEP {step_number}: {step_description} ==={Style.RESET_ALL}\n")

def process_pdf(file_path):
    """
    Processes a PDF file by splitting it into pages, sending each page to Azure Form Recognizer,
    and combining the results.
    
    :param file_path: Path to the PDF file.
    :return: Combined Form Recognizer result.
    """
    print_step(1, "Data Extraction from PDF")
    
    pdf_reader = PdfReader(file_path)
    num_pages = len(pdf_reader.pages)
    combined_result = None
    
    logger.info(f"Processing PDF with {num_pages} pages: {file_path}")
    
    # Create a progress bar
    progress_bar = tqdm.tqdm(total=num_pages, desc="Processing PDF pages", unit="page")
    
    for page_num in range(num_pages):
        logger.info(f"Processing page {page_num + 1} of {num_pages}...")
        progress_bar.update(1)
        
        # Write the current page to a temporary PDF file.
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_page:
            page_tmp_path = tmp_page.name
            pdf_writer = PdfWriter()
            pdf_writer.add_page(pdf_reader.pages[page_num])
            with open(page_tmp_path, 'wb') as f_out:
                pdf_writer.write(f_out)

        try:
            # Read the temporary page and send it to Form Recognizer.
            with open(page_tmp_path, 'rb') as f:
                poller = document_analysis_client.begin_analyze_document("prebuilt-document", f.read())
            
            # Add a small progress indicator while waiting for the API response
            print(f"  {Fore.YELLOW}Waiting for Form Recognizer to process page {page_num + 1}...{Style.RESET_ALL}")
            result = poller.result()
            
            # Combine the results from each page.
            if combined_result is None:
                combined_result = result
            else:
                # Extend the results with the new page data
                combined_result.pages.extend(result.pages)
                combined_result.tables.extend(result.tables)
                combined_result.key_value_pairs.extend(result.key_value_pairs)
                
            logger.info(f"Successfully processed page {page_num + 1}")
            
        except Exception as e:
            logger.error(f"Error processing page {page_num + 1}: {str(e)}")
            print(f"  {Fore.RED}Error processing page {page_num + 1}: {str(e)}{Style.RESET_ALL}")
        finally:
            # Clean up the temporary file.
            try:
                if os.path.exists(page_tmp_path):
                    os.unlink(page_tmp_path)
            except Exception as e:
                logger.warning(f"Could not delete temporary file {page_tmp_path}: {str(e)}")
    
    progress_bar.close()
    return combined_result

def extract_raw_data(form_result):
    """
    Extracts raw data from the Form Recognizer result into a structured JSON format.
    
    :param form_result: Result from Azure Form Recognizer.
    :return: Dictionary with extracted data.
    """
    print_step(1.5, "Converting Form Recognizer results to structured data")
    
    # Initialize the data structure
    raw_data = {
        "metadata": {
            "extraction_timestamp": datetime.now().isoformat(),
            "page_count": len(form_result.pages) if hasattr(form_result, "pages") else 0,
        },
        "key_value_pairs": [],
        "tables": [],
        "text_content": []
    }
    
    # Extract key-value pairs
    if hasattr(form_result, "key_value_pairs"):
        for kv in form_result.key_value_pairs:
            if kv.key and kv.value:
                raw_data["key_value_pairs"].append({
                    "key": kv.key.content,
                    "value": kv.value.content,
                    "confidence": kv.confidence if hasattr(kv, "confidence") else None
                })
    
    # Extract tables
    if hasattr(form_result, "tables"):
        for table_idx, table in enumerate(form_result.tables):
            table_data = {
                "table_id": table_idx,
                "row_count": table.row_count,
                "column_count": table.column_count,
                "cells": []
            }
            
            for cell in table.cells:
                table_data["cells"].append({
                    "row_index": cell.row_index,
                    "column_index": cell.column_index,
                    "content": cell.content,
                    "row_span": cell.row_span,
                    "column_span": cell.column_span
                })
            
            raw_data["tables"].append(table_data)
    
    # Extract text content from pages
    if hasattr(form_result, "pages"):
        for page_idx, page in enumerate(form_result.pages):
            page_data = {
                "page_number": page_idx + 1,
                "lines": []
            }
            
            for line in page.lines:
                page_data["lines"].append({
                    "content": line.content,
                    "bounding_box": line.polygon if hasattr(line, "polygon") else None
                })
            
            raw_data["text_content"].append(page_data)
    
    logger.info(f"Extracted {len(raw_data['key_value_pairs'])} key-value pairs, {len(raw_data['tables'])} tables, and text from {len(raw_data['text_content'])} pages")
    return raw_data

def minify_data(raw_data):
    """
    Minifies the raw data by removing redundant, irrelevant, and obviously worthless parts.
    
    :param raw_data: Raw data extracted from Form Recognizer.
    :return: Minified data.
    """
    print_step(2, "Data Minification")
    
    logger.info("Starting data minification process")
    
    # Create a copy of the raw data to avoid modifying the original
    minified_data = {
        "metadata": raw_data["metadata"],
        "key_value_pairs": [],
        "tables": [],
        "text_content": []
    }
    
    # Filter key-value pairs based on confidence and relevance
    for kv in raw_data["key_value_pairs"]:
        # Skip empty or very low confidence key-value pairs
        if not kv["value"] or (kv["confidence"] is not None and kv["confidence"] < 0.5):
            continue
            
        # Skip irrelevant key-value pairs (customize based on your needs)
        key_lower = kv["key"].lower()
        if any(term in key_lower for term in ["page", "document", "footer", "header"]):
            continue
            
        minified_data["key_value_pairs"].append(kv)
    
    # Filter tables to keep only those with meaningful content
    for table in raw_data["tables"]:
        # Skip tables with very few cells
        if len(table["cells"]) < 4:  # Arbitrary threshold, adjust as needed
            continue
            
        # Check if table has meaningful content
        has_meaningful_content = False
        for cell in table["cells"]:
            if cell["content"] and len(cell["content"].strip()) > 3:  # Skip tables with mostly empty cells
                has_meaningful_content = True
                break
                
        if has_meaningful_content:
            minified_data["tables"].append(table)
    
    # Filter text content to keep only meaningful lines
    for page in raw_data["text_content"]:
        page_data = {
            "page_number": page["page_number"],
            "lines": []
        }
        
        for line in page["lines"]:
            # Skip very short lines or lines that are likely headers/footers
            content = line["content"].strip()
            if len(content) < 3 or re.match(r"^page \d+$|^\d+$", content.lower()):
                continue
                
            page_data["lines"].append({
                "content": content,
                # Omit bounding box to reduce size
            })
        
        if page_data["lines"]:
            minified_data["text_content"].append(page_data)
    
    # Calculate reduction in size
    original_size = len(json.dumps(raw_data))
    minified_size = len(json.dumps(minified_data))
    reduction_percent = ((original_size - minified_size) / original_size) * 100
    
    logger.info(f"Data minification complete. Size reduced by {reduction_percent:.2f}% ({original_size} -> {minified_size} bytes)")
    print(f"  {Fore.CYAN}Data minified: {len(minified_data['key_value_pairs'])} key-value pairs, {len(minified_data['tables'])} tables, and text from {len(minified_data['text_content'])} pages{Style.RESET_ALL}")
    print(f"  {Fore.CYAN}Size reduction: {reduction_percent:.2f}%{Style.RESET_ALL}")
    
    return minified_data

def load_json_template():
    """
    Loads the JSON template from the file.
    
    :return: JSON template as a dictionary.
    """
    try:
        with open("docextract-jsontemplate.txt", "r", encoding="utf-8") as f:
            template = json.loads(f.read())
        return template
    except Exception as e:
        logger.error(f"Error loading JSON template: {str(e)}")
        raise

def load_openai_prompt():
    """
    Loads the OpenAI prompt from the file.
    
    :return: OpenAI prompt as a string.
    """
    try:
        with open("docextract-prompt.txt", "r", encoding="utf-8") as f:
            prompt = f.read()
        return prompt
    except Exception as e:
        logger.error(f"Error loading OpenAI prompt: {str(e)}")
        raise

def refine_data_with_openai(minified_data, json_template, openai_prompt):
    """
    Refines the minified data using OpenAI API.
    
    :param minified_data: Minified data from the previous step.
    :param json_template: JSON template for the output structure.
    :param openai_prompt: Prompt for OpenAI.
    :return: Refined data from OpenAI.
    """
    print_step(3, "Data Refinement via OpenAI")
    
    logger.info("Starting data refinement with OpenAI")
    
    # Prepare the input for OpenAI
    # Convert the minified data to a more readable format for the AI
    formatted_data = json.dumps(minified_data, indent=2)
    
    # Create a more explicit prompt that includes the template structure
    template_str = json.dumps(json_template, indent=2)
    enhanced_prompt = f"""
You are an expert in commercial insurance and a data interpreter specializing in processing and structuring insurance policy data for brokers.

Your task is to analyze the extracted insurance policy data and convert it into a structured JSON format that matches the template provided below.

EXTRACTED DATA:
{formatted_data}

TARGET JSON TEMPLATE:
{template_str}

Please fill in the values in the template with relevant information from the extracted data. 
For each field, look for matching or related information in the extracted data.
If a value cannot be found, leave it as an empty string.
Return ONLY the completed JSON structure without any additional text or explanations.
"""
    
    # Create the messages for the OpenAI API
    messages = [
        {"role": "system", "content": openai_prompt},
        {"role": "user", "content": enhanced_prompt}
    ]
    
    # For debugging, save the prompt to a file
    debug_prompt_path = ".debug/debug_openai_prompt.txt"
    with open(debug_prompt_path, "w", encoding="utf-8") as f:
        f.write(enhanced_prompt)
    print(f"  {Fore.CYAN}Saved debug prompt to {debug_prompt_path}{Style.RESET_ALL}")
    
    try:
        print(f"  {Fore.YELLOW}Sending request to OpenAI API...{Style.RESET_ALL}")
        
        # Call the OpenAI API using the new client
        response = openai_client.chat.completions.create(
            model="gpt-4-turbo",  # Use an appropriate model
            messages=messages,
            temperature=0.2,  # Lower temperature for more deterministic output
            max_tokens=4096  # Adjust based on your needs
        )
        
        # Extract the response content
        ai_response = response.choices[0].message.content
        
        # Save the raw response for debugging
        debug_response_path = ".debug/debug_openai_response.txt"
        with open(debug_response_path, "w", encoding="utf-8") as f:
            f.write(ai_response)
        print(f"  {Fore.CYAN}Saved raw OpenAI response to {debug_response_path}{Style.RESET_ALL}")
        
        # Try to parse the response as JSON
        try:
            # Extract JSON from the response (in case there's additional text)
            json_match = re.search(r'```json\n([\s\S]*?)\n```|({[\s\S]*})', ai_response)
            if json_match:
                json_str = json_match.group(1) or json_match.group(2)
                refined_data = json.loads(json_str)
            else:
                refined_data = json.loads(ai_response)
                
            logger.info("Successfully refined data with OpenAI")
            print(f"  {Fore.GREEN}Data successfully refined with OpenAI{Style.RESET_ALL}")
            
            return refined_data
            
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing OpenAI response as JSON: {str(e)}")
            logger.error(f"Raw response: {ai_response}")
            print(f"  {Fore.RED}Error parsing OpenAI response as JSON. See log for details.{Style.RESET_ALL}")
            
            # As a fallback, try to create a structure that matches the template
            return {"InsurancePolicy": json_template["InsurancePolicy"]}
            
    except Exception as e:
        logger.error(f"Error calling OpenAI API: {str(e)}")
        print(f"  {Fore.RED}Error calling OpenAI API: {str(e)}{Style.RESET_ALL}")
        
        # Return the template structure as a fallback
        return {"InsurancePolicy": json_template["InsurancePolicy"]}

def save_json(data, original_filename, step_name):
    """
    Saves the JSON data to a file in an output directory, appending a timestamp to the filename.
    
    :param data: JSON-serializable dictionary.
    :param original_filename: The original filename of the processed PDF.
    :param step_name: Name of the processing step (for the filename).
    :return: The path to the saved JSON file.
    """
    os.makedirs("output", exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_filename = os.path.splitext(os.path.basename(original_filename))[0]
    json_filename = f"output/{base_filename}_{step_name}_{timestamp}.json"
    
    with open(json_filename, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
        
    logger.info(f"Saved {step_name} data to {json_filename}")
    print(f"  {Fore.GREEN}Saved {step_name} data to {json_filename}{Style.RESET_ALL}")
    
    return json_filename

def load_json_file(file_path):
    """
    Loads JSON data from a file.
    
    :param file_path: Path to the JSON file.
    :return: Loaded JSON data as a dictionary.
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        return data
    except Exception as e:
        logger.error(f"Error loading JSON file {file_path}: {str(e)}")
        raise

def is_json_file(file_path):
    """
    Checks if a file is a JSON file.
    
    :param file_path: Path to the file.
    :return: True if the file is a JSON file, False otherwise.
    """
    return file_path.lower().endswith('.json')

def get_latest_json_file(output_dir="output", file_type="final"):
    """
    Gets the most recent JSON file of specified type from the output directory.
    
    :param output_dir: Directory to search in
    :param file_type: Type of JSON file to look for ("raw", "minified", or "final")
    :return: Path to the most recent JSON file, or None if not found
    """
    if not os.path.exists(output_dir):
        return None
        
    json_files = [f for f in os.listdir(output_dir) 
                  if f.endswith('.json') and f'_{file_type}_' in f]
    
    if not json_files:
        return None
        
    # Sort by modification time (most recent first)
    json_files.sort(key=lambda x: os.path.getmtime(os.path.join(output_dir, x)), 
                   reverse=True)
    
    return os.path.join(output_dir, json_files[0])

def run_csx_script():
    """
    Runs the 0-main.csx script with a standardized JSON file path.
    
    :return: True if successful, False otherwise.
    """
    print_step(5, "Building Document with CSX Script")
    
    try:
        # Get the latest final JSON file
        latest_json = get_latest_json_file(file_type="final")
        if not latest_json:
            print(f"  {Fore.RED}No final JSON file found in output directory{Style.RESET_ALL}")
            logger.error("No final JSON file found in output directory")
            return False
            
        print(f"  {Fore.CYAN}Using JSON file: {latest_json}{Style.RESET_ALL}")
        
        # Copy to a predictable location/name in the output directory
        os.makedirs("output", exist_ok=True)
        temp_json_path = "output/temp.json"
        shutil.copy2(latest_json, temp_json_path)
        print(f"  {Fore.CYAN}Copied to {temp_json_path} for processing{Style.RESET_ALL}")
        
        # Assuming you have the dotnet-script tool installed
        cmd = ["dotnet-script", "0-main.csx"]
        
        print(f"  {Fore.YELLOW}Executing: {' '.join(cmd)}{Style.RESET_ALL}")
        logger.info(f"Executing CSX script: {' '.join(cmd)}")
        
        # Run the process and capture output
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Print output in real-time
        for line in iter(process.stdout.readline, ''):
            if not line:
                break
            print(f"  {line.strip()}")
            logger.info(f"CSX output: {line.strip()}")
        
        # Wait for the process to complete
        return_code = process.wait()
        
        if return_code == 0:
            print(f"  {Fore.GREEN}Document build completed successfully{Style.RESET_ALL}")
            logger.info("Document build completed successfully")
            return True
        else:
            # Capture error output
            error_output = process.stderr.read()
            print(f"  {Fore.RED}Document build failed with code {return_code}{Style.RESET_ALL}")
            print(f"  {Fore.RED}Error: {error_output}{Style.RESET_ALL}")
            logger.error(f"Document build failed with code {return_code}")
            logger.error(f"Error output: {error_output}")
            return False
            
    except Exception as e:
        print(f"  {Fore.RED}Error executing CSX script: {str(e)}{Style.RESET_ALL}")
        logger.error(f"Error executing CSX script: {str(e)}", exc_info=True)
        return False
    finally:
        logger.info("CSX script execution completed")
        # Clean up the temp file if it exists
        # if os.path.exists("output/temp.json"):
        #     try:
        #         os.remove("output/temp.json")
        #         logger.info("Removed temporary JSON file")
        #     except Exception as e:
        #         logger.warning(f"Could not remove temporary JSON file: {str(e)}")

def main(input_path=None, bypass_form_recognizer=False, build_doc=True):
    """
    Main processing function that extracts data from a PDF, minifies it, refines it with OpenAI,
    and saves the resulting JSON, then builds the document using CSX.
    
    :param input_path: Path to the PDF file or JSON file to be processed.
    :param bypass_form_recognizer: Whether to bypass the Form Recognizer step.
    :param build_doc: Whether to build a document using the CSX script (defaults to True).
    """
    try:
        # If only building document, skip all other steps
        if build_doc and not input_path:
            print(f"{Fore.CYAN}Skipping processing steps and building document only...{Style.RESET_ALL}")
            logger.info("Skipping processing steps and building document only")
            run_csx_script()
            return
            
        # Normal processing flow
        start_time = time.time()
        
        print(f"{Fore.CYAN}Starting insurance policy data extraction process...{Style.RESET_ALL}")
        logger.info(f"Starting processing of: {input_path}")
        
        # Check if the input is a JSON file or if bypass_form_recognizer is True
        if bypass_form_recognizer or is_json_file(input_path):
            if is_json_file(input_path):
                print(f"{Fore.CYAN}Input is a JSON file. Bypassing Form Recognizer step.{Style.RESET_ALL}")
                logger.info(f"Input is a JSON file: {input_path}. Bypassing Form Recognizer step.")
                
                # Load the JSON file
                data = load_json_file(input_path)
                
                # Determine if it's raw or minified data
                if "_raw_" in input_path:
                    print(f"{Fore.CYAN}Detected raw data. Proceeding with minification.{Style.RESET_ALL}")
                    raw_data = data
                    minified_data = minify_data(raw_data)
                    minified_json_path = save_json(minified_data, input_path, "minified")
                elif "_minified_" in input_path:
                    print(f"{Fore.CYAN}Detected minified data. Proceeding with OpenAI refinement.{Style.RESET_ALL}")
                    minified_data = data
                    minified_json_path = input_path
                else:
                    # Assume it's raw data if not specified
                    print(f"{Fore.YELLOW}Could not determine data type. Assuming raw data.{Style.RESET_ALL}")
                    raw_data = data
                    minified_data = minify_data(raw_data)
                    minified_json_path = save_json(minified_data, input_path, "minified")
            else:
                print(f"{Fore.CYAN}Bypassing Form Recognizer step as requested.{Style.RESET_ALL}")
                logger.info("Bypassing Form Recognizer step as requested.")
                
                # Look for the most recent minified JSON file in the output directory
                output_dir = "output"
                if os.path.exists(output_dir):
                    json_files = [f for f in os.listdir(output_dir) if f.endswith('.json') and '_minified_' in f]
                    if json_files:
                        # Sort by modification time (most recent first)
                        json_files.sort(key=lambda x: os.path.getmtime(os.path.join(output_dir, x)), reverse=True)
                        minified_json_path = os.path.join(output_dir, json_files[0])
                        print(f"{Fore.CYAN}Using most recent minified JSON file: {minified_json_path}{Style.RESET_ALL}")
                        logger.info(f"Using most recent minified JSON file: {minified_json_path}")
                        minified_data = load_json_file(minified_json_path)
                    else:
                        raise ValueError("No minified JSON files found in the output directory. Cannot bypass Form Recognizer step.")
                else:
                    raise ValueError("Output directory not found. Cannot bypass Form Recognizer step.")
        else:
            # Process the PDF with Form Recognizer
            form_result = process_pdf(input_path)
            
            # Convert Form Recognizer result to structured data
            raw_data = extract_raw_data(form_result)
            
            # Save the raw data
            raw_json_path = save_json(raw_data, input_path, "raw")
            
            # Step 2: Minify the data
            minified_data = minify_data(raw_data)
            
            # Save the minified data
            minified_json_path = save_json(minified_data, input_path, "minified")
        
        # Load the JSON template and OpenAI prompt
        json_template = load_json_template()
        openai_prompt = load_openai_prompt()
        
        # Step 3: Refine the data using OpenAI
        refined_data = refine_data_with_openai(minified_data, json_template, openai_prompt)
        
        # Step 4: Save the final JSON
        print_step(4, "Save Final JSON")
        final_json_path = save_json(refined_data, input_path, "final")
        
        # After all processing is done, run CSX script by default
        if build_doc:
            run_csx_script()
        
        # Calculate and display processing time
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n{Fore.GREEN}Processing complete!{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Total processing time: {processing_time:.2f} seconds{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Final output saved to: {final_json_path}{Style.RESET_ALL}")
        
        logger.info(f"Processing complete. Total time: {processing_time:.2f} seconds")
        logger.info(f"Final output saved to: {final_json_path}")
        
    except Exception as e:
        logger.error(f"Error in main processing: {str(e)}", exc_info=True)
        print(f"\n{Fore.RED}Error: {str(e)}{Style.RESET_ALL}")
        print(f"{Fore.RED}See log file for details.{Style.RESET_ALL}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Extract and process insurance policy data from PDFs.")
    parser.add_argument("input_path", nargs="?", help="Path to the PDF file or JSON file to be processed.")
    parser.add_argument("--bypass-form-recognizer", action="store_true", 
                       help="Bypass the Form Recognizer step and use the most recent minified JSON file.")
    parser.add_argument("--no-build-doc", action="store_true",  # Changed to --no-build-doc
                       help="Skip building document using the CSX script.")
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.input_path and not args.no_build_doc:  # Updated condition
        parser.error("input_path is required unless only building document")
    
    main(args.input_path, args.bypass_form_recognizer, not args.no_build_doc)  # Inverted the flag 