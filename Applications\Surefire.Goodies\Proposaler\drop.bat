@echo off
setlocal

:: Check if a file was provided via drag-and-drop
if "%~1"=="" (
    echo Please drag and drop a PDF file onto this script.
    pause
    exit /b
)

:: Get the full path of the dropped file
set "PDF_FILE=%~1"

:: Change to the directory of this script (if needed)
cd /d "%~dp0"

:: Run the Python script with the provided PDF file
python docextract.py "%PDF_FILE%"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Document ready, press any key to open it now. Remember to save your new Word file or it will be overwritten the next time you run this program.
    pause >nul
    start "" "output\output.docx"
)

:: Wait for user input before closing
echo Process completed.
pause
