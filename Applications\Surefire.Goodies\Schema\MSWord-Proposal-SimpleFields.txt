```---DISCLOSURE PAGE
varMinimumEarnedPercentage		computed  (Default=25%, copy from fieldMinimumEarnedPercentage
varTotalTaxesAndFeesDollarAmount	computed  (Sum of all fieldTaxFeeItemName items, plus the fieldMetroRetailBrokerFee)
varTotalDepositDollarAmount		computed  (fieldMinimumEarnedPercentage % of varTotalPurePremium, plus varTotalTaxesAndFeesDollarAmount)


```---PAYMENT PAGE
```Premiums
fieldPurePremiumDollarAmount		EDITABLE
varTotalPurePremium			computed (fieldPurePremiumDollarAmount)


```Taxes and Fees
(LOOPABLE)
  fieldTaxFeeItemDollarAmount		EDITABLE 
  fieldTaxFeeItemName			EDITABLE
(ENDLOOPABLE)
fieldMetroRetailBrokerFee		EDITABLE
varTotalTaxesAndFeesDollarAmount	computed (Sum of all fieldTaxFeeItemName items, plus the fieldMetroRetailBrokerFee)
varTotalPolicyCost			computed (varTotalTaxesAndFeesDollarAmount plus varTotalPurePremium)

```Finance Payment
fieldMinimumEarnedPercentage		EDITABLE
varMinimumEarnedDollarAmount		computed (fieldMinimumEarnedPercentage % of varTotalPurePremium)
varTotalDepositDollarAmount		computed (fieldMinimumEarnedPercentage % of varTotalPurePremium, plus varTotalTaxesAndFeesDollarAmount)

```Full Pay
varTotalPurePremium			computed (fieldPurePremiumDollarAmount)
varTotalTaxesAndFeesDollarAmount	computed (Sum of all fieldTaxFeeItemName items, plus the fieldMetroRetailBrokerFee)
varTotalPolicyCost			computed (varTotalTaxesAndFeesDollarAmount plus varTotalPurePremium)

```Fine Print
varMetroRetailBrokerFee			computed (fieldMetroRetailBrokerFee)