﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Schema\extractor.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Schema\extractor.json">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Installer\" />
    <Folder Include="Proposaler\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Macros\.old\pro_updateProposal_v001 - Copy.bas" />
    <None Include="Macros\.old\pro_updateProposal_v013.cls" />
    <None Include="Macros\.old\pro_updateProposal_v200.cls" />
  </ItemGroup>

</Project>
