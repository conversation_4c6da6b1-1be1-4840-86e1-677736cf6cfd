using Microsoft.Office.Tools.Ribbon;
using System;

namespace Surefire.Outlook
{
    public partial class Ribbon : Microsoft.Office.Core.IRibbonExtensibility
    {
        private Microsoft.Office.Core.IRibbonUI ribbon;

        public string GetCustomUI(string ribbonID)
        {
            return @"
                <customUI xmlns='http://schemas.microsoft.com/office/2009/07/customui'>
                    <ribbon>
                        <tabs>
                            <tab idMso='TabAddIns'>
                                <group id='SurefireGroup' label='Surefire Tools'>
                                    <toggleButton id='toggleTaskPane'
                                                label='Show/Hide Tools'
                                                onAction='OnToggleTaskPane'
                                                getPressed='GetTaskPaneVisible'
                                                size='large'
                                                />
                                </group>
                            </tab>
                        </tabs>
                    </ribbon>
                </customUI>";
        }

        public void OnToggleTaskPane(Microsoft.Office.Core.IRibbonControl control, bool pressed)
        {
            var taskPane = Globals.ThisAddIn.CustomTaskPanes[0];
            if (taskPane != null)
            {
                taskPane.Visible = pressed;
            }
        }

        public bool GetTaskPaneVisible(Microsoft.Office.Core.IRibbonControl control)
        {
            var taskPane = Globals.ThisAddIn.CustomTaskPanes[0];
            return taskPane != null && taskPane.Visible;
        }

        public void Ribbon_Load(Microsoft.Office.Core.IRibbonUI ribbonUI)
        {
            this.ribbon = ribbonUI;
        }
    }
} 