using System;
using System.Windows.Forms;
using System.Drawing;
using Outlook = Microsoft.Office.Interop.Outlook;
using System.Net.Http;
using System.Text.Json;
using System.Configuration;
using System.Threading.Tasks;

namespace Surefire.Outlook
{
    public partial class SurefireTaskPaneControl : UserControl
    {
        private Button btnSummarize;
        private TextBox txtOutput;
        private readonly HttpClient _httpClient;

        public SurefireTaskPaneControl()
        {
            InitializeComponent();
            InitializeCustomComponents();
            _httpClient = new HttpClient();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.Name = "SurefireTaskPaneControl";
            this.Size = new System.Drawing.Size(300, 600);
            this.ResumeLayout(false);
        }

        private void InitializeCustomComponents()
        {
            // Initialize Summarize button
            btnSummarize = new Button
            {
                Text = "Summarize",
                Location = new Point(10, 10),
                Size = new Size(100, 30)
            };
            btnSummarize.Click += BtnSummarize_Click;

            // Initialize output textbox
            txtOutput = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Location = new Point(10, 50),
                Size = new Size(280, 540),
                ReadOnly = true
            };

            // Add controls to the task pane
            this.Controls.Add(btnSummarize);
            this.Controls.Add(txtOutput);
        }

        private async void BtnSummarize_Click(object sender, EventArgs e)
        {
            try
            {
                var explorer = Globals.ThisAddIn.Application.ActiveExplorer();
                var selection = explorer.Selection;
                
                if (selection.Count > 0)
                {
                    var mailItem = selection[1] as Outlook.MailItem;
                    if (mailItem != null)
                    {
                        btnSummarize.Enabled = false;
                        txtOutput.Text = "Generating summary...";
                        
                        string summary = await SummarizeEmailAsync(mailItem.Body);
                        txtOutput.Text = summary;
                    }
                    else
                    {
                        txtOutput.Text = "Please select an email to summarize.";
                    }
                }
                else
                {
                    txtOutput.Text = "No email selected.";
                }
            }
            catch (Exception ex)
            {
                txtOutput.Text = $"Error: {ex.Message}";
            }
            finally
            {
                btnSummarize.Enabled = true;
            }
        }

        private async Task<string> SummarizeEmailAsync(string emailBody)
        {
            try
            {
                string apiKey = ConfigurationManager.AppSettings["OpenAIApiKey"];
                if (string.IsNullOrEmpty(apiKey))
                {
                    return "Error: OpenAI API key not found in configuration.";
                }

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");

                var requestBody = new
                {
                    model = "gpt-3.5-turbo",
                    messages = new[]
                    {
                        new
                        {
                            role = "system",
                            content = "You are a helpful assistant that summarizes emails concisely."
                        },
                        new
                        {
                            role = "user",
                            content = $"Please summarize this email concisely:\n\n{emailBody}"
                        }
                    },
                    max_tokens = 150
                };

                var response = await _httpClient.PostAsync(
                    "https://api.openai.com/v1/chat/completions",
                    new StringContent(JsonSerializer.Serialize(requestBody), System.Text.Encoding.UTF8, "application/json")
                );

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    var jsonResponse = JsonSerializer.Deserialize<JsonElement>(result);
                    return jsonResponse.GetProperty("choices")[0].GetProperty("message").GetProperty("content").GetString();
                }
                else
                {
                    return $"Error: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}";
                }
            }
            catch (Exception ex)
            {
                return $"Error generating summary: {ex.Message}";
            }
        }
    }
} 