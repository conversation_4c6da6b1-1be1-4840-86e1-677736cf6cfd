﻿<?xml version="1.0" encoding="UTF-8"?>
<customUI xmlns="http://schemas.microsoft.com/office/2009/07/customui">
  <ribbon>
    <tabs>
      <tab idMso="TabAddIns">
        <group id="SurefireGroup" label="Surefire Tools">
          <toggleButton id="toggleTaskPane"
                      label="Show/Hide Tools"
                      onAction="OnToggleTaskPane"
                      getPressed="GetTaskPaneVisible"
                      size="large"/>
        </group>
      </tab>
    </tabs>
  </ribbon>
</customUI>