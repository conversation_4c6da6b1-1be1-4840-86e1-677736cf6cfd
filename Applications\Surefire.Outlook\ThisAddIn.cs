﻿using Microsoft.Office.Tools;
using Microsoft.Office.Tools.Ribbon;
using Microsoft.Office.Core;
using System;
using System.Windows.Forms;
using System.Drawing;
using System.Net.Http;
using System.Text.Json;
using System.Configuration;
using System.Threading.Tasks;
using Microsoft.Office.Interop.Outlook;
using OutlookApp = Microsoft.Office.Interop.Outlook.Application;
using SystemException = System.Exception;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Resources;
using System.Reflection;
using System.Linq;
using System.IO;
using Markdig;
using System.Threading;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;
using System.Runtime.InteropServices;

namespace Surefire.Outlook
{
    public static class EmailPrompts
    {
        public const string FormattingInstructions = @"
Please format the output as follows:
- Use Markdown.
- For section headers, use bold (e.g., **Header:**).
- Use '-' for bullet points.
- Wrap text in *asterisks* for italics.
- Insert a newline after each section.
";

        public static string BriefPrompt => @"You are a helpful assistant specializing in Property & Casualty commercial insurance that provides concise email summaries.
Extract a concise overview of the main topic, key message, or request. Note important details like dates, policies, numbers, or specific details and end the summary with required actions or responses.
If there are any questions that need answering, provide suggested responses in a new paragraph.
" + FormattingInstructions;

        public static string RecapPrompt => @"Analyze the entire email chain, including the original email and all responses.
Extract the following:
1. SUMMARY: A comprehensive recap of the email thread including context, identifying whether the conversation is between:
   - Metro Insurance and a Carrier (e.g., underwriting questions, policy changes, quotes, endorsements)
   - Metro Insurance and a Client (e.g., policy service requests, renewals, certificates of insurance)
   - Metro Insurance internal staff (e.g., procedural discussions, internal assignments)
   - Metro Insurance and a Vendor (rare, but possible)

2. KEY DATA: Extract and list:
   - Client Name (if applicable)
   - Policy Information
   - Policy Number(s)
   - Policy Type(s) (General Liability, Workers' Comp, Commercial Auto, Property, etc.)
   - Key Discussion Points
   - Decisions Made or Pending Approvals

3. ACTION ITEMS: List tasks with:
   - Who they are assigned to
   - Deadlines if mentioned
   - Priority level
   - Whether the email is directed TO the user vs. if they are CC'd

4. PENDING QUESTIONS: List any questions that:
   - Require a response from Metro Insurance
   - Need clarification
   - Are awaiting answers from other parties
" + FormattingInstructions;

        public static string DraftReplyPrompt => @"You are an experienced commercial insurance broker that provides clients with insurance solutions. You also work with the carriers, 
wholesalers and underwriters to facilitate this. Your job is to understand the context of the email below and answer any questions, ask questions to get the information needed to hand over to
the carriers and underwriters, or facilitate getting the info the underwriters need to provide quotes and process requests from clients. Understanding who the email is from first is paramount - is it a client
asking a question about a policy? A potential new business opportunity trying to get a quote? An underwriter asking if we have some information they need to quote? A vendor or third party asking for 
information about a policy? Figure this out first, then try to understand if the need can be answered completely by you alone. If not, work your thinking into the email about what needs to take place to make 
this happen so that everyone can understand what's to happen next and what they might be waiting for. Read the entire email chain, but respond only as if you are responding to the person who sent the most recent email.
Cite the evidentiary data where you have it. Write the email as if it was from Nathan Smith.";

        public static string TomPrompt => @"You are a helpful assistant tasked with analyzing forwarded email chains from Tom, a senior insurance executive.

Step 1: Determine exactly what Tom is asking for by examining his brief instruction (usually at the top of the chain).
- Is he requesting a quote?
- Does he want a status update?
- Is he asking for a review/revision of an attached draft?
- Any other specific action?

Step 2: From the forwarded email chain, extract only the details necessary to fulfill Tom's request:
- Identify relevant client information (company name, policy numbers, coverage types)
- Highlight key points or instructions from other participants
- Summarize background context needed to complete the request

Format the output in two clear sections:
1. **Tom's Request:** [Clarify what he wants done in clear, actionable terms]
2. **Relevant Information:** [Bullet points of only the essential details needed]
" + FormattingInstructions;
    }

    public partial class ThisAddIn
    {
        private Microsoft.Office.Tools.CustomTaskPane _taskPane;
        private SurefireTaskPaneControl _taskPaneControl;
        private Ribbon _ribbon;
        private const int EXPANDED_WIDTH = 450;  // Changed default to 450
        private const int COLLAPSED_WIDTH = 50;
        private int _lastExpandedWidth = EXPANDED_WIDTH;  // This will now be 450

        // Add static property to access the task pane
        public static ThisAddIn Instance { get; private set; }
        public Microsoft.Office.Tools.CustomTaskPane TaskPane => _taskPane;

        private void ThisAddIn_Startup(object sender, System.EventArgs e)
        {
            try
            {
                Instance = this;  // Store instance for static access
                _taskPaneControl = new SurefireTaskPaneControl();
                _taskPane = this.CustomTaskPanes.Add(_taskPaneControl, "Surefire Tools");
                _taskPane.Width = COLLAPSED_WIDTH;  // Start collapsed instead of expanded
                _taskPane.Visible = true;
                
                // Connect the toggle event
                _taskPaneControl.OnToggleWidth += (s, args) => {
                    if (_taskPane.Width > SurefireTaskPaneControl.COLLAPSE_THRESHOLD)
                    {
                        // Store current width before collapsing
                        _lastExpandedWidth = _taskPane.Width;
                        _taskPane.Width = COLLAPSED_WIDTH;
                    }
                    else
                    {
                        // Restore to last expanded width
                        _taskPane.Width = _lastExpandedWidth;
                    }
                };

                // Track width changes through the control's size changes
                _taskPaneControl.SizeChanged += (s, args) => {
                    if (_taskPane.Width > SurefireTaskPaneControl.COLLAPSE_THRESHOLD)
                    {
                        _lastExpandedWidth = _taskPane.Width;
                    }
                };
            }
            catch (SystemException ex)
            {
                MessageBox.Show($"Error initializing add-in: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ThisAddIn_Shutdown(object sender, System.EventArgs e)
        {
            // Note: Outlook will automatically clean up task panes
        }

        protected override Microsoft.Office.Core.IRibbonExtensibility CreateRibbonExtensibilityObject()
        {
            _ribbon = new Ribbon();
            return _ribbon;
        }

        #region VSTO generated code
        private void InternalStartup()
        {
            this.Startup += new System.EventHandler(ThisAddIn_Startup);
            this.Shutdown += new System.EventHandler(ThisAddIn_Shutdown);
        }
        #endregion
    }

    public class SettingsForm : Form
    {
        private TrackBar sliderProfessionalism;
        private TrackBar sliderDetails;
        private TrackBar sliderUrgency;
        private Button btnOK;
        private Button btnCancel;

        public int Professionalism { get; private set; }
        public int Details { get; private set; }
        public int Urgency { get; private set; }

        public SettingsForm(int currentProfessionalism = 5, int currentDetails = 5, int currentUrgency = 5)
        {
            this.Text = "Response Settings";
            this.Size = new Size(400, 450);  // Increased height from 350 to 450
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.White;

            // Initialize with current values
            Professionalism = currentProfessionalism;
            Details = currentDetails;
            Urgency = currentUrgency;

            InitializeComponents();
        }

        private void InitializeComponents()
        {
            var titleLabel = new Label
            {
                Text = "Response Settings",
                Font = new Font("Segoe UI", 12f, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40
            };

            // Create slider titles and sliders with current values
            var profTitle = new Label
            {
                Text = "Professionalism",
                Font = new Font("Segoe UI", 10f, FontStyle.Bold),
                Location = new Point(10, 50),
                AutoSize = true
            };
            sliderProfessionalism = CreateSettingsSlider("Professionalism", "Frat House", "Royal Decree", 75, Professionalism);

            var detailsTitle = new Label
            {
                Text = "Level of Detail",
                Font = new Font("Segoe UI", 10f, FontStyle.Bold),
                Location = new Point(10, 165),
                AutoSize = true
            };
            sliderDetails = CreateSettingsSlider("Level of Detail", "Post-it Note", "PhD Dissertation", 190, Details);

            var urgencyTitle = new Label
            {
                Text = "Urgency",
                Font = new Font("Segoe UI", 10f, FontStyle.Bold),
                Location = new Point(10, 280),
                AutoSize = true
            };
            sliderUrgency = CreateSettingsSlider("Urgency", "Next Ice Age", "Life or Death", 305, Urgency);

            // Create buttons
            btnOK = new Button
            {
                Text = "OK",
                DialogResult = DialogResult.OK,
                Size = new Size(75, 30),
                Location = new Point(this.ClientSize.Width - 170, this.ClientSize.Height - 45),
                BackColor = ColorTranslator.FromHtml("#1e1e5e"),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnCancel = new Button
            {
                Text = "Cancel",
                DialogResult = DialogResult.Cancel,
                Size = new Size(75, 30),
                Location = new Point(this.ClientSize.Width - 85, this.ClientSize.Height - 45),
                BackColor = Color.White,
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Standard
            };

            this.Controls.AddRange(new Control[] {
                titleLabel,
                profTitle,
                sliderProfessionalism,
                CreateSliderLabels("Frat House", "Royal Decree", 75),
                detailsTitle,
                sliderDetails,
                CreateSliderLabels("Post-it Note", "PhD Dissertation", 190),
                urgencyTitle,
                sliderUrgency,
                CreateSliderLabels("Next Ice Age?", "Life or Death", 305),
                btnOK,
                btnCancel
            });

            this.AcceptButton = btnOK;
            this.CancelButton = btnCancel;
        }

        private TrackBar CreateSettingsSlider(string name, string leftLabel, string rightLabel, int top, int initialValue)
        {
            var slider = new TrackBar
            {
                Name = name,
                Minimum = 0,
                Maximum = 10,
                Value = initialValue,
                Width = 360,
                Location = new Point(10, top),
                TickFrequency = 1,
                TickStyle = TickStyle.BottomRight
            };

            slider.ValueChanged += (s, e) => {
                switch (name)
                {
                    case "Professionalism":
                        Professionalism = slider.Value;
                        break;
                    case "Level of Detail":
                        Details = slider.Value;
                        break;
                    case "Urgency":
                        Urgency = slider.Value;
                        break;
                }
            };

            return slider;
        }

        private Panel CreateSliderLabels(string leftText, string rightText, int top)
        {
            var labelPanel = new Panel
            {
                Width = 360,
                Height = 40,
                Location = new Point(10, top + 35),
                BackColor = Color.White,
                Padding = new Padding(0, 0, 0, 0)
            };

            var leftLabel = new Label
            {
                Text = leftText,
                AutoSize = true,
                Location = new Point(0, 10),
                Font = new Font("Segoe UI", 8f),
                ForeColor = Color.Gray,
                BackColor = Color.White
            };

            var rightLabel = new Label
            {
                Text = rightText,
                AutoSize = true,
                Font = new Font("Segoe UI", 8f),
                ForeColor = Color.Gray,
                BackColor = Color.White
            };

            rightLabel.Location = new Point(labelPanel.Width - rightLabel.PreferredWidth, 10);

            labelPanel.Controls.AddRange(new Control[] { leftLabel, rightLabel });
            return labelPanel;
        }
    }

    public class SurefireTaskPaneControl : UserControl
    {
        [DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(int nLeftRect, int nTopRect, int nRightRect, int nBottomRect, int nWidthEllipse, int nHeightEllipse);

        private Button btnBrief;
        private Button btnRecap;
        private Button btnDraftReply;
        private Button btnTom;
        private Button btnToggle;
        private Button btnSettings;
        private Panel settingsPanel;
        private TrackBar sliderProfessionalism;
        private TrackBar sliderDetails;
        private TrackBar sliderUrgency;
        private WebView2 webView;
        private RichTextBox fallbackTextBox;
        private PictureBox logoBox;
        private TableLayoutPanel mainLayout;
        private FlowLayoutPanel buttonPanel;
        private TextBox customPromptBox;
        private Button btnCustomPrompt;
        private Panel textBoxWrapper;
        private readonly HttpClient _httpClient;
        private bool useWebView = false;
        private int _lastExpandedWidth = 450;  // Changed default to 450
        private const int EXPANDED_WIDTH = 450;  // Changed default to 450
        private const int COLLAPSED_WIDTH = 50;
        public static readonly int COLLAPSE_THRESHOLD = 100;
        private FlowLayoutPanel collapsedButtonPanel;
        private bool settingsPanelVisible = false;

        // Add settings properties
        private int Professionalism { get; set; } = 5;
        private int Details { get; set; } = 5;
        private int Urgency { get; set; } = 5;

        public event EventHandler OnToggleWidth;

        public SurefireTaskPaneControl()
        {
            InitializeComponent();
            InitializeCustomComponents();
            LoadSettings(); // Load saved settings

            // Load event will handle WebView2 initialization
            this.Load += SurefireTaskPaneControl_Load;

            try
            {
                // Configure TLS
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;

                // Create handler with proxy and SSL configuration
                var handler = new HttpClientHandler
                {
                    UseProxy = true,
                    AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
                    ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) =>
                    {
                        // For debugging, accept all certificates
                        return true;
                    }
                };

                // Use system proxy if available
                handler.Proxy = WebRequest.GetSystemWebProxy();
                handler.Proxy.Credentials = CredentialCache.DefaultCredentials;

                _httpClient = new HttpClient(handler)
                {
                    Timeout = TimeSpan.FromSeconds(30)
                };

                // Set default headers
                _httpClient.DefaultRequestHeaders.Add("User-Agent", "Surefire.Outlook/1.0");
                _httpClient.DefaultRequestHeaders.Accept.ParseAdd("application/json");
            }
            catch (SystemException ex)
            {
                MessageBox.Show($"Error initializing HTTP client: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        private async void SurefireTaskPaneControl_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize WebView2
                webView = new WebView2();
                webView.Dock = DockStyle.Fill;
                webView.Margin = new Padding(10, 5, 10, 5);

                // Initialize WebView2 environment
                var env = await CoreWebView2Environment.CreateAsync(null, Path.Combine(Path.GetTempPath(), "WebView2Cache"));
                await webView.EnsureCoreWebView2Async(env);

                // Configure WebView2 settings
                webView.CoreWebView2.Settings.AreDefaultContextMenusEnabled = false;
                webView.CoreWebView2.Settings.IsStatusBarEnabled = false;
                webView.CoreWebView2.Settings.AreDevToolsEnabled = false;

                // Replace fallback textbox with WebView2
                mainLayout.Controls.Remove(fallbackTextBox);
                mainLayout.Controls.Add(webView, 1, 1);
                mainLayout.SetColumnSpan(webView, 1);
                useWebView = true;

                // Set default content
                SetContent("Select an email and click one of the buttons above.");
            }
            catch (SystemException)
            {
                // WebView2 initialization failed, use fallback RichTextBox
                useWebView = false;
                fallbackTextBox.Text = "Select an email and click one of the buttons above.";
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.Name = "SurefireTaskPaneControl";
            this.Dock = DockStyle.Fill;
            this.AutoScroll = true;
            this.ResumeLayout(false);
        }

        private void InitializeCustomComponents()
        {
            // Create main layout panel
            mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                AutoSize = false,
                AutoSizeMode = AutoSizeMode.GrowAndShrink,
                Padding = new Padding(0),
                BackColor = Color.White,
                Margin = new Padding(0)
            };

            // Configure column styles
            mainLayout.ColumnStyles.Clear();
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 10F));
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

            // Configure row styles
            mainLayout.RowStyles.Clear();
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 35)); // Button bar row
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // Content row
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 40)); // Custom prompt row

            // Create a panel for the top bar that will contain both the button panel and gear
            var topBarPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(0),
                Margin = new Padding(0),
                Height = 35,
                BackColor = Color.Transparent
            };

            // Create button panel with gradient background
            buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Left,
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = false,
                AutoSize = true,
                Margin = new Padding(0),
                Padding = new Padding(0),
                BackColor = Color.Transparent,
                Height = 35
            };

            using (var stream = new MemoryStream(Resource1.grad))
            {
                topBarPanel.BackgroundImage = Image.FromStream(stream);
                topBarPanel.BackgroundImageLayout = ImageLayout.Stretch;
            }

            // Initialize settings button
            btnSettings = new Button
            {
                Width = 24,
                Height = 24,
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Right | AnchorStyles.Top,
                Location = new Point(mainLayout.Width - 34, 5)
            };
            btnSettings.FlatAppearance.BorderSize = 0;

            try
            {
                using (var stream = new MemoryStream(Resource1.aGear))
                {
                    btnSettings.Image = new Bitmap(Image.FromStream(stream), 16, 16);
                }
            }
            catch (SystemException ex)
            {
                MessageBox.Show($"Error loading gear icon: {ex.Message}", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            btnSettings.Click += (s, e) => {
                using (var settingsForm = new SettingsForm(Professionalism, Details, Urgency))
                {
                    settingsForm.TopMost = true;
                    if (settingsForm.ShowDialog() == DialogResult.OK)
                    {
                        Professionalism = settingsForm.Professionalism;
                        Details = settingsForm.Details;
                        Urgency = settingsForm.Urgency;
                        SaveSettings(); // Save settings when OK is clicked
                    }
                }
            };

            // Initialize toggle button
            btnToggle = new Button
            {
                Text = "•",
                Dock = DockStyle.Fill,
                FlatStyle = FlatStyle.Flat,
                BackColor = ColorTranslator.FromHtml("#1e1e5e"),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12f, FontStyle.Bold),
                Margin = new Padding(0),
                Padding = new Padding(0),
                Cursor = Cursors.Hand,
                Width = 10
            };
            btnToggle.FlatAppearance.BorderSize = 0;
            btnToggle.Click += (s, e) => {
                OnToggleWidth?.Invoke(this, EventArgs.Empty);
            };

            // Add toggle button to layout
            mainLayout.Controls.Add(btnToggle, 0, 0);
            mainLayout.SetRowSpan(btnToggle, 3);

            // Add the top bar panel to the main layout
            mainLayout.Controls.Add(topBarPanel, 1, 0);

            // Initialize logo
            logoBox = new PictureBox
            {
                Width = 31,
                Height = 35,
                SizeMode = PictureBoxSizeMode.Normal,
                BackColor = Color.Transparent,
                Margin = new Padding(0, 0, 0, 0)
            };

            try
            {
                using (var stream = new MemoryStream(Resource1.dLogo))
                {
                    logoBox.Image = Image.FromStream(stream);
                }
            }
            catch (SystemException ex)
            {
                MessageBox.Show($"Error loading logo: {ex.Message}", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            // Initialize Brief button
            btnBrief = new Button
            {
                Text = "Tip",
                Height = 29,  // Increased by 1px
                AutoSize = true,
                Margin = new Padding(0),
                Padding = new Padding(0),
                BackColor = Color.Transparent,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10f, FontStyle.Regular),
                Image = new Bitmap(Image.FromStream(new MemoryStream(Resource1.cBrief)), 33, 29),
                TextImageRelation = TextImageRelation.ImageBeforeText,
                ImageAlign = ContentAlignment.MiddleLeft
            };
            btnBrief.FlatAppearance.BorderSize = 0;
            btnBrief.Click += BtnBrief_Click;
            btnBrief.MouseEnter += (s, e) => {
                btnBrief.BackColor = ColorTranslator.FromHtml("#d04b0e");
            };
            btnBrief.MouseLeave += (s, e) => {
                btnBrief.BackColor = Color.Transparent;
            };

            // Initialize Recap button
            btnRecap = new Button
            {
                Text = "Sum",
                Height = 29,  // Increased by 1px
                AutoSize = true,
                Margin = new Padding(0),
                Padding = new Padding(0),
                BackColor = Color.Transparent,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10f, FontStyle.Regular),
                Image = new Bitmap(Image.FromStream(new MemoryStream(Resource1.cRecap)), 29, 29),
                TextImageRelation = TextImageRelation.ImageBeforeText,
                ImageAlign = ContentAlignment.MiddleLeft
            };
            btnRecap.FlatAppearance.BorderSize = 0;
            btnRecap.Click += BtnRecap_Click;
            btnRecap.MouseEnter += (s, e) => {
                btnRecap.BackColor = ColorTranslator.FromHtml("#d04b0e");
            };
            btnRecap.MouseLeave += (s, e) => {
                btnRecap.BackColor = Color.Transparent;
            };

            // Initialize Tom button
            btnTom = new Button
            {
                Text = "Tom",
                Height = 29,  // Increased by 1px
                AutoSize = true,
                Margin = new Padding(0),
                Padding = new Padding(0),
                BackColor = Color.Transparent,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10f, FontStyle.Regular),
                Image = new Bitmap(Image.FromStream(new MemoryStream(Resource1.cTom)), 29, 29),
                TextImageRelation = TextImageRelation.ImageBeforeText,
                ImageAlign = ContentAlignment.MiddleLeft
            };
            btnTom.FlatAppearance.BorderSize = 0;
            btnTom.Click += async (s, e) => await ProcessEmailWithPrompt(s, EmailPrompts.TomPrompt);
            btnTom.MouseEnter += (s, e) => {
                btnTom.BackColor = ColorTranslator.FromHtml("#d04b0e");
            };
            btnTom.MouseLeave += (s, e) => {
                btnTom.BackColor = Color.Transparent;
            };

            // Initialize Draft Reply button
            btnDraftReply = new Button
            {
                Text = "Reply",
                Height = 29,
                AutoSize = true,
                Margin = new Padding(0),
                Padding = new Padding(0),
                BackColor = Color.Transparent,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 12f, FontStyle.Bold),
                Image = new Bitmap(Image.FromStream(new MemoryStream(Resource1.cReply)), 33, 29),
                TextImageRelation = TextImageRelation.ImageBeforeText,
                ImageAlign = ContentAlignment.MiddleLeft
            };
            btnDraftReply.FlatAppearance.BorderSize = 0;
            btnDraftReply.Click += BtnDraftReply_Click;
            btnDraftReply.MouseEnter += (s, e) => {
                btnDraftReply.BackColor = ColorTranslator.FromHtml("#d04b0e");
            };
            btnDraftReply.MouseLeave += (s, e) => {
                btnDraftReply.BackColor = Color.Transparent;
            };

            // Add logo and buttons to panel
            buttonPanel.Controls.Add(logoBox);
            buttonPanel.Controls.Add(btnBrief);
            buttonPanel.Controls.Add(btnRecap);
            buttonPanel.Controls.Add(btnTom);
            buttonPanel.Controls.Add(btnDraftReply);

            // Add buttonPanel and settings button to topBarPanel
            topBarPanel.Controls.Add(buttonPanel);
            topBarPanel.Controls.Add(btnSettings);
            btnSettings.BringToFront();

            // Add the top bar panel to the main layout
            mainLayout.Controls.Add(topBarPanel, 1, 0);

            // Initialize fallback RichTextBox
            fallbackTextBox = new RichTextBox
            {
                Multiline = true,
                ScrollBars = RichTextBoxScrollBars.Vertical,
                Dock = DockStyle.Fill,
                ReadOnly = true,
                Font = new Font("Segoe UI", 9f),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None,
                Margin = new Padding(10, 5, 10, 5),
                DetectUrls = true
            };

            // Initialize custom prompt box
            customPromptBox = new TextBox
            {
                Dock = DockStyle.Fill,
                Height = 34,
                Font = new Font("Segoe UI", 10f),
                ForeColor = ColorTranslator.FromHtml("#0d0d0d"),
                Text = "Ask a question or provide context...",
                BorderStyle = BorderStyle.None,
                BackColor = Color.White,
                Margin = new Padding(0)
            };

            // Create and initialize textBoxWrapper panel
            textBoxWrapper = new Panel
            {
                Dock = DockStyle.Fill,
                Height = 34,
                Margin = new Padding(3, 3, 3, 3),
                BackColor = Color.White,
                Padding = new Padding(10, 0, 10, 0)
            };

            // Create rounded corners for the wrapper panel
            textBoxWrapper.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, textBoxWrapper.Width, textBoxWrapper.Height, 6, 6));
            textBoxWrapper.Resize += (s, e) => {
                textBoxWrapper.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, textBoxWrapper.Width, textBoxWrapper.Height, 6, 6));
            };

            textBoxWrapper.Controls.Add(customPromptBox);

            // Add placeholder text behavior
            customPromptBox.GotFocus += (s, e) => {
                if (customPromptBox.Text == "Ask a question or provide context...")
                {
                    customPromptBox.Text = "";
                }
            };
            customPromptBox.LostFocus += (s, e) => {
                if (string.IsNullOrWhiteSpace(customPromptBox.Text))
                {
                    customPromptBox.Text = "Ask a question or provide context...";
                }
            };

            // Add Enter key handling
            customPromptBox.KeyDown += async (s, e) => {
                if (e.KeyCode == Keys.Enter)
                {
                    e.SuppressKeyPress = true;  // Prevent the "ding" sound
                    if (customPromptBox.Text != "Enter custom instructions about the email..." && !string.IsNullOrWhiteSpace(customPromptBox.Text))
                    {
                        await ProcessEmailWithPrompt(s, @"You are a helpful and knowledgeable assistant that processes requests for the staff at Metro Insurance. 
Your task is to analyze the provided email content and produce an output that exactly follows the user's request. You should first read and analyze the entire email (including email chains if applicable) for key points, 
important dates, numbers, policy details, actions required, pending questions, etc. Ensure your output exactly addresses the instructions provided by the user.

User's Instructions: " + customPromptBox.Text);
                    }
                }
            };

            // Initialize custom prompt button (hidden but needed for event handling)
            btnCustomPrompt = new Button
            {
                Visible = false,
                Width = 0,
                Height = 0
            };
            btnCustomPrompt.Click += async (s, e) => await ProcessEmailWithPrompt(s, @"You are a helpful and knowledgeable assistant that processes requests for the staff at Metro Insurance. 
Your task is to analyze the provided email content and produce an output that exactly follows the user's request. You should first read and analyze the entire email (including email chains if applicable) for key points, 
important dates, numbers, policy details, actions required, pending questions, etc. Ensure your output exactly addresses the instructions provided by the user.

User's Instructions: " + customPromptBox.Text);

            var customPromptPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                AutoSize = false,
                Margin = new Padding(0),
                Padding = new Padding(0, 3, 10, 3)
            };

            customPromptPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            customPromptPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 28F));
            customPromptPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 28F));  // Add column for new button
            customPromptPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));

            using (var stream = new MemoryStream(Resource1.grad))
            {
                customPromptPanel.BackgroundImage = Image.FromStream(stream);
                customPromptPanel.BackgroundImageLayout = ImageLayout.Stretch;
            }

            // Add the wrapper panel instead of the text box directly
            customPromptPanel.Controls.Add(textBoxWrapper, 0, 0);

            // Initialize visible submit button with chevron
            var submitButton = new Button
            {
                Text = "›",
                Height = 28,
                Width = 22,
                Margin = new Padding(0, 3, 0, 0),  // Added 3px top margin
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 14f, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(52, 28, 104), //1e1e5e
                Cursor = Cursors.Hand
            };
            submitButton.FlatAppearance.BorderSize = 0;

            // Create rounded corners for the submit button
            submitButton.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, submitButton.Width, submitButton.Height, 6, 6));
            submitButton.Resize += (s, e) => {
                submitButton.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, submitButton.Width, submitButton.Height, 6, 6));
            };

            submitButton.Click += async (s, e) => {
                if (customPromptBox.Text != "Ask a question or provide context...")
                {
                    string basePrompt = @"You are a helpful and knowledgeable assistant that processes requests for the staff at Metro Insurance. 
Your task is to analyze the provided email content and produce an output that exactly follows the user's request. You should first read and analyze the entire email (including email chains if applicable) for key points, 
important dates, numbers, policy details, actions required, pending questions, etc. Ensure your output exactly addresses the instructions provided by the user.

User's Instructions: " + customPromptBox.Text;

                    await ProcessEmailWithPrompt(s, GetModifiedPrompt(basePrompt));
                }
            };

            // Initialize custom reply submit button
            var replySubmitButton = new Button
            {
                Text = "+",
                Height = 28,
                Width = 22,
                Margin = new Padding(0, 3, 0, 0),  // Added 3px top margin
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 14f, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(208, 75, 14), // Orange color to match Reply button
                Cursor = Cursors.Hand
            };
            replySubmitButton.FlatAppearance.BorderSize = 0;

            // Create rounded corners for the reply submit button
            replySubmitButton.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, replySubmitButton.Width, replySubmitButton.Height, 6, 6));
            replySubmitButton.Resize += (s, e) => {
                replySubmitButton.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, replySubmitButton.Width, replySubmitButton.Height, 6, 6));
            };

            // Add hover effects
            replySubmitButton.MouseEnter += (s, e) => {
                replySubmitButton.BackColor = Color.FromArgb(168, 60, 11); // Darker orange
            };
            replySubmitButton.MouseLeave += (s, e) => {
                replySubmitButton.BackColor = Color.FromArgb(208, 75, 14); // Original orange
            };

            // Add click handler for custom reply drafting
            replySubmitButton.Click += async (s, e) => {
                try
                {
                    var explorer = Globals.ThisAddIn.Application.ActiveExplorer();
                    var selection = explorer.Selection;

                    if (selection.Count > 0 && customPromptBox.Text != "Ask a question or provide context...")
                    {
                        var mailItem = selection[1] as Microsoft.Office.Interop.Outlook.MailItem;
                        if (mailItem != null)
                        {
                            SetContent("Drafting custom reply...");

                            string modifiedPrompt = GetModifiedPrompt(EmailPrompts.DraftReplyPrompt + "\n\nAdditional Instructions: " + customPromptBox.Text);
                            string draftReply = await SummarizeEmailAsync(mailItem.Body, modifiedPrompt);

                            var replyMail = mailItem.Reply();
                            replyMail.HTMLBody = draftReply.Replace("\n", "<br>");
                            replyMail.Display(false);

                            SetContent("Custom reply draft created and opened in new window.");
                        }
                        else
                        {
                            SetContent("Please select an email to reply to.");
                        }
                    }
                    else
                    {
                        SetContent("Please select an email and provide custom instructions.");
                    }
                }
                catch (SystemException ex)
                {
                    SetContent($"Error: {ex.Message}");
                }
            };

            // Add tooltip for the reply submit button
            var replyTooltip = new ToolTip();
            replyTooltip.SetToolTip(replySubmitButton, "Draft a custom reply using your instructions");

            customPromptPanel.Controls.Add(submitButton, 1, 0);
            customPromptPanel.Controls.Add(replySubmitButton, 2, 0);
            customPromptPanel.Controls.Add(btnCustomPrompt, 0, 0); // Keep the hidden button for Enter key handling

            // Add all controls to layout (update column indices)
            mainLayout.Controls.Add(fallbackTextBox, 1, 1);
            mainLayout.Controls.Add(customPromptPanel, 1, 2);

            mainLayout.SetColumnSpan(fallbackTextBox, 1);
            mainLayout.SetColumnSpan(customPromptPanel, 1);

            // Initialize collapsed button panel
            collapsedButtonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.TopDown,
                WrapContents = false,
                AutoSize = true,
                Margin = new Padding(0),
                Padding = new Padding(2),
                BackColor = ColorTranslator.FromHtml("#1e1e5e"),
                Visible = false
            };

            // Create collapsed state buttons
            var btnCollapsedReply = CreateCollapsedButton("Reply", "MailReply");
            var btnCollapsedBrief = CreateCollapsedButton("Brief", "Summary");
            var btnCollapsedRecap = CreateCollapsedButton("Recap", "ViewAllFieldsMenu");
            var btnCollapsedTom = CreateCollapsedButton("Tom", "ContactCard");

            btnCollapsedReply.Click += async (s, e) => {
                ThisAddIn.Instance.TaskPane.Width = _lastExpandedWidth;
                await Task.Delay(100);
                BtnDraftReply_Click(s, e);
            };

            btnCollapsedBrief.Click += async (s, e) => {
                ThisAddIn.Instance.TaskPane.Width = _lastExpandedWidth;
                await Task.Delay(100);
                BtnBrief_Click(s, e);
            };

            btnCollapsedRecap.Click += async (s, e) => {
                ThisAddIn.Instance.TaskPane.Width = _lastExpandedWidth;
                await Task.Delay(100);
                BtnRecap_Click(s, e);
            };

            btnCollapsedTom.Click += async (s, e) => {
                ThisAddIn.Instance.TaskPane.Width = _lastExpandedWidth;
                await Task.Delay(100);
                await ProcessEmailWithPrompt(s, EmailPrompts.TomPrompt);
            };

            // Create collapsed logo button
            var btnCollapsedLogo = new Button
            {
                Height = 42,
                Width = 42,
                Margin = new Padding(0, 2, 0, 10), // Added bottom margin to separate from other buttons
                Padding = new Padding(2),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };
            btnCollapsedLogo.FlatAppearance.BorderSize = 0;

            // Set the logo image
            try
            {
                using (var stream = new MemoryStream(Resource1.dLogo))
                {
                    btnCollapsedLogo.Image = new Bitmap(Image.FromStream(stream), 24, 24);
                }
            }
            catch (SystemException ex)
            {
                MessageBox.Show($"Error loading logo: {ex.Message}", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            btnCollapsedLogo.ImageAlign = ContentAlignment.MiddleCenter;

            // Create rounded corners
            btnCollapsedLogo.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, btnCollapsedLogo.Width, btnCollapsedLogo.Height, 8, 8));
            btnCollapsedLogo.Resize += (s, e) => {
                btnCollapsedLogo.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, btnCollapsedLogo.Width, btnCollapsedLogo.Height, 8, 8));
            };

            // Add hover effect
            btnCollapsedLogo.MouseEnter += (s, e) => {
                btnCollapsedLogo.BackColor = ColorTranslator.FromHtml("#2d2d8e");
            };
            btnCollapsedLogo.MouseLeave += (s, e) => {
                btnCollapsedLogo.BackColor = Color.Transparent;
            };

            // Add click handler to expand the pane
            btnCollapsedLogo.Click += (s, e) => {
                ThisAddIn.Instance.TaskPane.Width = _lastExpandedWidth;
            };

            // Add logo button first to collapsed panel
            collapsedButtonPanel.Controls.Clear(); // Clear existing controls
            collapsedButtonPanel.Controls.AddRange(new Control[] {
                btnCollapsedLogo,
                btnCollapsedBrief,
                btnCollapsedRecap,
                btnCollapsedTom,
                btnCollapsedReply
            });

            // Add collapsed panel to main layout
            mainLayout.Controls.Add(collapsedButtonPanel, 1, 1);

            // Add layout to form
            this.Controls.Clear();
            this.Controls.Add(mainLayout);

            // Handle resize events
            this.Resize += SurefireTaskPaneControl_Resize;

            // Subscribe to SizeChanged to handle panel visibility
            this.SizeChanged += (s, e) => UpdatePanelVisibility();
        }

        private Button CreateCollapsedButton(string text, string imageMso)
        {
            var btn = new Button
            {
                Text = "",  // Remove text since we're using icons
                Height = 42,  // Increased size
                Width = 42,  // Increased size
                Margin = new Padding(0, 2, 0, 2),
                Padding = new Padding(2),  // Added 2px padding
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = Color.White,
                Cursor = Cursors.Hand,
                Font = new Font("Segoe UI", 10f, FontStyle.Bold)
            };
            btn.FlatAppearance.BorderSize = 0;

            // Create rounded corners
            btn.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, btn.Width, btn.Height, 8, 8));
            btn.Resize += (s, e) => {
                btn.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, btn.Width, btn.Height, 8, 8));
            };

            // Set the appropriate icon based on the button type
            byte[] iconResource;
            switch (text)
            {
                case "Brief":
                    iconResource = Resource1.cBrief;
                    break;
                case "Recap":
                    iconResource = Resource1.cRecap;
                    break;
                case "Tom":
                    iconResource = Resource1.cTom;
                    break;
                case "Reply":
                    iconResource = Resource1.cReply;
                    break;
                default:
                    iconResource = Resource1.cBrief; // Default fallback
                    break;
            }

            using (var stream = new MemoryStream(iconResource))
            {
                // Scale icon to 24x24 for larger collapsed buttons
                btn.Image = new Bitmap(Image.FromStream(stream), 24, 24);
            }
            btn.ImageAlign = ContentAlignment.MiddleCenter;

            // Create tooltip
            var tooltip = new ToolTip();
            tooltip.SetToolTip(btn, text);

            // Add hover effect
            btn.MouseEnter += (s, e) => {
                btn.BackColor = Color.Black; // Slightly darker version
            };
            btn.MouseLeave += (s, e) => {
                btn.BackColor = Color.Transparent; // Original color
            };

            return btn;
        }

        private void UpdatePanelVisibility()
        {
            if (this.Width <= COLLAPSE_THRESHOLD)
            {
                // Hide unnecessary rows
                mainLayout.RowStyles[0].Height = 0; // Button bar row
                mainLayout.RowStyles[2].Height = 0; // Custom prompt row

                // Hide controls
                buttonPanel.Visible = false;
                fallbackTextBox.Visible = false;
                webView?.Hide();
                customPromptBox.Visible = false;

                // Show collapsed panel
                collapsedButtonPanel.Visible = true;
                collapsedButtonPanel.Dock = DockStyle.Fill;
                collapsedButtonPanel.BackColor = ColorTranslator.FromHtml("#1e1e5e");

                btnToggle.Text = "»";
            }
            else
            {
                // Restore row heights
                mainLayout.RowStyles[0].Height = 35; // Button bar row
                mainLayout.RowStyles[2].Height = 40; // Custom prompt row

                // Show controls
                buttonPanel.Visible = true;
                fallbackTextBox.Visible = useWebView ? false : true;
                webView?.Show();
                customPromptBox.Visible = true;

                // Hide collapsed panel
                collapsedButtonPanel.Visible = false;

                btnToggle.Text = "«";

                // Update last expanded width
                _lastExpandedWidth = this.Width;
            }

            // Force layout update
            mainLayout.PerformLayout();
        }

        private void SurefireTaskPaneControl_Resize(object sender, EventArgs e)
        {
            // Adjust controls if needed based on new size
            mainLayout.PerformLayout();
        }

        private async void BtnBrief_Click(object sender, EventArgs e)
        {
            await ProcessEmailWithPrompt(sender, EmailPrompts.BriefPrompt);
        }

        private async void BtnRecap_Click(object sender, EventArgs e)
        {
            await ProcessEmailWithPrompt(sender, EmailPrompts.RecapPrompt);
        }

        private async void BtnDraftReply_Click(object sender, EventArgs e)
        {
            try
            {
                var explorer = Globals.ThisAddIn.Application.ActiveExplorer();
                if (explorer == null)
                {
                    SetContent("Error: Could not access Outlook explorer.");
                    return;
                }

                var selection = explorer.Selection;
                if (selection == null || selection.Count == 0)
                {
                    SetContent("Please select an email to reply to.");
                    return;
                }

                try
                {
                    var mailItem = selection[1] as Microsoft.Office.Interop.Outlook.MailItem;
                    if (mailItem == null)
                    {
                        SetContent("Please select an email message (the selected item is not an email).");
                        return;
                    }

                    Button sourceButton = sender as Button;
                    if (sourceButton != null)
                        sourceButton.Enabled = false;

                    SetContent("Drafting reply...");

                    // Get email body safely
                    string emailBody;
                    try
                    {
                        emailBody = mailItem.Body;
                        if (string.IsNullOrEmpty(emailBody))
                        {
                            SetContent("The selected email appears to be empty.");
                            return;
                        }
                    }
                    catch (System.Runtime.InteropServices.COMException comEx)
                    {
                        if ((uint)comEx.ErrorCode == 0x8004010F)
                        {
                            SetContent("Error: Could not access the email content. Please make sure you have selected a valid email and try again.");
                        }
                        else
                        {
                            SetContent($"Error accessing email: {comEx.Message}");
                        }
                        return;
                    }

                    // Use GetModifiedPrompt to apply tone modifiers
                    string modifiedPrompt = GetModifiedPrompt(EmailPrompts.DraftReplyPrompt);
                    string draftReply = await SummarizeEmailAsync(emailBody, modifiedPrompt);

                    // Create new email
                    var replyMail = mailItem.Reply();
                    replyMail.HTMLBody = draftReply.Replace("\n", "<br>");
                    replyMail.Display(false);

                    SetContent("Reply draft created and opened in new window.");
                }
                catch (System.Runtime.InteropServices.COMException comEx)
                {
                    if ((uint)comEx.ErrorCode == 0x8004010F)
                    {
                        SetContent("Error: Could not access the email. Please make sure you have selected a valid email and try again.");
                    }
                    else
                    {
                        SetContent($"Outlook error: {comEx.Message}");
                    }
                }
            }
            catch (SystemException ex)
            {
                SetContent($"Error: {ex.Message}\nPlease make sure you have selected a valid email and try again.");
            }
            finally
            {
                btnBrief.Enabled = true;
                btnRecap.Enabled = true;
                if (sender is Button sourceButton)
                    sourceButton.Enabled = true;
            }
        }

        private async Task ProcessEmailWithPrompt(object sender, string systemPrompt)
        {
            try
            {
                var explorer = Globals.ThisAddIn.Application.ActiveExplorer();
                if (explorer == null)
                {
                    SetContent("Error: Could not access Outlook explorer.");
                    return;
                }

                var selection = explorer.Selection;
                if (selection == null || selection.Count == 0)
                {
                    SetContent("Please select an email to process.");
                    return;
                }

                try
                {
                    var mailItem = selection[1] as Microsoft.Office.Interop.Outlook.MailItem;
                    if (mailItem == null)
                    {
                        SetContent("Please select an email message (the selected item is not an email).");
                        return;
                    }

                    Button sourceButton = sender as Button;
                    if (sourceButton != null)
                        sourceButton.Enabled = false;
                    btnBrief.Enabled = false;
                    btnRecap.Enabled = false;

                    SetContent("Processing email...", true); // Show spinner

                    // Get email body safely
                    string emailBody;
                    try
                    {
                        emailBody = mailItem.Body;
                        if (string.IsNullOrEmpty(emailBody))
                        {
                            SetContent("The selected email appears to be empty.");
                            return;
                        }
                    }
                    catch (System.Runtime.InteropServices.COMException comEx)
                    {
                        if ((uint)comEx.ErrorCode == 0x8004010F)
                        {
                            SetContent("Error: Could not access the email content. Please make sure you have selected a valid email and try again.");
                        }
                        else
                        {
                            SetContent($"Error accessing email: {comEx.Message}");
                        }
                        return;
                    }

                    string summary = await SummarizeEmailAsync(emailBody, systemPrompt);
                    SetContent(summary, false); // Hide spinner
                }
                catch (System.Runtime.InteropServices.COMException comEx)
                {
                    if ((uint)comEx.ErrorCode == 0x8004010F)
                    {
                        SetContent("Error: Could not access the email. Please make sure you have selected a valid email and try again.");
                    }
                    else
                    {
                        SetContent($"Outlook error: {comEx.Message}");
                    }
                }
            }
            catch (SystemException ex)
            {
                SetContent($"Error: {ex.Message}\nPlease make sure you have selected a valid email and try again.");
            }
            finally
            {
                btnBrief.Enabled = true;
                btnRecap.Enabled = true;
                if (sender is Button sourceButton)
                    sourceButton.Enabled = true;
            }
        }

        private async Task<string> SummarizeEmailAsync(string emailBody, string systemPrompt)
        {
            try
            {
                string apiKey = ConfigurationManager.AppSettings["OpenAIApiKey"];
                if (string.IsNullOrEmpty(apiKey))
                {
                    return "Error: OpenAI API key not found in configuration. Please check your App.config file.";
                }

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
                _httpClient.DefaultRequestHeaders.Accept.ParseAdd("application/json");
                _httpClient.DefaultRequestHeaders.Add("User-Agent", "Surefire.Outlook/1.0");

                var requestData = new
                {
                    model = "gpt-4o-mini",
                    messages = new[]
                    {
                        new { role = "system", content = systemPrompt },
                        new { role = "user", content = $"Please analyze this email:\n\n{emailBody}" }
                    },
                    max_tokens = 500
                };

                var jsonContent = System.Text.Json.JsonSerializer.Serialize(requestData);
                SetContent("Processing...", true); // Show spinner while making the request

                var content = new StringContent(
                    jsonContent,
                    System.Text.Encoding.UTF8,
                    "application/json"
                );

                var response = await _httpClient.PostAsync("https://api.openai.com/v1/chat/completions", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    try
                    {
                        using (var doc = JsonDocument.Parse(responseContent))
                        {
                            return doc.RootElement.GetProperty("choices")[0].GetProperty("message").GetProperty("content").GetString();
                        }
                    }
                    catch (SystemException jsonEx)
                    {
                        return $"Error parsing OpenAI response: {jsonEx.Message}\nResponse: {responseContent}";
                    }
                }
                else
                {
                    return $"OpenAI API Error ({response.StatusCode}):\n{responseContent}";
                }
            }
            catch (HttpRequestException reqEx)
            {
                return $"Network Error: {reqEx.Message}\nInner Exception: {reqEx.InnerException?.Message}\nPlease check your internet connection and firewall settings.";
            }
            catch (TaskCanceledException)
            {
                return "Request timed out. Please try again.";
            }
            catch (SystemException ex)
            {
                return $"Unexpected error: {ex.Message}";
            }
        }

        private void SetContent(string markdown, bool isLoading = false)
        {
            try
            {
                if (!useWebView)
                {
                    fallbackTextBox.Text = markdown;
                    return;
                }

                var pipeline = new MarkdownPipelineBuilder()
                    .UseEmphasisExtras()
                    .UseListExtras()
                    .UsePipeTables()
                    .UseGridTables()
                    .Build();

                string html = Markdown.ToHtml(markdown, pipeline);

                string spinnerHtml = isLoading ? @"
                    <div class='spinner-container'>
                        <div class='ms-Spinner ms-Spinner--large'>
                            <div class='ms-Spinner-circle'></div>
                            <div class='ms-Spinner-label'>Processing...</div>
                        </div>
                    </div>" : "";

                string fullHtml = $@"
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset='utf-8'>
                        <style>
                            body {{
                                font-family: 'Segoe UI', sans-serif;
                                font-size: 12px;
                                line-height: 1.4;
                                margin: 10px;
                                color: #333;
                                background-color: white;
                            }}
                            h1, h2, h3, h4, h5, h6 {{
                                margin: 0.8em 0 0.4em 0;
                                color: #000;
                            }}
                            strong {{
                                color: #000;
                            }}
                            ul {{
                                margin: 0.5em 0;
                                padding-left: 1.5em;
                            }}
                            li {{
                                margin: 0.3em 0;
                            }}
                            p {{
                                margin: 0.5em 0;
                            }}
                            .spinner-container {{
                                position: fixed;
                                top: 0;
                                left: 0;
                                right: 0;
                                bottom: 0;
                                background: rgba(255, 255, 255, 0.9);
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                flex-direction: column;
                            }}
                            .ms-Spinner {{
                                position: relative;
                                width: 32px;
                                height: 32px;
                                margin-bottom: 50px;  /* Added margin to push label down */
                                margin-right: 10px;   /* Shift spinner right to compensate for label */
                            }}
                            .ms-Spinner-circle {{
                                position: absolute;
                                border-radius: 50%;
                                border: 2px solid #1e1e5e;
                                border-top-color: transparent;
                                width: 100%;
                                height: 100%;
                                animation: ms-spinner-spin 1.3s infinite cubic-bezier(.53,.21,.29,.67);
                            }}
                            .ms-Spinner-label {{
                                position: absolute;
                                color: #1e1e5e;
                                font-family: 'Segoe UI', sans-serif;
                                font-size: 12px;
                                margin-left: -10px;  /* Shift label left */
                                margin-top: 40px;    /* Push label down */
                            }}
                            @keyframes ms-spinner-spin {{
                                from {{ transform: rotate(0deg); }}
                                to {{ transform: rotate(360deg); }}
                            }}
                        </style>
                    </head>
                    <body>
                        {html}
                        {spinnerHtml}
                    </body>
                    </html>";

                webView?.NavigateToString(fullHtml);
            }
            catch (SystemException ex)
            {
                if (useWebView)
                {
                    useWebView = false;
                    fallbackTextBox.Text = markdown;
                    MessageBox.Show($"WebView2 error: {ex.Message}\nFalling back to basic text display.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                else
                {
                    MessageBox.Show($"Error setting content: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (_httpClient != null)
                {
                    _httpClient.Dispose();
                }
            }
            base.Dispose(disposing);
        }

        private string GetModifiedPrompt(string basePrompt)
        {
            string professionalismModifier = "";
            string detailsModifier = "";
            string urgencyModifier = "";

            // Adjust tone based on professionalism
            if (Professionalism == 0 || Professionalism == 1)
            {
                // 0-1 range
                professionalismModifier =
                    "\nTone: Over-the-top funny and casual, like you're speaking to your best friend while a bit tipsy. " +
                    $"Professionalism level: {Professionalism}/10";
            }
            else if (Professionalism == 2 || Professionalism == 3)
            {
                // 3-4 range
                professionalismModifier =
                    "\nTone: Extremely casual and humorous with lighthearted banter. " +
                    $"Professionalism level: {Professionalism}/10";
            }
            else if (Professionalism == 4)
            {
                // 3-4 range
                professionalismModifier =
                    "\nTone: Casual, relaxed, and friendly. Feel free to use some humor, but keep it moderate." +
                    $"Professionalism level: {Professionalism}/10";
            }
            else if (Professionalism == 6 || Professionalism == 7)
            {
                // 8-10 range
                professionalismModifier =
                    "\nTone: Polite, moderately professional, but still approachable. Minimal humor is okay. " +
                    $"Professionalism level: {Professionalism}/10";
            }
            else if (Professionalism == 8 || Professionalism == 9)
            {
                // 6-7 range
                professionalismModifier =
                    "\nTone: Formal business style with polite courtesy. Keep humor to a minimum." +
                    $"Professionalism level: {Professionalism}/10";
            }
            else if (Professionalism == 10)
            {
                // 6-7 range
                professionalismModifier =
                    "\nTone: Extremely formal—on the level of a UN treaty or armistice." +
                    $"Professionalism level: {Professionalism}/10";
            }

            // Adjust detail level
            if (Details == 0 || Details == 1)
            {
                detailsModifier =
                    "\nDetail Level: Extremely brief—essentially one sentance is more than enough. Do not include a greeting or salutation. " +
                    $"Detail level: {Details}/10";
            }
            else if (Details == 2 || Details == 3 || Details == 4)
            {
                detailsModifier =
                    "\nDetail Level: Brief and concise but informative. " +
                    $"Detail level: {Details}/10";
            }
            else if (Details == 6 || Details == 7)
            {
                detailsModifier =
                    "\nDetail Level: Moderately more detailed than necessary—explain fully but avoid unnecessary tangents." +
                    $"Detail level: {Details}/10";
            }
            else if (Details == 8 || Details == 9)
            {
                detailsModifier =
                    "\nDetail Level: Extremely thorough and comprehensive—include nuances and relevant examples." +
                    $"Detail level: {Details}/10";
            }
            else if (Details == 10)
            {
                detailsModifier =
                    "\nDetail Level: Incredibly exhaustive—cover every angle and detail, leaving nothing out." +
                    $"Detail level: {Details}/10";
            }

            // Adjust urgency
            if (Urgency == 0 || Urgency == 1)
            {
                // 0-2 range
                urgencyModifier =
                    "\nUrgency: This is purely an exercise in futility, don't worry about a thing." +
                    $"Urgency level: {Urgency}/10";
            }
            else if (Urgency == 2 || Urgency == 3 || Urgency == 4)
            {
                // 3-4 range
                urgencyModifier =
                    "\nUrgency: Not high priority but we should keep it moving. " +
                    $"Urgency level: {Urgency}/10";
            }
            else if (Urgency == 6 || Urgency == 7)
            {
                // 8-10 range
                urgencyModifier =
                    "\nUrgency: Important and needs prompt attention—shouldn't be delayed." +
                    $"Urgency level: {Urgency}/10";
            }
            else if (Urgency == 8 || Urgency == 9)
            {
                // 6-7 range
                urgencyModifier =
                    "\nUrgency: Very urgent; we're almost at a critical point and need quick turnaround." +
                    $"Urgency level: {Urgency}/10";
            }
            else if (Urgency == 10)
            {
                // 6-7 range
                urgencyModifier =
                    "\nUrgency: Everyone should know to act immediately as this is life or death and needed it yesterday!" +
                    $"Urgency level: {Urgency}/10";
            }

            return basePrompt + professionalismModifier + detailsModifier + urgencyModifier;
        }

        private void SaveSettings()
        {
            try
            {
                // Get the configuration file path
                string configPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "Surefire.Outlook",
                    "user.config"
                );

                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(configPath));

                // Create or open the configuration file
                Configuration userConfig;
                if (!File.Exists(configPath))
                {
                    // Create new configuration
                    userConfig = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                    if (userConfig.Sections["appSettings"] == null)
                    {
                        userConfig.Sections.Add("appSettings", new AppSettingsSection());
                    }
                    userConfig.SaveAs(configPath);
                }
                else
                {
                    // Open existing configuration
                    var fileMap = new ExeConfigurationFileMap { ExeConfigFilename = configPath };
                    userConfig = ConfigurationManager.OpenMappedExeConfiguration(fileMap, ConfigurationUserLevel.None);
                    
                    // Add appSettings section if it doesn't exist
                    if (userConfig.Sections["appSettings"] == null)
                    {
                        userConfig.Sections.Add("appSettings", new AppSettingsSection());
                    }
                }

                // Get or create the appSettings section
                var appSettings = (AppSettingsSection)userConfig.GetSection("appSettings") ?? new AppSettingsSection();

                // Update settings
                UpdateOrAddSetting(appSettings.Settings, "Professionalism", Professionalism.ToString());
                UpdateOrAddSetting(appSettings.Settings, "Details", Details.ToString());
                UpdateOrAddSetting(appSettings.Settings, "Urgency", Urgency.ToString());

                // Save the configuration
                userConfig.Save(ConfigurationSaveMode.Modified);
            }
            catch (SystemException ex)
            {
                MessageBox.Show($"Error saving settings: {ex.Message}\nSettings will not persist between sessions.", 
                    "Settings Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void UpdateOrAddSetting(KeyValueConfigurationCollection settings, string key, string value)
        {
            if (settings[key] == null)
            {
                settings.Add(key, value);
            }
            else
            {
                settings[key].Value = value;
            }
        }

        private void LoadSettings()
        {
            try
            {
                // Get the configuration file path
                string configPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "Surefire.Outlook",
                    "user.config"
                );

                // If config doesn't exist, use defaults
                if (!File.Exists(configPath))
                {
                    Professionalism = 5;
                    Details = 5;
                    Urgency = 5;
                    return;
                }

                // Open the configuration file
                var fileMap = new ExeConfigurationFileMap { ExeConfigFilename = configPath };
                var userConfig = ConfigurationManager.OpenMappedExeConfiguration(fileMap, ConfigurationUserLevel.None);

                // Load settings if they exist, otherwise use defaults
                var settingsCollection = userConfig.AppSettings?.Settings;
                if (settingsCollection != null)
                {
                    Professionalism = int.TryParse(settingsCollection["Professionalism"]?.Value, out int prof) ? prof : 5;
                    Details = int.TryParse(settingsCollection["Details"]?.Value, out int det) ? det : 5;
                    Urgency = int.TryParse(settingsCollection["Urgency"]?.Value, out int urg) ? urg : 5;
                }
                else
                {
                    Professionalism = 5;
                    Details = 5;
                    Urgency = 5;
                }
            }
            catch (SystemException)
            {
                // Use defaults if settings can't be loaded
                Professionalism = 5;
                Details = 5;
                Urgency = 5;
            }
        }
    }

    public class Ribbon : Microsoft.Office.Core.IRibbonExtensibility
    {
        private Microsoft.Office.Core.IRibbonUI ribbon;

        public string GetCustomUI(string ribbonID)
        {
            return @"
                <customUI xmlns='http://schemas.microsoft.com/office/2009/07/customui'>
                    <ribbon>
                        <tabs>
                            <tab idMso='TabAddIns'>
                                <group id='SurefireGroup' label='Surefire Tools'>
                                    <toggleButton id='toggleTaskPane'
                                                label='Show/Hide Tools'
                                                screentip='Show or Hide Surefire Tools'
                                                supertip='Click to show or hide the Surefire Tools panel. You can find this button in the Add-ins tab if you need to reopen the panel.'
                                                onAction='OnToggleTaskPane'
                                                getPressed='GetTaskPaneVisible'
                                                imageMso='GroupShowHide'
                                                size='large'
                                                />
                                </group>
                            </tab>
                        </tabs>
                    </ribbon>
                </customUI>";
        }

        public void OnToggleTaskPane(Microsoft.Office.Core.IRibbonControl control, bool pressed)
        {
            var taskPane = ThisAddIn.Instance?.TaskPane;
            if (taskPane != null)
            {
                taskPane.Visible = pressed;
                if (pressed && taskPane.Width <= SurefireTaskPaneControl.COLLAPSE_THRESHOLD)
                {
                    // If showing the pane and it was collapsed, expand it
                    taskPane.Width = 450; // Default expanded width
                }
            }
        }

        public bool GetTaskPaneVisible(Microsoft.Office.Core.IRibbonControl control)
        {
            var taskPane = ThisAddIn.Instance?.TaskPane;
            return taskPane != null && taskPane.Visible;
        }

        public void Ribbon_Load(Microsoft.Office.Core.IRibbonUI ribbonUI)
        {
            this.ribbon = ribbonUI;
        }
    }
}
