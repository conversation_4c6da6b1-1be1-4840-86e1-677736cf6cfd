﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.Drawing;
using System.Net.Http;
using System.Threading.Tasks;

namespace SurefireTray
{
    public static class WindowsControl
    {
        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsIconic(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool OpenIcon(IntPtr hWnd);

        public static void BringWindowToFront(IntPtr handle)
        {
            try
            {
                if (handle != IntPtr.Zero)
                {
                    if (IsIconic(handle))
                    {
                        OpenIcon(handle);
                    }
                    ShowWindow(handle, 9); // SW_RESTORE = 9
                    SetForegroundWindow(handle);
                }
            }
            catch (System.Exception ex)
            {
                SystemTray.Log($"Error bringing window to front: {ex.Message}");
            }
        }

        public static void PerformWindowsFunction(string emberFunction, List<string> parameters)
        {
            switch (emberFunction)
            {
                case "Windows_OpenFolder":
                    OpenFolder(parameters.FirstOrDefault());
                    break;
                case "Windows_OpenFile":
                    OpenFile(parameters.FirstOrDefault());
                    break;
                case "Windows_ShowCallNotification":
                    ShowCallNotification(parameters);
                    break;
                default:
                    SystemControl.Log($"Unknown ember windowscontrol function: {emberFunction}");
                    break;
            }
        }

        public static void OpenFolder(string folderPath)
        {
            SystemControl.Log($"Finding to Open: {folderPath}");
            if (!string.IsNullOrEmpty(folderPath))
            {
                try
                {   
                    // Check if the path is a file or a folder
                    if (File.Exists(folderPath))
                    {
                        // It's a file path - open Explorer and select the file
                        System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{folderPath}\"");
                        SystemControl.Log($"Opened folder and selected file: {folderPath}");
                    }
                    else if (Directory.Exists(folderPath))
                    {
                        // It's a folder path - just open the folder
                        System.Diagnostics.Process.Start("explorer.exe", folderPath);
                        SystemControl.Log($"Opened folder in Explorer: {folderPath}");
                    }
                    else
                    {
                        SystemControl.Log($"Path does not exist: {folderPath}");
                    }

                    // Bring the Explorer window to the front
                    //SystemControl.BringToFront("CabinetWClass"); // "CabinetWClass" is the class name for Explorer windows
                }
                catch (System.Exception ex)
                {
                    SystemControl.Log($"Error opening folder in Explorer: {ex.Message}");
                }
            }
        }
        public static void OpenFile(string filePath)
        {
            SystemControl.Log($"Finding to Open: {filePath}");
            if (!string.IsNullOrEmpty(filePath))
            {
                try
                {
                    // Open the file using the default application
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true // Ensures the file opens with the associated application
                    });
                    SystemControl.Log($"Opened file: {filePath}");

                    // Optionally, bring the associated application to the front
                    // Remove this if unnecessary or unclear
                    SystemControl.BringToFront("CabinetWClass"); // Adjust class name if needed for specific apps
                }
                catch (System.Exception ex)
                {
                    SystemControl.Log($"Error opening file: {ex.Message}");
                }
            }
            else
            {
                SystemControl.Log("No file path provided to open.");
            }
        }

        public static async void ShowCallNotification(List<string> parameters)
        {
            if (parameters == null || parameters.Count < 3)
            {
                SystemControl.Log("Error: Insufficient parameters for call notification. Required: Title, Message, ClientId");
                return;
            }

            string title = parameters[0];
            string message = parameters[1];
            string clientId = parameters[2];
            string headshotUrl = parameters.Count > 3 ? parameters[3] : null;
            string clientUrl = $"https://surefire.local/Clients/{clientId}";

            try
            {
                // Create a custom notification form
                using (var notificationForm = new Form())
                {
                    notificationForm.FormBorderStyle = FormBorderStyle.None;
                    notificationForm.StartPosition = FormStartPosition.Manual;
                    notificationForm.Size = new Size(400, 150);
                    notificationForm.Location = new Point(Screen.PrimaryScreen.WorkingArea.Width - notificationForm.Width - 10,
                                                       Screen.PrimaryScreen.WorkingArea.Height - notificationForm.Height - 10);
                    notificationForm.BackColor = Color.White;

                    // Create title label
                    var titleLabel = new Label
                    {
                        Text = title,
                        Font = new Font("Segoe UI", 12, FontStyle.Bold),
                        Location = new Point(10, 10),
                        AutoSize = true
                    };

                    // Create message label
                    var messageLabel = new Label
                    {
                        Text = message,
                        Font = new Font("Segoe UI", 10),
                        Location = new Point(10, 40),
                        AutoSize = true
                    };

                    // Create open client button
                    var openButton = new Button
                    {
                        Text = "Open Client",
                        Location = new Point(10, 100),
                        Size = new Size(100, 30),
                        FlatStyle = FlatStyle.Flat,
                        BackColor = Color.FromArgb(0, 120, 215),
                        ForeColor = Color.White,
                        Font = new Font("Segoe UI", 9, FontStyle.Bold)
                    };
                    openButton.Click += (s, e) =>
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = clientUrl,
                            UseShellExecute = true
                        });
                        notificationForm.Close();
                    };

                    // Add controls to form
                    notificationForm.Controls.Add(titleLabel);
                    notificationForm.Controls.Add(messageLabel);
                    notificationForm.Controls.Add(openButton);

                    // Add headshot if provided
                    if (!string.IsNullOrEmpty(headshotUrl))
                    {
                        try
                        {
                            using (var httpClient = new HttpClient())
                            {
                                var response = await httpClient.GetAsync(headshotUrl);
                                if (response.IsSuccessStatusCode)
                                {
                                    var imageBytes = await response.Content.ReadAsByteArrayAsync();
                                    using (var ms = new System.IO.MemoryStream(imageBytes))
                                    {
                                        var headshot = Image.FromStream(ms);
                                        var headshotPicture = new PictureBox
                                        {
                                            Image = headshot,
                                            SizeMode = PictureBoxSizeMode.Zoom,
                                            Size = new Size(80, 80),
                                            Location = new Point(notificationForm.Width - 90, 10)
                                        };
                                        notificationForm.Controls.Add(headshotPicture);
                                    }
                                }
                            }
                        }
                        catch (System.Exception ex)
                        {
                            SystemControl.Log($"Error loading headshot: {ex.Message}");
                        }
                    }

                    // Show the notification
                    notificationForm.Show();
                    
                    // Auto-close after 10 seconds
                    var timer = new Timer { Interval = 10000 };
                    timer.Tick += (s, e) =>
                    {
                        timer.Stop();
                        notificationForm.Close();
                    };
                    timer.Start();
                }
            }
            catch (System.Exception ex)
            {
                SystemControl.Log($"Error showing call notification: {ex.Message}");
            }
        }
    }
}
