﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Office.Interop.Outlook;
using System.Net.Http;
using System.Drawing;

namespace SurefireTray
{
    public partial class SystemTray : Form
    {
        private HubConnection _connection;
        private string _userId;
        private string _logFilePath = @"C:\SUREFIRE\TrayLog.txt";
        
        // Notification tracking
        private static Dictionary<string, DateTime> _recentNotifications = new Dictionary<string, DateTime>();
        private static object _notificationLock = new object();
        private static int _notificationCooldown = 30; // Changed from 5 to 30 seconds
        
        // Connection status tracking
        private bool _isConnected = false;
        private Timer _reconnectTimer;
        private int _reconnectAttempt = 0;
        private readonly int[] _reconnectIntervals = { 10, 10, 10, 30, 60, 120, 300, 3000, 10000 }; // Seconds between reconnect attempts
        private ToolStripMenuItem _reconnectMenuItem;
        private ToolStripMenuItem _statusMenuItem;
        private Timer _connectionCheckTimer;
        private bool _isLocalhostMode = false;
        private ToolStripMenuItem _localhostModeMenuItem;

        // Icons for different connection states
        private Icon _connectedIcon;
        private Icon _disconnectedIcon;

        public SystemTray()
        {
            InitializeComponent();

            // Load icons
            _connectedIcon = Properties.Resources.notify;
            _disconnectedIcon = Properties.Resources.notify; // Replace with a different icon if available

            // Hide the main window on startup
            this.WindowState = FormWindowState.Minimized;
            this.ShowInTaskbar = false;
            this.ControlBox = false;
            this.Text = string.Empty;
            this.FormBorderStyle = FormBorderStyle.None;

            // Configure the NotifyIcon
            SurefireEmberIcon.Icon = _connectedIcon;
            SurefireEmberIcon.Visible = true;
            SurefireEmberIcon.Text = "Surefire Tray - Initializing...";

            // Setup context menu
            SetupContextMenu();

            // Initialize user ID
            _userId = Environment.UserName;
            
            // Initialize reconnect timer
            _reconnectTimer = new Timer();
            _reconnectTimer.Tick += ReconnectTimer_Tick;
            
            // Initialize connection check timer
            _connectionCheckTimer = new Timer();
            _connectionCheckTimer.Interval = 60000; // Check every minute
            _connectionCheckTimer.Tick += ConnectionCheckTimer_Tick;
            _connectionCheckTimer.Start();
            
            // Register for system events
            System.Windows.Forms.Application.ApplicationExit += Application_ApplicationExit;
        }

        private void SetupContextMenu()
        {
            // Start with a clean slate – clear designer-added items to avoid duplicates
            EmberContextMenu.Items.Clear();

            // Add status indicator to the context menu (first item)
            _statusMenuItem = new ToolStripMenuItem("Status: Initializing...");
            _statusMenuItem.Enabled = false;
            EmberContextMenu.Items.Insert(0, _statusMenuItem);
            
            // Add separator
            EmberContextMenu.Items.Insert(1, new ToolStripSeparator());

            // Add "Reconnect" item with keyboard shortcut
            _reconnectMenuItem = new ToolStripMenuItem("Reconnect to Server", null, Reconnect_Click, Keys.Control | Keys.R);
            EmberContextMenu.Items.Insert(2, _reconnectMenuItem);
            _reconnectMenuItem.Enabled = false;
            
            // Add "Localhost Mode" toggle
            _localhostModeMenuItem = new ToolStripMenuItem("Localhost Mode");
            _localhostModeMenuItem.CheckOnClick = true;
            _localhostModeMenuItem.Click += LocalhostMode_Click;
            EmberContextMenu.Items.Insert(3, _localhostModeMenuItem);
            
            // Add "Show Debug Console" item
            ToolStripMenuItem consoleMenuItem = new ToolStripMenuItem("Show Debug Console", null, ShowDebugConsole_Click, Keys.Control | Keys.D);
            EmberContextMenu.Items.Insert(4, consoleMenuItem);

            // Add "Clear Debug Log" item
            ToolStripMenuItem clearLogMenuItem = new ToolStripMenuItem("Clear Debug Log");
            clearLogMenuItem.Click += ClearDebugLog_Click;
            EmberContextMenu.Items.Insert(5, clearLogMenuItem);
            
            // Add separator
            EmberContextMenu.Items.Insert(6, new ToolStripSeparator());

            // Keep the "Exit" item as last
            ToolStripMenuItem exitMenuItem = new ToolStripMenuItem("Exit", null, Exit_Click, Keys.Control | Keys.Q);
            EmberContextMenu.Items.Insert(7, exitMenuItem);
        }

        private void Application_ApplicationExit(object sender, EventArgs e)
        {
            // Clean up connection when application exits
            if (_connection != null)
            {
                try
                {
                    Log("Application exiting - closing SignalR connection");
                    _connection.StopAsync().Wait(1000); // Give it a second to close gracefully
                }
                catch
                {
                    // Ignore errors during shutdown
                }
            }
        }

        private async void SystemTray_Load(object sender, EventArgs e)
        {
            Log("=========================================================");
            Log("-------------------- SUREFIRE TRAY!! ----------------------");
            Log($"[SystemTray] Application startup at: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            Log($"[SystemTray] User: {Environment.UserName}");
            Log($"[SystemTray] Machine: {Environment.MachineName}");
            Log($"[SystemTray] OS: {Environment.OSVersion}");
            Log($"[SystemTray] .NET Version: {Environment.Version}");
            Log($"[SystemTray] Working Directory: {Environment.CurrentDirectory}");
            Log($"[SystemTray] Available Word command: GetWordDocContents");
            await StartSignalRConnection();
        }

        private async Task StartSignalRConnection()
        {
            string myurl = _isLocalhostMode ? "https://localhost:7074/emberHub" : "https://surefire.local/emberHub";

            Log("=========================================================");
            Log("-------------------- SUREFIRE TRAY ----------------------");
            Log($"     SignalR:   Starting connection to {myurl}");
            Log($"       UserId:   {_userId}");
            Log($"       Auth:     Checking authentication state...");

            _connection = new HubConnectionBuilder()
                .WithUrl(myurl, options =>
                {
                    options.HttpMessageHandlerFactory = _ => new HttpClientHandler
                    {
                        ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator
                    };
                    // Increase buffer sizes for large data transfers (like Word documents)
                    options.ApplicationMaxBufferSize = 10 * 1024 * 1024; // 10MB
                    options.TransportMaxBufferSize = 10 * 1024 * 1024; // 10MB
                })
                .WithAutomaticReconnect(new[] { TimeSpan.Zero, TimeSpan.FromSeconds(2), TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(30) })
                .Build();

            // Listen for incoming commands
            _connection.On<string, List<string>>("ReceiveEmberCommand", (emberFunction, parameters) =>
            {
                Log($"[SystemTray] ========== ReceiveEmberCommand START ==========");
                Log($"[SystemTray] Received ember command: '{emberFunction}'");
                Log($"[SystemTray] Parameters count: {parameters?.Count ?? 0}");
                if (parameters != null && parameters.Count > 0)
                {
                    Log($"[SystemTray] Parameters: [{string.Join(", ", parameters.Select(p => $"'{p}'"))}]");
                }
                Log($"[SystemTray] Connection state: {_connection.State}");
                Log($"[SystemTray] Is authenticated: {_connection.State == HubConnectionState.Connected}");
                Log($"[SystemTray] Current user ID: '{_userId}'");
                Log($"[SystemTray] Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");

                try
                {
                    Log($"[SystemTray] Routing command to appropriate handler...");
                    
                    // Check and call appropriate functions based on the emberFunction prefix
                    if (emberFunction.ToLower().StartsWith("outlooksearch_"))
                    {
                        Log($"[SystemTray] Routing to OutlookControl (OutlookSearch_ prefix)");
                        OutlookControl.PerformOutlookFunction(emberFunction, parameters);
                    }
                    else if (emberFunction.ToLower().StartsWith("windows_"))
                    {
                        Log($"[SystemTray] Routing to WindowsControl (Windows_ prefix)");
                        WindowsControl.PerformWindowsFunction(emberFunction, parameters);
                    }
                    else if (emberFunction.ToLower().Equals("getworddoccontents"))
                    {
                        Log($"[SystemTray] Routing to WordControl (GetWordDocContents command)");
                        WordControl.PerformWordFunction(emberFunction, parameters);
                    }
                    else if (emberFunction.ToLower().Equals("showtraynotification"))
                    {
                        Log($"[SystemTray] Routing to ShowTrayNotification");
                        ShowTrayNotification(parameters);
                    }
                    else if (emberFunction.ToLower().Equals("showstaffchat"))
                    {
                        Log($"[SystemTray] Routing to ShowStaffChat");
                        ShowStaffChatNotification(parameters);
                    }
                    else
                    {
                        Log($"[SystemTray] No specific handler found, using fallback to OutlookControl");
                        Log($"[SystemTray] Available commands: OutlookSearch_*, Windows_*, GetWordDocContents, ShowTrayNotification");
                        // Fallback to try the general handler
                        OutlookControl.PerformOutlookFunction(emberFunction, parameters);
                    }
                    
                    Log($"[SystemTray] Command handler completed successfully");
                    Log($"[SystemTray] ========== ReceiveEmberCommand END (Success) ==========");
                }
                catch (System.Exception ex)
                {
                    Log($"[SystemTray] ========== ReceiveEmberCommand ERROR ==========");
                    Log($"[SystemTray] Exception Type: {ex.GetType().Name}");
                    Log($"[SystemTray] Exception Message: {ex.Message}");
                    Log($"[SystemTray] Stack Trace: {ex.StackTrace}");
                    
                    if (ex.InnerException != null)
                    {
                        Log($"[SystemTray] Inner Exception: {ex.InnerException.Message}");
                    }
                    
                    Log($"[SystemTray] Failed command: '{emberFunction}'");
                    Log($"[SystemTray] ========== ReceiveEmberCommand END (Exception) ==========");
                }
            });

            try
            {
                await _connection.StartAsync();
                _isConnected = true;
                _reconnectAttempt = 0;
                _reconnectMenuItem.Enabled = false;
                
                UpdateConnectionStatus("Connected", true);
                Log("     Status: Connected");
                Log($"Connection state: {_connection.State}");
                Log($"Authentication state: {_connection.State == HubConnectionState.Connected}");

                await _connection.InvokeAsync("JoinGroup", _userId);
                Log("       Join: Success!");
                Log("=========================================================");
            }
            catch (System.Exception ex)
            {
                _isConnected = false;
                _reconnectMenuItem.Enabled = true;
                
                UpdateConnectionStatus("Disconnected", false);
                Log($"Error starting SignalR connection: {ex.Message}");
                Log($"Connection state: {_connection.State}");
                Log($"Authentication state: {_connection.State == HubConnectionState.Connected}");
                Log($"Stack trace: {ex.StackTrace}");
                ShowReconnectNotification();
                StartReconnectTimer();
            }
        }

        private Task Connection_Closed(System.Exception arg)
        {
            _isConnected = false;
            _reconnectMenuItem.Enabled = true;
            
            UpdateConnectionStatus("Disconnected", false);
            Log($"SignalR connection closed: {arg?.Message ?? "Unknown reason"}");
            ShowReconnectNotification();
            StartReconnectTimer();
            return Task.CompletedTask;
        }

        private Task Connection_Reconnecting(System.Exception arg)
        {
            _isConnected = false;
            UpdateConnectionStatus("Reconnecting...", false);
            Log($"SignalR attempting to reconnect: {arg?.Message ?? "Unknown reason"}");
            return Task.CompletedTask;
        }

        private async Task Connection_Reconnected(string arg)
        {
            _isConnected = true;
            _reconnectAttempt = 0;
            _reconnectMenuItem.Enabled = false;
            
            UpdateConnectionStatus("Connected", true);
            Log($"SignalR reconnected with connection ID: {arg}");
            
            try
            {
                await _connection.InvokeAsync("JoinGroup", _userId);
                Log("Rejoined SignalR group after reconnection");
                
                // Stop the timer if it's running
                _reconnectTimer.Stop();
                
                // Clear notification and update icon
                SurefireEmberIcon.BalloonTipText = "";
                ShowConnectedNotification();
            }
            catch (System.Exception ex)
            {
                Log($"Error rejoining group after reconnection: {ex.Message}");
            }
        }

        private void UpdateConnectionStatus(string status, bool isConnected)
        {
            // Update tray icon and menu
            _statusMenuItem.Text = $"Status: {status}";
            SurefireEmberIcon.Icon = isConnected ? _connectedIcon : _disconnectedIcon;
            SurefireEmberIcon.Text = $"Surefire Tray - {status}";
            
            // Force refresh of the icon
            SurefireEmberIcon.Visible = false;
            SurefireEmberIcon.Visible = true;
        }

        private void StartReconnectTimer()
        {
            // Get the appropriate interval based on the number of attempts
            int intervalIndex = Math.Min(_reconnectAttempt, _reconnectIntervals.Length - 1);
            int intervalSeconds = _reconnectIntervals[intervalIndex];
            
            Log($"Scheduling reconnection attempt in {intervalSeconds} seconds");
            
            _reconnectTimer.Interval = intervalSeconds * 1000;
            _reconnectTimer.Start();
        }

        private async void ReconnectTimer_Tick(object sender, EventArgs e)
        {
            _reconnectTimer.Stop();
            
            if (!_isConnected)
            {
                _reconnectAttempt++;
                Log($"Automatic reconnection attempt #{_reconnectAttempt}");
                await TryReconnect();
            }
        }

        private void ConnectionCheckTimer_Tick(object sender, EventArgs e)
        {
            // Only check if we think we're connected
            if (_isConnected && (_connection?.State != HubConnectionState.Connected))
            {
                Log("Connection check detected disconnection");
                _isConnected = false;
                _reconnectMenuItem.Enabled = true;
                UpdateConnectionStatus("Disconnected", false);
                ShowReconnectNotification();
                StartReconnectTimer();
            }
        }

        private async void Reconnect_Click(object sender, EventArgs e)
        {
            if (!_isConnected)
            {
                _reconnectAttempt = 0; // Reset the counter for manual reconnection
                Log("Manual reconnection requested");
                await TryReconnect();
            }
        }

        private async Task TryReconnect()
        {
            try
            {
                UpdateConnectionStatus("Reconnecting...", false);
                
                if (_connection.State == HubConnectionState.Disconnected)
                {
                    await _connection.StartAsync();
                    _isConnected = true;
                    _reconnectMenuItem.Enabled = false;
                    
                    UpdateConnectionStatus("Connected", true);
                    Log("SignalR connection reestablished");
                    
                    await _connection.InvokeAsync("JoinGroup", _userId);
                    Log("Rejoined SignalR group after manual reconnection");
                    
                    // Clear notification and show success
                    SurefireEmberIcon.BalloonTipText = "";
                    ShowConnectedNotification();
                }
            }
            catch (System.Exception ex)
            {
                _isConnected = false;
                UpdateConnectionStatus("Disconnected", false);
                Log($"Reconnection attempt failed: {ex.Message}");
                StartReconnectTimer();
            }
        }

        private void ShowReconnectNotification()
        {
            SurefireEmberIcon.BalloonTipTitle = "Connection Lost";
            SurefireEmberIcon.BalloonTipText = "Connection to server lost. Click to reconnect.";
            SurefireEmberIcon.BalloonTipIcon = ToolTipIcon.Warning;
            SurefireEmberIcon.ShowBalloonTip(5000);
            
            // Add click handler for the notification icon
            SurefireEmberIcon.BalloonTipClicked += async (s, e) => await TryReconnect();
            SurefireEmberIcon.Click += async (s, e) => 
            {
                if (!_isConnected)
                {
                    await TryReconnect();
                }
            };
        }
        
        private void ShowConnectedNotification()
        {
            SurefireEmberIcon.BalloonTipTitle = "Connection Restored";
            SurefireEmberIcon.BalloonTipText = "Successfully reconnected to the server.";
            SurefireEmberIcon.BalloonTipIcon = ToolTipIcon.Info;
            SurefireEmberIcon.ShowBalloonTip(3000);
        }

        private void ShowDebugConsole_Click(object sender, EventArgs e)
        {
            ConsoleWindow.ToggleConsoleWindow();
        }

        private void Exit_Click(object sender, EventArgs e)
        {
            SurefireEmberIcon.Visible = false;
            System.Windows.Forms.Application.Exit();
        }

        private void ClearDebugLog_Click(object sender, EventArgs e)
        {
            try
            {
                if (File.Exists(_logFilePath))
                {
                    File.WriteAllText(_logFilePath, string.Empty); // Clear the file
                    MessageBox.Show("Debug log cleared successfully.", "Log Cleared", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("No debug log file found to clear.", "Log Not Found", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"Failed to clear the debug log. Error: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void Log(string message)
        {
            try
            {
                File.AppendAllText(@"C:\SUREFIRE\TrayLog.txt", $"{DateTime.Now}: {message}\n");
            }
            catch (System.Exception ex)
            {
                Console.WriteLine($"Failed to write to log: {ex.Message}");
            }
        }

        public static async void SendEmberResponse(string command, List<string> responseData)
        {
            Log($"[SystemTray] ========== SendEmberResponse START ==========");
            Log($"[SystemTray] Command: '{command}'");
            Log($"[SystemTray] Response data count: {responseData?.Count ?? 0}");
            
            if (responseData != null && responseData.Count > 0)
            {
                for (int i = 0; i < responseData.Count; i++)
                {
                    var data = responseData[i];
                    if (data != null)
                    {
                        // Log first 200 characters of each response item for debugging
                        string preview = data.Length > 200 ? data.Substring(0, 200) + "..." : data;
                        Log($"[SystemTray] Response[{i}] length: {data.Length}, preview: '{preview.Replace('\r', ' ').Replace('\n', ' ')}'");
                    }
                    else
                    {
                        Log($"[SystemTray] Response[{i}] is null");
                    }
                }
            }
            
            try
            {
                Log($"[SystemTray] Step 1: Finding SystemTray form instance...");
                
                // Find the SystemTray instance
                var forms = System.Windows.Forms.Application.OpenForms;
                Log($"[SystemTray] Total open forms: {forms.Count}");
                
                SystemTray systemTrayForm = null;
                foreach (System.Windows.Forms.Form form in forms)
                {
                    Log($"[SystemTray] Checking form: {form.GetType().Name}");
                    if (form is SystemTray)
                    {
                        systemTrayForm = (SystemTray)form;
                        Log($"[SystemTray] Found SystemTray form instance");
                        break;
                    }
                }

                if (systemTrayForm == null)
                {
                    Log($"[SystemTray] ERROR: Could not find SystemTray form instance for sending response");
                    Log($"[SystemTray] Available forms: {string.Join(", ", forms.Cast<System.Windows.Forms.Form>().Select(f => f.GetType().Name))}");
                    Log($"[SystemTray] ========== SendEmberResponse END (No form) ==========");
                    return;
                }

                Log($"[SystemTray] Step 2: Checking SignalR connection...");
                Log($"[SystemTray] Connection state: {systemTrayForm._connection?.State ?? Microsoft.AspNetCore.SignalR.Client.HubConnectionState.Disconnected}");
                Log($"[SystemTray] UserId: '{systemTrayForm._userId ?? "null"}'");

                if (systemTrayForm._connection == null)
                {
                    Log($"[SystemTray] ERROR: SignalR connection is null");
                    Log($"[SystemTray] ========== SendEmberResponse END (No connection) ==========");
                    return;
                }

                if (string.IsNullOrEmpty(systemTrayForm._userId))
                {
                    Log($"[SystemTray] ERROR: UserId is null or empty");
                    Log($"[SystemTray] ========== SendEmberResponse END (No userId) ==========");
                    return;
                }

                if (systemTrayForm._connection.State != Microsoft.AspNetCore.SignalR.Client.HubConnectionState.Connected)
                {
                    Log($"[SystemTray] WARNING: SignalR connection is not in Connected state: {systemTrayForm._connection.State}");
                    Log($"[SystemTray] Attempting to send anyway...");
                }

                Log($"[SystemTray] Step 3: Sending response via SignalR...");
                Log($"[SystemTray] Hub method: 'SendEmberResponse'");
                Log($"[SystemTray] Parameters: userId='{systemTrayForm._userId}', command='{command}', responseData.Count={responseData?.Count ?? 0}");

                // Send the response back to the server
                await systemTrayForm._connection.InvokeAsync("SendEmberResponse", systemTrayForm._userId, command, responseData);
                
                Log($"[SystemTray] SUCCESS: Response sent successfully for command: {command}");
                Log($"[SystemTray] ========== SendEmberResponse END (Success) ==========");
            }
            catch (System.Exception ex)
            {
                Log($"[SystemTray] ========== SendEmberResponse ERROR ==========");
                Log($"[SystemTray] Exception Type: {ex.GetType().Name}");
                Log($"[SystemTray] Exception Message: {ex.Message}");
                Log($"[SystemTray] Stack Trace: {ex.StackTrace}");
                
                if (ex.InnerException != null)
                {
                    Log($"[SystemTray] Inner Exception: {ex.InnerException.Message}");
                }
                
                Log($"[SystemTray] Command that failed: '{command}'");
                Log($"[SystemTray] ========== SendEmberResponse END (Exception) ==========");
            }
        }

        private void ShowTrayNotification(List<string> parameters)
        {
            try
            {
                if (parameters == null || parameters.Count < 2)
                {
                    Log("Invalid parameters for ShowTrayNotification. Expected at least title and message.");
                    return;
                }

                string title = parameters[0];
                string message = parameters[1];
                ToolTipIcon icon = ToolTipIcon.None; // Set to None to prevent sound
                string clientId = null;

                // If we have a third parameter, it's the ClientId
                if (parameters.Count > 2)
                {
                    clientId = parameters[2];
                    Log($"Call notification for ClientId: {clientId}");
                    
                    // If we have a fourth parameter, it's the icon type
                    if (parameters.Count > 3)
                    {
                        if (Enum.TryParse<ToolTipIcon>(parameters[3], true, out ToolTipIcon parsedIcon))
                        {
                            icon = parsedIcon;
                        }
                    }
                }
                
                // Create a more robust notification key that handles null/empty values better
                // Include timestamp in seconds to ensure very similar notifications within a few milliseconds are still shown
                // Format: title|message|clientId|timestamp (using | as separator which is unlikely to be in the strings)
                string timestamp = DateTime.Now.ToString("HH:mm:ss");
                string notificationKey = $"{title}|{message}|{clientId ?? "none"}";
                
                Log($"Processing notification with key: {notificationKey}");
                
                bool isDuplicate = false;
                
                // Check if this is a duplicate notification
                lock(_notificationLock)
                {
                    // Clean up expired notifications
                    var expiredKeys = _recentNotifications.Where(kv => 
                        (DateTime.Now - kv.Value).TotalSeconds > _notificationCooldown)
                        .Select(kv => kv.Key).ToList();
                        
                    foreach(var key in expiredKeys)
                    {
                        _recentNotifications.Remove(key);
                        Log($"Removed expired notification: {key}");
                    }
                    
                    // Check if this notification was recently shown
                    if (_recentNotifications.ContainsKey(notificationKey))
                    {
                        isDuplicate = true;
                        Log($"Skipping duplicate notification: {notificationKey} (shown {(DateTime.Now - _recentNotifications[notificationKey]).TotalSeconds:0.00} seconds ago)");
                    }
                    else
                    {
                        // Add to recent notifications
                        _recentNotifications[notificationKey] = DateTime.Now;
                        Log($"Added new notification to tracking: {notificationKey}");
                    }
                }
                
                // Return early if it's a duplicate
                if (isDuplicate)
                {
                    Log($"Duplicate... skipping.");
                    return;
                    
                }

                // Remove any existing click handlers
                SurefireEmberIcon.BalloonTipClicked -= OnBalloonTipClicked;
                
                // Store the clientId for the click handler
                SurefireEmberIcon.Tag = clientId;

                SurefireEmberIcon.BalloonTipTitle = title;
                SurefireEmberIcon.BalloonTipText = message;
                SurefireEmberIcon.BalloonTipIcon = icon;

                // Add click handler if we have a ClientId
                if (!string.IsNullOrEmpty(clientId))
                {
                    SurefireEmberIcon.BalloonTipClicked += OnBalloonTipClicked;
                }

                SurefireEmberIcon.ShowBalloonTip(5000); // Show for 5 seconds

                Log($"Showing tray notification - Title: {title}, Message: {message}, Icon: {icon}");
            }
            catch (System.Exception ex)
            {
                Log($"Error showing tray notification: {ex.Message}");
            }
        }

        private void ShowStaffChatNotification(List<string> parameters)
        {
            try
            {
                if (parameters == null || parameters.Count < 3)
                {
                    Log("Invalid parameters for ShowStaffChatNotification. Expected at least sender name, message, and sender full name.");
                    return;
                }

                string senderName = parameters[0];
                string message = parameters[1];
                string senderFullName = parameters[2];

                string title = $"Staff Message from {senderFullName}";
                string notificationMessage = $"{senderName}: {message}";

                // Create notification key for cooldown (handled by main app, but log for debugging)
                Log($"Staff chat notification - From: {senderFullName} ({senderName}), Message: {message}");

                // Remove any existing click handlers
                SurefireEmberIcon.BalloonTipClicked -= OnStaffChatBalloonTipClicked;
                
                // Set special tag to indicate this is a staff chat notification
                SurefireEmberIcon.Tag = "STAFF_CHAT";

                SurefireEmberIcon.BalloonTipTitle = title;
                SurefireEmberIcon.BalloonTipText = notificationMessage;
                SurefireEmberIcon.BalloonTipIcon = ToolTipIcon.Info;

                // Add click handler for staff chat
                SurefireEmberIcon.BalloonTipClicked += OnStaffChatBalloonTipClicked;

                SurefireEmberIcon.ShowBalloonTip(8000); // Show for 8 seconds

                Log($"Showing staff chat notification - Title: {title}, Message: {notificationMessage}");
            }
            catch (System.Exception ex)
            {
                Log($"Error showing staff chat notification: {ex.Message}");
            }
        }

        private void OnStaffChatBalloonTipClicked(object sender, EventArgs e)
        {
            try
            {
                Log("Staff chat notification clicked - attempting to bring Surefire window to front");
                
                // Try to find and bring the Surefire Edge app window to front
                var surefireProcess = System.Diagnostics.Process.GetProcesses()
                    .FirstOrDefault(p => 
                        p.MainWindowTitle.ToLower().Contains("surefire") ||
                        p.ProcessName.ToLower().Contains("msedge") && 
                        p.MainWindowTitle.ToLower().Contains("surefire"));
                
                if (surefireProcess != null)
                {
                    Log($"Found Surefire window: {surefireProcess.MainWindowTitle} (Process: {surefireProcess.ProcessName})");
                    WindowsControl.BringWindowToFront(surefireProcess.MainWindowHandle);
                }
                else
                {
                    // Fallback - try to find any Edge process with a window title containing relevant terms
                    var edgeProcesses = System.Diagnostics.Process.GetProcesses()
                        .Where(p => p.ProcessName.ToLower().Contains("msedge") && 
                                   !string.IsNullOrEmpty(p.MainWindowTitle))
                        .ToList();
                    
                    Log($"Found {edgeProcesses.Count} Edge processes with windows");
                    foreach (var proc in edgeProcesses)
                    {
                        Log($"Edge process: {proc.MainWindowTitle}");
                    }
                    
                    // Try to find one that might be Surefire
                    var likelyProcess = edgeProcesses
                        .FirstOrDefault(p => p.MainWindowTitle.ToLower().Contains("surefire") ||
                                           p.MainWindowTitle.ToLower().Contains("localhost"));
                    
                    if (likelyProcess != null)
                    {
                        Log($"Found likely Surefire process: {likelyProcess.MainWindowTitle}");
                        WindowsControl.BringWindowToFront(likelyProcess.MainWindowHandle);
                    }
                    else
                    {
                        // Last resort - open Surefire URL
                        string surefireUrl = "https://surefire.local/";
                        Log($"Surefire window not found, opening URL: {surefireUrl}");
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = surefireUrl,
                            UseShellExecute = true
                        });
                    }
                }
            }
            catch (System.Exception ex)
            {
                Log($"Error handling staff chat notification click: {ex.Message}");
            }
        }

        private void OnBalloonTipClicked(object sender, EventArgs e)
        {
            try
            {
                // Get the clientId from the tag
                string clientId = SurefireEmberIcon.Tag as string;
                
                if (string.IsNullOrEmpty(clientId))
                {
                    Log("No clientId found for notification click");
                    return;
                }
                
                // First try to find and bring the Edge app window to front
                var edgeProcess = System.Diagnostics.Process.GetProcesses()
                    .FirstOrDefault(p => p.MainWindowTitle.ToLower().Equals("surefire"));
                
                if (edgeProcess != null)
                {
                    Log($"Found Edge app window: {edgeProcess.MainWindowTitle}");
                    WindowsControl.BringWindowToFront(edgeProcess.MainWindowHandle);
                }
                else
                {
                    // Fallback to opening URL if window not found
                    string clientUrl = $"https://surefire.local/Clients/{clientId}";
                    Log($"Edge app window not found, opening URL: {clientUrl}");
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = clientUrl,
                        UseShellExecute = true
                    });
                }
            }
            catch (System.Exception ex)
            {
                Log($"Error handling notification click: {ex.Message}");
            }
        }

        private async void LocalhostMode_Click(object sender, EventArgs e)
        {
            _isLocalhostMode = _localhostModeMenuItem.Checked;
            Log($"Localhost mode {( _isLocalhostMode ? "enabled" : "disabled" )}");
            
            // Disconnect current connection
            if (_connection != null)
            {
                try
                {
                    await _connection.StopAsync();
                }
                catch
                {
                    // Ignore errors during disconnect
                }
            }
            
            // Start new connection with updated URL
            await StartSignalRConnection();
        }
    }
}