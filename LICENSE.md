# ﻿Surefire Public License Version 1.0 ("SPL")

Surefire open-source edition is licensed under Surefire Public License. Essentially it's a GNU Affero General Public License version 3 (GNU AGPL v3.0) with the additional requirement that you put "powered by Surefire" on every single page. The Surefire Public License Version 1.0 ("SPL") consists of the GNU AGPL v3.0 License with the Additional Terms below. The original GNU AGPL v3.0 License can be found at: http://opensource.org/licenses/GPL-3.0

**Additional Surefire terms:**

However, in addition to the other notice obligations, (1) all copies of the Program in Executable and Source Code form must, as a form of attribution of the original author, include on each user interface screen (i) the "powered by Surefire" text; and (2) all derivative works and copies of derivative works of the Covered Code in Executable and Source Code form must include on each user interface screen (i) the "powered by Surefire" text. In addition, the "powered by Surefire" text, as appropriate, must be visible to all users, must appear in each user interface screen, and must be in the same position. When users click on the "powered by Surefire" text it must direct them to https://www.surefireams.com. This obligation shall also apply to any copies or derivative works.

# Commercial License

Independent Software Vendors that want the benefits of embedding Surefire software in their commercial applications but do not want to be subject to the Surefire Public License ("SPL") and do not want to release the source code for their proprietary applications must purchase a commercial license from the Surefire team. Purchasing a commercial license means that the Surefire Public License ("SPL") does not apply, and a commercial license includes the assurances that distributors typically find in commercial distribution agreements. If use of Surefire under the SPL does not satisfy your organization's legal department you should also enter into a commercial license agreement with the Surefire team.

Feel free to contact us for more details - https://www.surefireams.com/
