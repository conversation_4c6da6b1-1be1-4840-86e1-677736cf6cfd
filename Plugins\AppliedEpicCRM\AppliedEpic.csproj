﻿<Project Sdk="Microsoft.NET.Sdk">
	<ItemGroup>
		<ProjectReference Include="..\..\Surefire\Surefire.csproj" />
	</ItemGroup>
	
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<CopyLocalLockFileAssemblies>false</CopyLocalLockFileAssemblies>
		<OutputPath>..\..\Surefire\Plugins\AppliedEpic</OutputPath>
		<OutDir>$(OutputPath)</OutDir>
	</PropertyGroup>

	<Target Name="ClearPluginAssemblies" AfterTargets="Build">
		<Message Importance="high" Text="Cleaning up AppliedEpic assemblies after build..." />
		<!-- Delete the unwanted files -->
		<Delete Files="$(OutDir)\Surefire.exe" />
		<Delete Files="$(OutDir)\Surefire.dll" />
		<Delete Files="$(OutDir)\Surefire.pdb" />
		<Delete Files="$(OutDir)\Surefire.deps.json" />
		<Delete Files="$(OutDir)\Surefire.staticwebassets.runtime.json" />
		<Delete Files="$(OutDir)\Surefire.staticwebassets.endpoints.json" />
		<Delete Files="$(OutDir)\Surefire.runtimeconfig.json" />
		<Delete Files="$(OutDir)\appsettings.json" />
		<Delete Files="$(OutDir)\appsettings.Development.json" />
	</Target>

	<Target Name="PostBuild" AfterTargets="PostBuildEvent">
		<!-- Remove all "Plugins" folders in subdirectories -->
		<Exec Command="for /d %%d in (&quot;$(SolutionDir)Surefire\bin\Plugins\*&quot;) do if exist %%d\Plugins rmdir /s /q &quot;%%d\Plugins&quot;" />
	</Target>
</Project>
