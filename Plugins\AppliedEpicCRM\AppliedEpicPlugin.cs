using Surefire.Data;
using Surefire.Domain.Clients.Models;
using Surefire.Domain.Plugins;
using Surefire.Domain.Contacts.Models;
using Surefire.Domain.Policies.Models;
using Surefire.Domain.Shared.Models;
using Surefire.Domain.Shared.Services;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net.Http.Headers;
using Microsoft.Extensions.Options;

namespace Surefire.Plugins.AppliedEpic;

public class AppliedEpicDataSync : IPlugin
{
    private readonly HttpClient _httpClient;
    private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
    private readonly AppliedEpicOptions _options;
    private readonly StateService _stateService;

    public string Name => "Applied Epic Cloud API";
    public bool IsActive { get; set; } = true;

    public AppliedEpicDataSync(StateService stateService, HttpClient httpClient, IDbContextFactory<ApplicationDbContext> dbContextFactory, IOptions<AppliedEpicOptions> options)
    {
        _httpClient = httpClient;
        _dbContextFactory = dbContextFactory;
        _options = options.Value;
        _stateService = stateService;
    }

    public async Task<PluginMethodResponse> ExecuteAsync(string methodName, object[] parameters, CancellationToken cancellationToken)
    {
        return methodName switch
        {
            "DataSync" => await DataSync((int)parameters[0], (bool)parameters[1], cancellationToken),
            "GetContacts" => await GetContacts((string)parameters[0]),
            _ => new PluginMethodResponse
            {
                success = false,
                message = $"Method {methodName} is not implemented in {Name} plugin."
            }
        };
    }

    public void RegisterEvents(IServiceProvider serviceProvider)
    {
        Console.WriteLine($"Registering events for plugin: {Name}");
    }

    public async Task<PluginMethodResponse> DataSync(int clientId, bool forceUpdate, CancellationToken cancellationToken)
    {
        using var context = _dbContextFactory.CreateDbContext();
        PluginMethodResponse pluginresponse = new PluginMethodResponse();

        // Fetch plugin-specific API configuration from the database
        var pluginConfig = await context.Plugins
            .Where(p => p.Name == Name)
            .Select(p => new
            {
                p.ClientId,
                p.ClientSecret,
                p.BaseUri
            })
            .FirstOrDefaultAsync();

        if (pluginConfig == null || string.IsNullOrEmpty(pluginConfig.ClientId) || string.IsNullOrEmpty(pluginConfig.ClientSecret) || string.IsNullOrEmpty(pluginConfig.BaseUri))
        {
            Console.Error.WriteLine("Plugin configuration is missing or incomplete.");
            pluginresponse.success = false;
            return pluginresponse;
        }

        // Fetch the client from the database
        var client = await context.Clients.FindAsync(clientId);
        if (client == null)
        {
            Console.Error.WriteLine($"Client with ID {clientId} not found.");
            pluginresponse.success = false;
            return pluginresponse;
        }

        var today = DateTime.Now.Date;
        if (client.DateOpened != null && client.DateOpened.Date == today && forceUpdate != true)
        {
            _stateService.UpdateStatus($"Policies are already in sync.", false);
            Console.WriteLine($"SYNC: Policies for {client.Name} have already been synced today at {client.DateOpened.Date}!");
            pluginresponse.success = true;
            return pluginresponse;
        }
        cancellationToken.ThrowIfCancellationRequested();

        // Retrieve access token
        var accessToken = await GetAccessTokenAsync(pluginConfig.ClientId, pluginConfig.ClientSecret, pluginConfig.BaseUri);
        try
        {
            //Fetch client id from API if needed
            if (client.eClientId == null)
            {
                var clientDetails = await GetClientIdAsync(client.LookupCode, accessToken, pluginConfig.BaseUri);
                if (clientDetails.ClientId == null)
                {
                    Console.Error.WriteLine("Failed to fetch client details from API");
                    pluginresponse.success = false;
                    return pluginresponse;
                }
                client.eClientId = clientDetails.ClientId;
            }
            cancellationToken.ThrowIfCancellationRequested();

            // Fetch policies for the client from API
            var policies = await GetPoliciesAsync(client.eClientId, accessToken, pluginConfig.BaseUri);
            Console.WriteLine($"SYNC: Fetched {policies.Count} policies from API");

            // Update client and policies in the database
            int newpolicies = await UpdatePoliciesWithLinesDataAsync(context, policies, client, accessToken, pluginConfig.BaseUri, cancellationToken);
            client.DateOpened = DateTime.Now;
            await context.SaveChangesAsync();

            Console.WriteLine($"SYNC: DataSync completed for client ID: {clientId}");
            pluginresponse.success = true;
            pluginresponse.cleanup = true;
            pluginresponse.message = $"Added {newpolicies} new policies to the client.";
            if (newpolicies > 0)
            {
                _stateService.UpdateStatus($"Successfully added {newpolicies} new policies to the client.", false);
            }
            else
            {
                _stateService.UpdateStatus($"Policies are in sync.", false);
            }


            return pluginresponse;

        }
        catch (Exception ex)
        {
            _stateService.UpdateStatus($"Error syncing data: {ex.Message}", false);
            Console.Error.WriteLine($"Error during DataSync: {ex.Message}");
            pluginresponse.success = false;
            return pluginresponse;
        }
    }

    private async Task<string> GetAccessTokenAsync(string clientId, string clientSecret, string baseUri)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, $"{baseUri}/v1/auth/connect/token");
        var credentials = $"{clientId}:{clientSecret}";
        var encodedCredentials = Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes(credentials));

        request.Headers.Authorization = new AuthenticationHeaderValue("Basic", encodedCredentials);
        request.Content = new FormUrlEncodedContent(new Dictionary<string, string>
            {
                { "grant_type", "client_credentials" }
            });

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();

        var responseContent = await response.Content.ReadAsStringAsync();
        var tokenResponse = JsonConvert.DeserializeObject<Dictionary<string, string>>(responseContent);

        return tokenResponse["access_token"];
    }

    private async Task<(string ClientId, JObject Details)> GetClientIdAsync(string lookupCode, string accessToken, string baseUri)
    {
        string encodedLookupCode = Uri.EscapeDataString(lookupCode);
        var request = new HttpRequestMessage(HttpMethod.Get, $"{baseUri}/crm/v1/clients?lookupCode={encodedLookupCode}");
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();

        var jsonResponse = await response.Content.ReadAsStringAsync();
        var clientData = JsonConvert.DeserializeObject<JObject>(jsonResponse);

        var embeddedData = clientData?["_embedded"] as JObject;
        var clientsArray = embeddedData?["clients"] as JArray;

        if (clientsArray != null && clientsArray.Count > 0)
        {
            var client = clientsArray.First;
            return (client["id"]?.ToString(), client as JObject);
        }

        return (null, null);
    }

    private async Task<List<Policy>> GetPoliciesAsync(string clientId, string accessToken, string baseUri)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, $"{baseUri}/policy/v1/clients/{clientId}/policies");
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();

        var jsonResponse = await response.Content.ReadAsStringAsync();
        var policyData = JsonConvert.DeserializeObject<JObject>(jsonResponse);

        var embeddedData = policyData?["_embedded"] as JObject;
        var policiesArray = embeddedData?["policies"] as JArray;

        var policies = policiesArray?.Select(policy => new Policy
        {
            PolicyNumber = policy["policyNumber"]?.ToString(),
            ePolicyId = policy["id"]?.ToString(),
            eType = policy["policyType"]?["description"]?.ToString(),
            eTypeCode = policy["policyType"]?["code"]?.ToString(),
            Notes = policy["description"]?.ToString(),
            EffectiveDate = policy["effectiveOn"] != null ? DateTime.Parse(policy["effectiveOn"].ToString()) : DateTime.MinValue,
            ExpirationDate = policy["expirationOn"] != null ? DateTime.Parse(policy["expirationOn"].ToString()) : DateTime.MinValue,
            Premium = policy["estimatedPremium"] != null
                ? decimal.Parse($"{policy["estimatedPremium"]["units"]}.{policy["estimatedPremium"]["partialUnits"]}")
                : 0m
        }).ToList();

        return policies ?? new List<Policy>();
    }

    private async Task<int> UpdatePoliciesWithLinesDataAsync(ApplicationDbContext context, List<Policy> policies, Client client, string accessToken, string baseUri, CancellationToken cancellationToken)
    {
        var allCarriers = await _stateService.AllCarriers;
        var allWholesalers = await _stateService.AllWholesalers;
        var existingPolicyIds = await context.Policies.Where(p => p.ClientId == client.ClientId).Select(p => p.ePolicyId).ToListAsync(cancellationToken);
        int newpolicies = 0;
        int loopnum = 1;
        Console.WriteLine("SYNC: Getting policy lines data...");

        foreach (var policy in policies)
        {
            cancellationToken.ThrowIfCancellationRequested();
            if (!existingPolicyIds.Contains(policy.ePolicyId))
            {
                Console.WriteLine($"SYNC: New policy found: {policy.ePolicyId}");

                // Policy doesn't exist in client's policy list
                string ePolicyId = policy.ePolicyId;
                var request = new HttpRequestMessage(HttpMethod.Get, $"{baseUri}/policy/v1/policies/{ePolicyId}/lines");
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var jsonResponse = await response.Content.ReadAsStringAsync();
                var policyLinesData = JObject.Parse(jsonResponse);

                if (policyLinesData["_embedded"]?["lines"] is JArray linesList && linesList.Count > 0)
                {
                    var firstLine = linesList[0] as JObject;
                    policy.ePolicyLineId = firstLine["id"].ToString();
                    
                    // Add status from the line data
                    policy.Status = firstLine["status"]?["code"]?.ToString();
                    
                    // Extract and set BillType from billMode
                    var billMode = firstLine["billMode"]?.ToString();
                    policy.BillType = billMode switch
                    {
                        "DIRECT" => Surefire.Domain.Accounting.Models.BillType.Direct,
                        "AGENCY" => Surefire.Domain.Accounting.Models.BillType.Agency,
                        _ => null
                    };
                    
                    Console.WriteLine($"SYNC: ({loopnum} of {policies.Count}) Attaching line {policy.ePolicyLineId} ({linesList.Count} total lines) to policy {policy.ePolicyId}");

                    var issuingCompanyCode = firstLine["issuingCompany"]?["lookupCode"]?.ToString();
                    var premiumPayableCode = firstLine["premiumPayable"]?["lookupCode"]?.ToString();

                    if (!string.IsNullOrEmpty(issuingCompanyCode))
                    {
                        var carrierFromList = allCarriers.FirstOrDefault(c => c.LookupCode == issuingCompanyCode);
                        if (carrierFromList != null)
                        {
                            var trackedCarrier = await context.Carriers.FindAsync(carrierFromList.CarrierId);
                            policy.Carrier = trackedCarrier;
                        }
                    }

                    if (!string.IsNullOrEmpty(premiumPayableCode))
                    {
                        var wholesalerFromList = allWholesalers.FirstOrDefault(c => c.LookupCode == premiumPayableCode);
                        if (wholesalerFromList != null)
                        {
                            var trackedWholesaler = await context.Carriers.FindAsync(wholesalerFromList.CarrierId);
                            policy.Wholesaler = trackedWholesaler;
                        }
                    }

                    // Store additional lines in the notes field
                    if (linesList.Count > 1)
                    {
                        policy.Notes = string.Join("; ", linesList.Skip(1).Select(line => line.ToString()));
                    }
                }

                policy.ProductId = GetProductIdFromTypeCode(policy.eTypeCode);
                Console.WriteLine($"Returned: {policy.ProductId}");
                client.Policies.Add(policy);
                newpolicies++;

            }
            else if (policy.EffectiveDate <= DateTime.Now && policy.ExpirationDate >= DateTime.Now)
            {
                // Update current policy details
                var existingPolicy = await context.Policies
                    .Include(p => p.Carrier)
                    .Include(p => p.Wholesaler)
                    .FirstOrDefaultAsync(p => p.ePolicyId == policy.ePolicyId, cancellationToken);

                if (existingPolicy != null)
                {
                    // Update basic policy details
                    existingPolicy.PolicyNumber = policy.PolicyNumber;
                    existingPolicy.eType = policy.eType;
                    existingPolicy.eTypeCode = policy.eTypeCode;
                    existingPolicy.Notes = policy.Notes;
                    existingPolicy.EffectiveDate = policy.EffectiveDate;
                    existingPolicy.ExpirationDate = policy.ExpirationDate;
                    existingPolicy.Premium = policy.Premium;

                    // Get and update line data
                    var request = new HttpRequestMessage(HttpMethod.Get, $"{baseUri}/policy/v1/policies/{policy.ePolicyId}/lines");
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                    var response = await _httpClient.SendAsync(request);
                    response.EnsureSuccessStatusCode();

                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var policyLinesData = JObject.Parse(jsonResponse);

                    if (policyLinesData["_embedded"]?["lines"] is JArray linesList && linesList.Count > 0)
                    {
                        var firstLine = linesList[0] as JObject;
                        existingPolicy.ePolicyLineId = firstLine["id"].ToString();
                        
                        // Add status update for existing policies
                        existingPolicy.Status = firstLine["status"]?["code"]?.ToString();
                        
                        // Extract and set BillType from billMode
                        var billMode = firstLine["billMode"]?.ToString();
                        existingPolicy.BillType = billMode switch
                        {
                            "DIRECT" => Surefire.Domain.Accounting.Models.BillType.Direct,
                            "AGENCY" => Surefire.Domain.Accounting.Models.BillType.Agency,
                            _ => null
                        };

                        var issuingCompanyCode = firstLine["issuingCompany"]?["lookupCode"]?.ToString();
                        var premiumPayableCode = firstLine["premiumPayable"]?["lookupCode"]?.ToString();

                        if (!string.IsNullOrEmpty(issuingCompanyCode))
                        {
                            var carrierFromList = allCarriers.FirstOrDefault(c => c.LookupCode == issuingCompanyCode);
                            if (carrierFromList != null)
                            {
                                var trackedCarrier = await context.Carriers.FindAsync(carrierFromList.CarrierId);
                                existingPolicy.Carrier = trackedCarrier;
                            }
                        }

                        if (!string.IsNullOrEmpty(premiumPayableCode))
                        {
                            var wholesalerFromList = allWholesalers.FirstOrDefault(c => c.LookupCode == premiumPayableCode);
                            if (wholesalerFromList != null)
                            {
                                var trackedWholesaler = await context.Carriers.FindAsync(wholesalerFromList.CarrierId);
                                existingPolicy.Wholesaler = trackedWholesaler;
                            }
                        }

                        // Store additional lines in the notes field
                        if (linesList.Count > 1)
                        {
                            existingPolicy.Notes = string.Join("; ", linesList.Skip(1).Select(line => line.ToString()));
                        }
                    }

                    existingPolicy.ProductId = GetProductIdFromTypeCode(policy.eTypeCode);
                    Console.WriteLine($"ReturnedUpdate: {existingPolicy.ProductId} and What: {policy.Product?.ProductId} WHAT2: {policy.ProductId}");
                    Console.WriteLine($"SYNC: Updated current policy: {policy.ePolicyId}");
                }
            }
            loopnum++;

        }

        // Save changes
        await context.SaveChangesAsync(cancellationToken);
        
        // Instead of calling RefreshClient, we'll update the state directly
        if (_stateService.ClientId == client.ClientId)
        {
            // Reload the client data
            await _stateService.LoadClient(client.ClientId);
        }
        
        return newpolicies;
    }
    private int GetProductIdFromTypeCode(string typeCode)
    {
        Console.WriteLine($"DEBUG: Received typeCode: '{typeCode}' (Length: {typeCode?.Length ?? 0})");
        // Trim any whitespace that might be present
        typeCode = typeCode?.Trim();
        
        var result = typeCode switch
        {
            "EQFL" => 17,
            "EPLX" => 8,
            "EART" => 18,
            "ACCD" => 11,
            "GDEN" or "DENT" => 15,
            "CUMB" => 7,
            "INLM" => 16,
            "EPLI" => 8,
            "BOP" => 6,
            "GLIA" => 3,
            "WCOM" => 2,
            "PROP" => 14,
            "BAUT" => 4,
            "PROF" => 5,
            "GHEA" => 9,
            "BOND" => 12,
            "CPKG" => 13,
            "DOLI" => 19,
            _ => 10,
        };
        Console.WriteLine($"DEBUG: Mapped to product ID: {result}");
        return result;
    }

    public async Task<PluginMethodResponse> GetContacts(string crmClientId)
    {
        Console.WriteLine("Getting contacts from API via Plugin...");
        crmClientId = crmClientId;
        int contactCount = 0;
        string message = "Reaching out to API...";
        List<Contact> loadedContacts = new List<Contact>();

        using var context = _dbContextFactory.CreateDbContext();
        PluginMethodResponse pluginresponse = new PluginMethodResponse();

        // Fetch plugin-specific API configuration from the database
        var pluginConfig = await context.Plugins
            .Where(p => p.Name == Name)
            .Select(p => new
            {
                p.ClientId,
                p.ClientSecret,
                p.BaseUri
            })
            .FirstOrDefaultAsync();

        if (pluginConfig == null || string.IsNullOrEmpty(pluginConfig.ClientId) || string.IsNullOrEmpty(pluginConfig.ClientSecret) || string.IsNullOrEmpty(pluginConfig.BaseUri))
        {
            Console.WriteLine("Plugin configuration is missing or incomplete.");
            pluginresponse.success = false;

            return pluginresponse;
        }

        try
        {
            if (!string.IsNullOrEmpty(crmClientId))
            {
                // Retrieve access token
                var accessToken = await GetAccessTokenAsync(pluginConfig.ClientId, pluginConfig.ClientSecret, pluginConfig.BaseUri);
                Console.WriteLine($"Got token: {accessToken}");

                var request = new HttpRequestMessage(HttpMethod.Get, $"{pluginConfig.BaseUri}/crm/v1/clients/{crmClientId}/contacts");
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var contactsData = JsonConvert.DeserializeObject<JObject>(jsonResponse);

                if (contactsData != null && contactsData.ContainsKey("_embedded"))
                {
                    var embeddedData = JsonConvert.DeserializeObject<Dictionary<string, object>>(contactsData["_embedded"].ToString());
                    if (embeddedData != null && embeddedData.ContainsKey("contacts"))
                    {
                        var contactsList = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(embeddedData["contacts"].ToString());
                        if (contactsList != null && contactsList.Count > 0)
                        {
                            foreach (var contactItem in contactsList)
                            {
                                // Retrieve first and last names
                                var firstName = contactItem.ContainsKey("firstName") ? contactItem["firstName"].ToString() : string.Empty;
                                var lastName = contactItem.ContainsKey("lastName") ? contactItem["lastName"].ToString() : string.Empty;

                                // Handling phone numbers
                                var phoneNumbers = contactItem.ContainsKey("phoneNumbers") ? contactItem["phoneNumbers"] as JArray : null;
                                string phone = string.Empty;
                                string mobile = string.Empty;
                                string additionalNotes = string.Empty;

                                if (phoneNumbers != null)
                                {
                                    foreach (var phoneNumber in phoneNumbers)
                                    {
                                        var type = phoneNumber["type"]?.ToString();
                                        var number = phoneNumber["number"]?.ToString();

                                        if (type == "BUSINESS" && string.IsNullOrEmpty(phone))
                                        {
                                            phone = number;
                                        }
                                        else if (type == "MOBILE" && string.IsNullOrEmpty(mobile))
                                        {
                                            mobile = number;
                                        }
                                        else
                                        {
                                            additionalNotes += $"Additional Number ({type}): {number}\n";
                                        }
                                    }
                                }

                                var address = contactItem.ContainsKey("address") ? contactItem["address"] as JObject : null;
                                var streets = address != null && address.ContainsKey("streets") ? address["streets"] as JArray : null;
                                var street = streets != null && streets.Count > 0 ? streets.First?.ToString() : string.Empty;
                                var city = address != null ? address["city"]?.ToString() : string.Empty;
                                var state = address != null ? address["stateOrProvince"]?.ToString() : string.Empty;
                                var zipCode = address != null ? address["zipOrPostalCode"]?.ToString() : string.Empty;

                                var contact = new Contact
                                {
                                    FirstName = firstName,
                                    LastName = lastName,
                                    // EmailAddresses
                                    EmailAddresses = (contactItem.ContainsKey("emails") && contactItem["emails"] is JArray emailsArray && emailsArray.Count > 0)
                                        ? emailsArray.Select((emailObj, idx) => new EmailAddress {
                                            Email = emailObj.Value<string>("emailAddress"),
                                            Label = emailObj.Value<string>("label"),
                                            IsPrimary = idx == 0 // First email is primary
                                        }).ToList()
                                        : new List<EmailAddress>(),
                                    // PhoneNumbers
                                    PhoneNumbers = new List<PhoneNumber>()
                                    {
                                        // Office/Main Phone
                                        !string.IsNullOrWhiteSpace(phone) ? new PhoneNumber {
                                            Number = phone,
                                            Type = PhoneType.Office,
                                            IsPrimary = true
                                        } : null,
                                        // Mobile Phone
                                        !string.IsNullOrWhiteSpace(mobile) ? new PhoneNumber {
                                            Number = mobile,
                                            Type = PhoneType.Mobile,
                                            IsPrimary = false
                                        } : null
                                    }.Where(p => p != null).ToList(),
                                    Notes = additionalNotes,
                                    Address = new Address
                                    {
                                        AddressLine1 = street,
                                        City = city,
                                        State = state,
                                        PostalCode = zipCode
                                    }
                                    // Add more fields as needed
                                };

                                loadedContacts.Add(contact);
                                contactCount++;
                            }
                        }
                        else
                        {
                            Console.WriteLine("No contacts found in the API response.");
                            message = "No contacts found.";
                        }
                    }
                    else
                    {
                        Console.WriteLine("Invalid API response format for contacts.");
                        message = "Invalid API response.";
                    }
                }
                else
                {
                    Console.WriteLine("Invalid API response format.");
                    message = "Invalid API response.";
                }
            }
            message = $"Finished. Found {contactCount} contacts. Remove the ones you don't need and click import.";

        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Error importing contacts: {ex.Message}");
            message = "Failed to import contacts.";
        }
        pluginresponse.success = true;
        pluginresponse.message = message;
        pluginresponse.contacts = loadedContacts;
        return pluginresponse;
    }
}
