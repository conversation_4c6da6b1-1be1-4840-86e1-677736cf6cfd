﻿using Surefire.Data;
using Surefire.Domain.Plugins;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;

namespace Surefire.Plugins.AppliedEpic;

public class PluginStartup
{
    public static void RegisterPlugin(IServiceCollection services, IConfiguration configuration)
    {
        Console.WriteLine("Registering AppliedEpic Plugin...");

        // Configure plugin-specific options
        services.Configure<AppliedEpicOptions>(options =>
        {
            options.ClientId = configuration["AppliedEpic:ClientId"];
            options.ClientSecret = configuration["AppliedEpic:ClientSecret"];
            options.BaseUri = configuration["AppliedEpic:BaseUri"];
        });

        // Add HttpClient for the plugin
        services.AddHttpClient<AppliedEpicDataSync>("AppliedEpicHttpClient", (sp, client) =>
        {
            var options = sp.GetRequiredService<IOptions<AppliedEpicOptions>>().Value;
            client.BaseAddress = new Uri(options.BaseUri);
        });
    }

    public static async Task InitializePlugin(IServiceProvider serviceProvider)
    {
        Console.WriteLine("AppliedEpicAPI Startup***************************************");
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        // Check if plugin exists in the Plugin table
        var pluginExists = await context.Plugins.AnyAsync(p => p.Name == "Applied Epic Cloud API");

        if (!pluginExists)
        {
            // Add new plugin entry with default values
            context.Plugins.Add(new Plugin
            {
                Name = "Applied Epic Cloud API",
                ShortDescription = "Applied Epic data sync integration for CRM API",
                Type = "DataSync",
                PluginWebsite = "https://yourdeveloperlink.com",
                DeveloperName = "FlashVenom.Surefire",
                HashId = "e687dad6bc28f91acf05d574dd48a199eff4e1cb4166c6269317e737abcd76b3"
            });

            await context.SaveChangesAsync();
        }
    }
}
