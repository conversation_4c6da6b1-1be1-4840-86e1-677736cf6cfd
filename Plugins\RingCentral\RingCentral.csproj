﻿<Project Sdk="Microsoft.NET.Sdk">
	<ItemGroup>
	  <PackageReference Include="RingCentral.Net" Version="6.3.1" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\..\Surefire\Surefire.csproj" />
	</ItemGroup>
	
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<CopyLocalLockFileAssemblies>false</CopyLocalLockFileAssemblies>
		<OutputPath>..\..\Surefire\Plugins\RingCentral</OutputPath>
		<OutDir>$(OutputPath)</OutDir>
	</PropertyGroup>

	<Target Name="ClearPluginAssemblies" AfterTargets="Build">
		<Message Importance="high" Text="Cleaning up RingCentral assemblies after build..." />
		<!-- Delete the unwanted files -->
		<Delete Files="$(OutDir)\Surefire.exe" />
		<Delete Files="$(OutDir)\Surefire.dll" />
		<Delete Files="$(OutDir)\Surefire.pdb" />
		<Delete Files="$(OutDir)\Surefire.deps.json" />
		<Delete Files="$(OutDir)\Surefire.staticwebassets.runtime.json" />
		<Delete Files="$(OutDir)\Surefire.staticwebassets.endpoints.json" />
		<Delete Files="$(OutDir)\Surefire.runtimeconfig.json" />
		<Delete Files="$(OutDir)\appsettings.json" />
		<Delete Files="$(OutDir)\appsettings.Development.json" />
	</Target>

	<Target Name="PostBuild" AfterTargets="PostBuildEvent">
		<!-- Remove all "Plugins" folders in subdirectories -->
		<Exec Command="for /d %%d in (&quot;$(SolutionDir)Surefire\bin\Plugins\*&quot;) do if exist %%d\Plugins rmdir /s /q &quot;%%d\Plugins&quot;" />
	</Target>
	<!--<PropertyGroup>
		<PluginOutputPath>$(SolutionDir)Surefire\Plugins\$(MSBuildProjectName)</PluginOutputPath>
	</PropertyGroup>-->
	<!--<OutputPath>..\..\Surefire\bin\Plugins\RingCentral</OutputPath>-->
</Project>
