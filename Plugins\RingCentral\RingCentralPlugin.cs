﻿using Surefire.Data;
using Surefire.Domain.Plugins;
using Surefire.Domain.Shared.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using RingCentral;

namespace Surefire.Plugins.RingCentral
{
    public class RingCentralApi : ICallLogPlugin
    {
        private readonly RingCentralOptions _options;
        private readonly RestClient _restClient;

        public string Name => "RingCentral";
        public bool IsActive { get; set; } = true;

        public RingCentralApi(IDbContextFactory<ApplicationDbContext> dbContextFactory, RingCentralOptions options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));

            // Validate required configuration
            if (string.IsNullOrEmpty(_options.ApiUrl))
                throw new ArgumentNullException(nameof(_options.ApiUrl), "ApiUrl cannot be null.");
            if (string.IsNullOrEmpty(_options.ClientId))
                throw new ArgumentNullException(nameof(_options.ClientId), "ClientId cannot be null.");
            if (string.IsNullOrEmpty(_options.ClientSecret))
                throw new ArgumentNullException(nameof(_options.ClientSecret), "ClientSecret cannot be null.");

            _restClient = new RestClient(_options.ClientId, _options.ClientSecret, _options.ApiUrl);
        }

        public async Task<PluginMethodResponse> ExecuteAsync(string methodName, object[] parameters, CancellationToken cancellationToken)
        {
            return null;
        }

        public async Task<List<CallLogRecord>> GetCallLogsAsync(DateTime dateFrom, DateTime dateTo, CancellationToken cancellationToken)
        {
            await AuthorizeWithJWT(_options.Jwt);

            var queryParams = new ReadUserCallLogParameters
            {
                dateFrom = dateFrom.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                dateTo = dateTo.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                perPage = 1000,
                view = "Simple"
            };

            var response = await _restClient.Restapi().Account().Extension().CallLog().List(queryParams);
            return response.records?.ToList() ?? new List<CallLogRecord>();
        }

        private async Task AuthorizeWithJWT(string jwtToken)
        {
            try
            {
                await _restClient.Authorize(jwtToken);
            }
            catch (Exception ex)
            {
                throw new ApplicationException("Failed to authorize with RingCentral.", ex);
            }
        }
    }
}
