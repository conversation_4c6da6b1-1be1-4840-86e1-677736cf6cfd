﻿using Surefire.Data;
using Surefire.Domain.Plugins;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Surefire.Plugins.RingCentral
{
    public static class PluginStartup
    {
        public static void RegisterPlugin(IServiceCollection services, IConfiguration configuration)
        {
            var ringCentralOptions = new RingCentralOptions
            {
                ClientId = "dSLSIsjJRpGKnhdC-9Yeeg",
                ClientSecret = "dbMDPX4kQ5mbzJAGplT52gJ9HzOeeiR8S8zS0OkKLzAA",
                Jwt = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
                ApiUrl = "https://platform.ringcentral.com"
            };

            // Register the options manually
            services.AddSingleton(ringCentralOptions);

            // Register the plugin
            services.AddScoped<ICallLogPlugin, RingCentralApi>();
        }

        //public static async Task InitializePlugin(IServiceProvider serviceProvider)
        //{
        //    Console.WriteLine("RingCentral Startup***************************************");
        //    using var scope = serviceProvider.CreateScope();
        //    var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        //    // Check if plugin exists in the database
        //    var pluginExists = await context.Plugins.AnyAsync(p => p.Name == "RingCentral");

        //    if (!pluginExists)
        //    {
        //        context.Plugins.Add(new Plugin
        //        {
        //            Name = "RingCentral",
        //            ShortDescription = "Call logging and texting using RingCentral SDK",
        //            Type = "CallLog",
        //            PluginWebsite = "https://flashvenom.com",
        //            DeveloperName = "FlashVenom.Surefire",
        //            HashId = "d782100596ed4770a863e9a0056fb6e7"
        //        });

        //        await context.SaveChangesAsync();
        //    }
        //}
    }
}
