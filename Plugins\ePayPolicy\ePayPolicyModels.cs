﻿using Surefire.Domain.Shared.Models;
using Newtonsoft.Json;

namespace Surefire.Plugins.ePayPolicy;

public class PaymentApiOptions
{
    public string ClientKey { get; set; }
    public string ClientSecret { get; set; }
    public string BaseAddress { get; set; }
}

public class TransactionsResponse
{
    [JsonProperty("transactions")]
    public List<RecentTransactions> Transactions { get; set; }
}
