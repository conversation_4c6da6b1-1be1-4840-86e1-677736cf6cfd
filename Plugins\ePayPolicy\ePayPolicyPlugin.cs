﻿using Surefire.Domain.Plugins;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using System.Text;

namespace Surefire.Plugins.ePayPolicy;

public class ePayPolicyPlugin : IPayLogPlugin
{
    private readonly HttpClient _httpClient;
    private readonly PaymentApiOptions _options;
    private string? _cachedResponse;
    private DateTime _lastFetchTime;

    public string Name => "ePayPolicy";
    public bool IsActive { get; set; } = true;

    public ePayPolicyPlugin(HttpClient httpClient, IOptions<PaymentApiOptions> options)
    {
        _httpClient = httpClient;
        _options = options.Value;
    }

    public async Task<PluginMethodResponse> ExecuteAsync(string methodName, object[] parameters, CancellationToken cancellationToken)
    {
        return methodName switch
        {
            "GetRecentPayments" => await GetRecentPayments(cancellationToken),
            _ => new PluginMethodResponse
            {
                success = false,
                message = $"Method {methodName} is not implemented in {Name} plugin."
            }
        };
    }

    public async Task<PluginMethodResponse> GetRecentPayments(CancellationToken cancellationToken)
    {
        if (!IsActive)
        {
            Console.WriteLine("PAY: Plugin is inactive.");
            return new PluginMethodResponse
            {
                success = false,
                message = "ePayPolicy plugin is inactive."
            };
        }

        var pluginResponse = new PluginMethodResponse();

        // Check if the cached response is still valid (within 30 minutes)
        if (_cachedResponse != null && (DateTime.UtcNow - _lastFetchTime).TotalMinutes < 30)
        {
            pluginResponse.success = true;
            pluginResponse.message = "Payment logs are still current.";
            return pluginResponse;
        }

        string transactionsUrl = _options.BaseAddress + "/api/v1/transactions";
        var request = new HttpRequestMessage(HttpMethod.Get, transactionsUrl);

        // Set up Basic Authentication header
        var credentials = $"{_options.ClientKey}:{_options.ClientSecret}";
        var encodedCredentials = Convert.ToBase64String(Encoding.ASCII.GetBytes(credentials));
        request.Headers.Authorization = new AuthenticationHeaderValue("Basic", encodedCredentials);
        request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

        var response = await _httpClient.SendAsync(request, cancellationToken);
        response.EnsureSuccessStatusCode();

        if (response != null)
        {
            _cachedResponse = await response.Content.ReadAsStringAsync();
            _lastFetchTime = DateTime.UtcNow;
            pluginResponse.jsonresponse = _cachedResponse;
            pluginResponse.success = true;
            pluginResponse.message = "Payment logs updated.";
        }
        else
        {
            pluginResponse.success = false;
            pluginResponse.message = "Payment logs null or invalid!";
            Console.WriteLine("PAY: Logs null or invalid.");
        }

        return pluginResponse;
    }
}