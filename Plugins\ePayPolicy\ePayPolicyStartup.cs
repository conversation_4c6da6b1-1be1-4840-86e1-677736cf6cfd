﻿using Surefire.Data;
using Surefire.Domain.Plugins;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace Surefire.Plugins.ePayPolicy
{
    public static class PluginStartup
    {
        public static void RegisterPlugin(IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<PaymentApiOptions>(options =>
            {
                options.ClientKey = "dc9248045d1245b";
                options.ClientSecret = "43a03eb765c9434";
                options.BaseAddress = "https://api.epaypolicy.com:443";
            });

            services.AddHttpClient("ePayPolicyHttpClient", (sp, client) =>
            {
                var options = sp.GetRequiredService<IOptions<PaymentApiOptions>>().Value;
                client.BaseAddress = new Uri(options.BaseAddress);
                client.DefaultRequestHeaders.Add("ClientId", options.ClientKey);
                client.DefaultRequestHeaders.Add("ClientSecret", options.ClientSecret);
            });

            services.AddScoped<IPayLogPlugin, ePayPolicyPlugin>();
        }

        //public static async Task InitializePlugin(IServiceProvider serviceProvider)
        //{
        //    Console.WriteLine("ePayPolicy Startup***************************************");
        //    using var scope = serviceProvider.CreateScope();
        //    var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        //    // Check if plugin exists in the database
        //    var pluginExists = await context.Plugins.AnyAsync(p => p.Name == "ePayPolicy");

        //    if (!pluginExists)
        //    {
        //        context.Plugins.Add(new Plugin
        //        {
        //            Name = "ePayPolicy",
        //            ShortDescription = "ePayPolicy Transactions API",
        //            Type = "PayLog",
        //            PluginWebsite = "https://flashvenom.com",
        //            DeveloperName = "FlashVenom.Surefire",
        //            HashId = "5509413df092ca2beb9471bf5e94c132",
        //            IsActive = true // Default to active
        //        });

        //        await context.SaveChangesAsync();
        //    }
        //}
    }
}
