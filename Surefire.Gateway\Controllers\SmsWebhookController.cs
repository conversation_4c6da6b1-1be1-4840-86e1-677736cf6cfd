using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using System.Security.Cryptography;
using System.Text;
using System.Net.Http.Json;
using Surefire.Gateway.Models;
using Surefire.Gateway.Helpers;

namespace Surefire.Gateway.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SmsWebhookController : ControllerBase
    {
        private readonly ILogger<SmsWebhookController> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly bool _enableDebugLogging;

        public SmsWebhookController(
            ILogger<SmsWebhookController> logger,
            IConfiguration configuration,
            IHttpClientFactory httpClientFactory)
        {
            _logger = logger;
            _configuration = configuration;
            _httpClient = httpClientFactory.CreateClient("SurefireApi");
            _enableDebugLogging = _configuration.GetValue<bool>("EnableDebugLogging", false);
        }

        [HttpPost]
        public async Task<IActionResult> ReceiveSmsWebhook([FromBody] JsonElement? payload = null)
        {
            string requestId = HttpContext.Items["RequestId"]?.ToString() ?? Guid.NewGuid().ToString("N").Substring(0, 8);
            
            try
            {
                // Log to both logger and file for comprehensive debugging
                _logger.LogInformation("=== SMS WEBHOOK RECEIVED [RequestId: {RequestId}] ===", requestId);
                
                // Check for validation token first - this is critical for webhook registration
                if (Request.Headers.TryGetValue("Validation-Token", out var validationToken))
                {
                    _logger.LogInformation(
                        "RingCentral validation request detected [RequestId: {RequestId}] - Token: {Token}",
                        requestId, validationToken);
                    
                    // Log the full request for debugging
                    if (_enableDebugLogging)
                    {
                        await DebugHelper.LogHttpRequestToFile(Request, $"validation_request_{requestId}");
                    }
                    
                    // Echo back the validation token as required by RingCentral
                    Response.Headers.Add("Validation-Token", validationToken.ToString());
                    return Ok();
                }
                
                // If no payload, return bad request
                if (payload == null || payload.Value.ValueKind == JsonValueKind.Undefined)
                {
                    _logger.LogWarning("Webhook received with no payload [RequestId: {RequestId}]", requestId);
                    return BadRequest(new { error = "No payload provided" });
                }
                
                // Log raw payload for debugging
                var rawPayload = payload.Value.GetRawText();
                _logger.LogInformation("Raw payload [RequestId: {RequestId}]: {Payload}", requestId, rawPayload);
                
                // Deserialize the payload to our model
                RingCentralWebhookPayload? webhookPayload = null;
                try
                {
                    webhookPayload = JsonSerializer.Deserialize<RingCentralWebhookPayload>(
                        rawPayload, 
                        new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to deserialize webhook payload [RequestId: {RequestId}]", requestId);
                    if (_enableDebugLogging)
                    {
                        await DebugHelper.LogHttpRequestToFile(Request, $"deserialization_error_{requestId}");
                    }
                    return BadRequest(new { error = "Invalid payload format" });
                }
                
                // Log the complete request to file if debug logging is enabled
                if (_enableDebugLogging)
                {
                    await DebugHelper.LogHttpRequestToFile(Request, $"sms_webhook_{requestId}");
                    DebugHelper.LogObjectToFile(webhookPayload, $"sms_payload_{requestId}");
                }
                
                _logger.LogInformation(
                    "SMS Webhook received [RequestId: {RequestId}] - Event: {Event}, UUID: {UUID}, SubscriptionId: {SubscriptionId}",
                    requestId, webhookPayload?.Event, webhookPayload?.Uuid, webhookPayload?.SubscriptionId);
                
                // Log detailed payload information for debugging
                _logger.LogInformation(
                    "Webhook payload details [RequestId: {RequestId}]: OwnerId: {OwnerId}, Timestamp: {Timestamp}, " +
                    "Body.ExtensionId: {ExtensionId}, Body.LastUpdated: {LastUpdated}, Body.Changes: {Changes}",
                    requestId, webhookPayload?.OwnerId, webhookPayload?.Timestamp, 
                    webhookPayload?.Body?.ExtensionId, webhookPayload?.Body?.LastUpdated,
                    webhookPayload?.Body?.Changes != null ? $"Array with {webhookPayload.Body.Changes.Length} items" : "null");
                
                // Validate webhook signature
                if (!IsValidWebhook(Request))
                {
                    _logger.LogWarning(
                        "Invalid webhook signature [RequestId: {RequestId}] - Headers: {@Headers}",
                        requestId, Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()));
                    return Unauthorized();
                }

                // Check if this is a direct SMS message (RingCentral sends SMS data directly in body)
                bool hasDirectSmsMessage = webhookPayload?.Body?.Type == "SMS" && 
                                         webhookPayload?.Body?.Id.HasValue == true;

                // Check if this has changes array with SMS data
                bool hasChangesWithSms = webhookPayload?.Body?.Changes != null && 
                                       webhookPayload.Body.Changes.Any(c => c.Type == "SMS");

                // Validate that this is an SMS message webhook
                // Accept if: 1) Event is the expected type, OR 2) Body contains SMS data
                bool isValidSmsWebhook = webhookPayload?.Event == "message.store.instant.message" ||
                                       hasDirectSmsMessage || hasChangesWithSms;

                if (!isValidSmsWebhook)
                {
                    _logger.LogInformation(
                        "Ignoring non-SMS webhook event [RequestId: {RequestId}] - Event: {Event}, HasDirectSms: {HasDirectSms}, HasChanges: {HasChanges}",
                        requestId, webhookPayload?.Event, hasDirectSmsMessage, hasChangesWithSms);
                    return Ok(new { success = true, message = "Non-SMS event ignored" });
                }

                if (hasDirectSmsMessage)
                {
                    _logger.LogInformation(
                        "Found direct SMS message in body [RequestId: {RequestId}] - ID: {MessageId}, Type: {Type}, Direction: {Direction}",
                        requestId, webhookPayload?.Body?.Id, webhookPayload?.Body?.Type, webhookPayload?.Body?.Direction);
                }
                else if (hasChangesWithSms)
                {
                    var smsChanges = webhookPayload.Body.Changes.Where(c => c.Type == "SMS").ToList();
                    _logger.LogInformation(
                        "Found {SmsChangeCount} SMS changes in changes array [RequestId: {RequestId}]",
                        smsChanges.Count, requestId);
                }
                else
                {
                    // Check if changes is null (common during webhook setup)
                    if (webhookPayload?.Body?.Changes == null && !hasDirectSmsMessage)
                    {
                        _logger.LogInformation(
                            "Webhook received with null changes and no direct SMS message (likely subscription setup) [RequestId: {RequestId}]",
                            requestId);
                        return Ok(new { success = true, message = "Webhook with null changes processed" });
                    }

                    _logger.LogInformation(
                        "No SMS data found in webhook [RequestId: {RequestId}] - HasDirectSms: {HasDirectSms}, HasChanges: {HasChanges}",
                        requestId, hasDirectSmsMessage, webhookPayload?.Body?.Changes != null);
                    return Ok(new { success = true, message = "No SMS data found" });
                }

                // Forward the webhook payload to the main Surefire application
                var mainAppUrl = _configuration["MainAppSettings:WebhookForwardUrl"];
                if (string.IsNullOrEmpty(mainAppUrl))
                {
                    _logger.LogError(
                        "MainAppSettings:WebhookForwardUrl configuration is missing [RequestId: {RequestId}]",
                        requestId);
                    return StatusCode(500, new { success = false, error = "Internal server configuration error" });
                }

                _logger.LogInformation(
                    "Forwarding webhook to main application [RequestId: {RequestId}] - URL: {Url}",
                    requestId, mainAppUrl);

                // Add security headers for internal communication
                var apiKey = _configuration["MainAppSettings:ApiKey"];
                if (!string.IsNullOrEmpty(apiKey))
                {
                    _httpClient.DefaultRequestHeaders.Remove("X-API-Key");
                    _httpClient.DefaultRequestHeaders.Add("X-API-Key", apiKey);
                    _logger.LogInformation("Added API key to request [RequestId: {RequestId}] - Key: {ApiKey}", requestId, apiKey);
                }
                else
                {
                    _logger.LogWarning("API key is not configured [RequestId: {RequestId}]", requestId);
                }

                // Add request ID to headers for tracing
                _httpClient.DefaultRequestHeaders.Remove("X-Request-ID");
                _httpClient.DefaultRequestHeaders.Add("X-Request-ID", requestId);

                // Log SMS message details if present
                LogSmsMessageDetails(webhookPayload, requestId);

                // Forward the payload to the main application
                var startTime = DateTime.UtcNow;
                
                _logger.LogInformation(
                    "Sending POST request to main app [RequestId: {RequestId}] - URL: {Url}, Payload size: {Size} bytes",
                    requestId, mainAppUrl, rawPayload.Length);
                
                var response = await _httpClient.PostAsJsonAsync(mainAppUrl, webhookPayload);
                var elapsedMs = (DateTime.UtcNow - startTime).TotalMilliseconds;
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError(
                        "Error forwarding webhook [RequestId: {RequestId}] - StatusCode: {StatusCode}, " +
                        "Response: {Response}, ElapsedMs: {ElapsedMs}ms",
                        requestId, response.StatusCode, errorContent, elapsedMs);
                    
                    // Log the error response to file for debugging
                    if (_enableDebugLogging)
                    {
                        DebugHelper.LogObjectToFile(new { 
                            StatusCode = response.StatusCode,
                            Response = errorContent,
                            ElapsedMs = elapsedMs,
                            RequestUrl = mainAppUrl,
                            RequestHeaders = _httpClient.DefaultRequestHeaders.ToDictionary(h => h.Key, h => string.Join(", ", h.Value))
                        }, $"error_response_{requestId}");
                    }
                    
                    return StatusCode(502, new { success = false, error = "Failed to process webhook" });
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogInformation(
                    "Successfully forwarded webhook [RequestId: {RequestId}] - StatusCode: {StatusCode}, " +
                    "Response: {Response}, ElapsedMs: {ElapsedMs}ms",
                    requestId, response.StatusCode, responseContent, elapsedMs);
                
                return Ok(new { success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex, 
                    "Error processing webhook [RequestId: {RequestId}] - Exception: {ExceptionType}, Message: {ExceptionMessage}",
                    requestId, ex.GetType().Name, ex.Message);
                
                // Log the exception details to file for debugging
                if (_enableDebugLogging)
                {
                    DebugHelper.LogObjectToFile(new { 
                        ExceptionType = ex.GetType().Name,
                        Message = ex.Message,
                        StackTrace = ex.StackTrace,
                        InnerException = ex.InnerException?.Message
                    }, $"exception_{requestId}");
                }
                
                return StatusCode(500, new { success = false, error = "Internal server error" });
            }
        }

        private void LogSmsMessageDetails(RingCentralWebhookPayload? payload, string requestId)
        {
            try
            {
                if (payload?.Body?.Changes == null)
                {
                    return;
                }

                foreach (var change in payload.Body.Changes)
                {
                    if (change.Type != "SMS")
                    {
                        continue;
                    }

                    _logger.LogDebug(
                        "SMS change detected [RequestId: {RequestId}] - NewCount: {NewCount}, UpdatedCount: {UpdatedCount}",
                        requestId, change.NewCount, change.UpdatedCount);

                    // Log new records
                    if (change.NewRecords != null)
                    {
                        foreach (var record in change.NewRecords)
                        {
                            LogMessageRecord(record, "New", requestId);
                        }
                    }

                    // Log updated records
                    if (change.UpdatedRecords != null)
                    {
                        foreach (var record in change.UpdatedRecords)
                        {
                            LogMessageRecord(record, "Updated", requestId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "Error logging SMS message details [RequestId: {RequestId}] - Exception: {ExceptionMessage}",
                    requestId, ex.Message);
            }
        }

        private void LogMessageRecord(MessageRecord record, string recordType, string requestId)
        {
            try
            {
                var direction = record.Direction ?? "Unknown";
                var fromNumber = record.From?.PhoneNumber ?? "Unknown";
                var toNumbers = record.To?.Select(t => t.PhoneNumber).ToArray() ?? new string[] { "Unknown" };
                
                _logger.LogInformation(
                    "{RecordType} SMS message [RequestId: {RequestId}] - ID: {MessageId}, Direction: {Direction}, " +
                    "From: {From}, To: {To}, CreationTime: {CreationTime}",
                    recordType, requestId, record.Id, direction, fromNumber, 
                    string.Join(", ", toNumbers), record.CreationTime);
                
                // Log message content if available
                if (record.Attachments != null)
                {
                    foreach (var attachment in record.Attachments)
                    {
                        if (attachment.Type == "Text")
                        {
                            // Truncate message content for logging
                            var content = attachment.Content ?? "";
                            var truncatedContent = content.Length > 100 
                                ? content.Substring(0, 97) + "..." 
                                : content;
                            
                            _logger.LogInformation(
                                "SMS content [RequestId: {RequestId}] - ID: {MessageId}, Content: \"{Content}\"",
                                requestId, record.Id, truncatedContent);
                            
                            // Log full message content to file if debug logging is enabled
                            if (_enableDebugLogging && content.Length > 100)
                            {
                                DebugHelper.LogObjectToFile(new { 
                                    MessageId = record.Id,
                                    Direction = direction,
                                    From = fromNumber,
                                    To = toNumbers,
                                    Content = content
                                }, $"sms_content_{requestId}_{record.Id}");
                            }
                        }
                        else
                        {
                            _logger.LogDebug(
                                "SMS attachment [RequestId: {RequestId}] - ID: {MessageId}, Type: {Type}, " +
                                "ContentType: {ContentType}, Size: {Size}, FileName: {FileName}",
                                requestId, record.Id, attachment.Type, attachment.ContentType, 
                                attachment.Size, attachment.FileName);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "Error logging message record [RequestId: {RequestId}] - Exception: {ExceptionMessage}",
                    requestId, ex.Message);
            }
        }

        private bool IsValidWebhook(HttpRequest request)
        {
            string requestId = HttpContext.Items["RequestId"]?.ToString() ?? Guid.NewGuid().ToString("N").Substring(0, 8);
            
            try
            {
                // Get the validation secret from configuration
                var validationSecret = _configuration["RingCentral:ValidationSecret"];
                if (string.IsNullOrEmpty(validationSecret))
                {
                    _logger.LogWarning(
                        "RingCentral validation secret not configured [RequestId: {RequestId}]",
                        requestId);
                    return false;
                }

                _logger.LogDebug(
                    "Validating webhook [RequestId: {RequestId}] - Headers: {@Headers}",
                    requestId, request.Headers.Keys);

                // Check for validation header (implement according to RingCentral's documentation)
                if (request.Headers.TryGetValue("Validation-Token", out var validationToken))
                {
                    _logger.LogInformation(
                        "Validation token request detected [RequestId: {RequestId}] - Token: {Token}",
                        requestId, validationToken);
                    
                    // For validation requests, RingCentral sends a validation token
                    // that needs to be echoed back
                    return true;
                }

                // For actual webhook calls, check signature if available
                if (request.Headers.TryGetValue("X-RingCentral-Signature", out var signature))
                {
                    _logger.LogDebug(
                        "Validating signature [RequestId: {RequestId}] - Signature: {Signature}",
                        requestId, signature);
                    
                    // Read the request body
                    request.Body.Position = 0;
                    using var reader = new StreamReader(request.Body, Encoding.UTF8, leaveOpen: true);
                    var body = reader.ReadToEndAsync().Result;
                    request.Body.Position = 0;

                    // Validate the signature (example implementation - adjust based on RingCentral docs)
                    using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(validationSecret));
                    var computedHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(body));
                    var computedSignature = Convert.ToBase64String(computedHash);

                    var isValid = signature.Equals(computedSignature);
                    
                    _logger.LogInformation(
                        "Signature validation result [RequestId: {RequestId}] - IsValid: {IsValid}, " +
                        "ComputedSignature: {ComputedSignature}",
                        requestId, isValid, computedSignature);
                    
                    // Log detailed validation info to file for debugging
                    if (_enableDebugLogging)
                    {
                        DebugHelper.LogWebhookValidation(request, validationSecret, computedSignature, isValid);
                    }
                    
                    return isValid;
                }

                // If we're in development mode, allow without validation
                if (_configuration.GetValue<bool>("DevelopmentMode"))
                {
                    _logger.LogWarning(
                        "Allowing webhook in development mode without validation [RequestId: {RequestId}]",
                        requestId);
                    return true;
                }

                _logger.LogWarning(
                    "No validation method found for webhook [RequestId: {RequestId}]",
                    requestId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error validating webhook [RequestId: {RequestId}] - Exception: {ExceptionType}, Message: {ExceptionMessage}",
                    requestId, ex.GetType().Name, ex.Message);
                return false;
            }
        }
    }
} 