using System.Text;
using System.Text.Json;

namespace Surefire.Gateway.Helpers
{
    public static class DebugHelper
    {
        private static readonly string LogDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            WriteIndented = true,
            MaxDepth = 10
        };

        /// <summary>
        /// Logs a complete object to a debug file with timestamp
        /// </summary>
        public static void LogObjectToFile(object obj, string prefix = "debug")
        {
            try
            {
                // Ensure directory exists
                if (!Directory.Exists(LogDirectory))
                {
                    Directory.CreateDirectory(LogDirectory);
                }

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var fileName = $"{prefix}_{timestamp}.json";
                var filePath = Path.Combine(LogDirectory, fileName);

                var json = JsonSerializer.Serialize(obj, JsonOptions);
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                // Don't throw from a debug helper
                Console.Error.WriteLine($"Error logging object to file: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs a complete HTTP request to a debug file
        /// </summary>
        public static async Task LogHttpRequestToFile(HttpRequest request, string prefix = "request")
        {
            try
            {
                // Ensure directory exists
                if (!Directory.Exists(LogDirectory))
                {
                    Directory.CreateDirectory(LogDirectory);
                }

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var fileName = $"{prefix}_{timestamp}.txt";
                var filePath = Path.Combine(LogDirectory, fileName);

                using var writer = new StreamWriter(filePath, false, Encoding.UTF8);

                // Write request details
                await writer.WriteLineAsync($"TIMESTAMP: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                await writer.WriteLineAsync($"METHOD: {request.Method}");
                await writer.WriteLineAsync($"PATH: {request.Path}");
                await writer.WriteLineAsync($"QUERY: {request.QueryString}");
                await writer.WriteLineAsync($"PROTOCOL: {request.Protocol}");
                await writer.WriteLineAsync($"SCHEME: {request.Scheme}");
                await writer.WriteLineAsync($"HOST: {request.Host}");
                await writer.WriteLineAsync($"CONTENT TYPE: {request.ContentType}");
                await writer.WriteLineAsync($"CONTENT LENGTH: {request.ContentLength}");

                // Write headers
                await writer.WriteLineAsync("\nHEADERS:");
                foreach (var header in request.Headers)
                {
                    await writer.WriteLineAsync($"{header.Key}: {header.Value}");
                }

                // Write cookies
                await writer.WriteLineAsync("\nCOOKIES:");
                foreach (var cookie in request.Cookies)
                {
                    await writer.WriteLineAsync($"{cookie.Key}: {cookie.Value}");
                }

                // Write body
                await writer.WriteLineAsync("\nBODY:");
                request.EnableBuffering();
                request.Body.Position = 0;
                using var reader = new StreamReader(request.Body, Encoding.UTF8, leaveOpen: true);
                var body = await reader.ReadToEndAsync();
                await writer.WriteLineAsync(body);
                request.Body.Position = 0;
            }
            catch (Exception ex)
            {
                // Don't throw from a debug helper
                Console.Error.WriteLine($"Error logging request to file: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs a webhook validation attempt with detailed information
        /// </summary>
        public static void LogWebhookValidation(HttpRequest request, string validationSecret, string computedSignature, bool isValid)
        {
            try
            {
                // Ensure directory exists
                if (!Directory.Exists(LogDirectory))
                {
                    Directory.CreateDirectory(LogDirectory);
                }

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var fileName = $"webhook_validation_{timestamp}.txt";
                var filePath = Path.Combine(LogDirectory, fileName);

                using var writer = new StreamWriter(filePath, false, Encoding.UTF8);

                // Write validation details
                writer.WriteLine($"TIMESTAMP: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                writer.WriteLine($"VALIDATION RESULT: {(isValid ? "VALID" : "INVALID")}");
                writer.WriteLine($"SECRET LENGTH: {validationSecret.Length}");
                writer.WriteLine($"COMPUTED SIGNATURE: {computedSignature}");

                // Write headers
                writer.WriteLine("\nHEADERS:");
                foreach (var header in request.Headers)
                {
                    writer.WriteLine($"{header.Key}: {header.Value}");
                }

                // If there's a signature header, write it separately for clarity
                if (request.Headers.TryGetValue("X-RingCentral-Signature", out var signature))
                {
                    writer.WriteLine($"\nSIGNATURE HEADER: {signature}");
                    writer.WriteLine($"SIGNATURE MATCHES: {signature.Equals(computedSignature)}");
                }
            }
            catch (Exception ex)
            {
                // Don't throw from a debug helper
                Console.Error.WriteLine($"Error logging webhook validation: {ex.Message}");
            }
        }
    }
} 