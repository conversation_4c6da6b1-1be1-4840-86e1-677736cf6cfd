using System.Text.Json.Serialization;

namespace Surefire.Gateway.Models
{
    public class RingCentralWebhookPayload
    {
        [JsonPropertyName("uuid")]
        public string? Uuid { get; set; }

        [JsonPropertyName("event")]
        public string? Event { get; set; }

        [JsonPropertyName("timestamp")]
        public string? Timestamp { get; set; }

        [JsonPropertyName("subscriptionId")]
        public string? SubscriptionId { get; set; }

        [JsonPropertyName("ownerId")]
        public string? OwnerId { get; set; }

        [JsonPropertyName("body")]
        public WebhookBody? Body { get; set; }
    }

    public class WebhookBody
    {
        [JsonPropertyName("extensionId")]
        public string? ExtensionId { get; set; }

        [JsonPropertyName("lastUpdated")]
        public string? LastUpdated { get; set; }

        [JsonPropertyName("changes")]
        public Change[]? Changes { get; set; }

        // Direct message properties (RingCentral sends SMS data directly in body)
        [JsonPropertyName("id")]
        public string? Id { get; set; }

        [JsonPropertyName("to")]
        public RecipientInfo[]? To { get; set; }

        [JsonPropertyName("from")]
        public RecipientInfo? From { get; set; }

        [JsonPropertyName("type")]
        public string? Type { get; set; }

        [JsonPropertyName("creationTime")]
        public string? CreationTime { get; set; }

        [JsonPropertyName("lastModifiedTime")]
        public string? LastModifiedTime { get; set; }

        [JsonPropertyName("readStatus")]
        public string? ReadStatus { get; set; }

        [JsonPropertyName("priority")]
        public string? Priority { get; set; }

        [JsonPropertyName("attachments")]
        public Attachment[]? Attachments { get; set; }

        [JsonPropertyName("direction")]
        public string? Direction { get; set; }

        [JsonPropertyName("availability")]
        public string? Availability { get; set; }

        [JsonPropertyName("subject")]
        public string? Subject { get; set; }

        [JsonPropertyName("messageStatus")]
        public string? MessageStatus { get; set; }

        [JsonPropertyName("conversationId")]
        public string? ConversationId { get; set; }

        [JsonPropertyName("conversation")]
        public ConversationInfo? Conversation { get; set; }

        [JsonPropertyName("eventType")]
        public string? EventType { get; set; }

        [JsonPropertyName("owner")]
        public OwnerInfo? Owner { get; set; }
    }

    public class Change
    {
        [JsonPropertyName("type")]
        public string? Type { get; set; }

        [JsonPropertyName("newCount")]
        public int? NewCount { get; set; }

        [JsonPropertyName("updatedCount")]
        public int? UpdatedCount { get; set; }

        [JsonPropertyName("newRecords")]
        public MessageRecord[]? NewRecords { get; set; }

        [JsonPropertyName("updatedRecords")]
        public MessageRecord[]? UpdatedRecords { get; set; }
    }

    public class MessageRecord
    {
        [JsonPropertyName("id")]
        public string? Id { get; set; }

        [JsonPropertyName("to")]
        public RecipientInfo[]? To { get; set; }

        [JsonPropertyName("from")]
        public RecipientInfo? From { get; set; }

        [JsonPropertyName("type")]
        public string? Type { get; set; }

        [JsonPropertyName("creationTime")]
        public string? CreationTime { get; set; }

        [JsonPropertyName("lastModifiedTime")]
        public string? LastModifiedTime { get; set; }

        [JsonPropertyName("readStatus")]
        public string? ReadStatus { get; set; }

        [JsonPropertyName("priority")]
        public string? Priority { get; set; }

        [JsonPropertyName("attachments")]
        public Attachment[]? Attachments { get; set; }

        [JsonPropertyName("direction")]
        public string? Direction { get; set; }

        [JsonPropertyName("availability")]
        public string? Availability { get; set; }

        [JsonPropertyName("subject")]
        public string? Subject { get; set; }

        [JsonPropertyName("messageStatus")]
        public string? MessageStatus { get; set; }

        [JsonPropertyName("conversationId")]
        public string? ConversationId { get; set; }
    }

    public class RecipientInfo
    {
        [JsonPropertyName("phoneNumber")]
        public string? PhoneNumber { get; set; }

        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("extensionId")]
        public string? ExtensionId { get; set; }

        [JsonPropertyName("target")]
        public bool? Target { get; set; }

        [JsonPropertyName("phoneNumberInfo")]
        public PhoneNumberInfo? PhoneNumberInfo { get; set; }
    }

    public class PhoneNumberInfo
    {
        [JsonPropertyName("countryCode")]
        public string? CountryCode { get; set; }

        [JsonPropertyName("nationalDestinationCode")]
        public string? NationalDestinationCode { get; set; }

        [JsonPropertyName("subscriberNumber")]
        public string? SubscriberNumber { get; set; }
    }

    public class Attachment
    {
        [JsonPropertyName("id")]
        public string? Id { get; set; }

        [JsonPropertyName("type")]
        public string? Type { get; set; }

        [JsonPropertyName("contentType")]
        public string? ContentType { get; set; }

        [JsonPropertyName("size")]
        public int? Size { get; set; }

        [JsonPropertyName("fileName")]
        public string? FileName { get; set; }

        [JsonPropertyName("content")]
        public string? Content { get; set; }
    }

    public class ConversationInfo
    {
        [JsonPropertyName("id")]
        public string? Id { get; set; }
    }

    public class OwnerInfo
    {
        [JsonPropertyName("extensionId")]
        public string? ExtensionId { get; set; }

        [JsonPropertyName("extensionType")]
        public string? ExtensionType { get; set; }

        [JsonPropertyName("name")]
        public string? Name { get; set; }
    }
} 