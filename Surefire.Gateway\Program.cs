using Serilog;
using Serilog.Events;
using System.IO;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File(
        Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs", "gateway-log-.txt"),
        rollingInterval: RollingInterval.Day,
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}{NewLine}")
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container.
builder.Services.AddControllers()
    .AddJsonOptions(options => {
        options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
    });

// Configure CORS for the gateway
builder.Services.AddCors(options =>
{
    options.AddPolicy("MainAppPolicy", policy =>
    {
        policy.WithOrigins(builder.Configuration["MainAppSettings:BaseUrl"] ?? "http://localhost:5000")
            .AllowAnyMethod()
            .AllowAnyHeader();
    });
});

// Configure HttpClient for forwarding requests to main app
builder.Services.AddHttpClient("SurefireApi", client =>
{
    var mainAppBaseUrl = builder.Configuration["MainAppSettings:BaseUrl"] ?? "http://localhost:5000";
    client.BaseAddress = new Uri(mainAppBaseUrl);
    
    // Add any default headers needed for authentication between gateway and main app
    if (!string.IsNullOrEmpty(builder.Configuration["MainAppSettings:ApiKey"]))
    {
        client.DefaultRequestHeaders.Add("X-API-Key", builder.Configuration["MainAppSettings:ApiKey"]);
    }
});

// Add health checks
builder.Services.AddHealthChecks();

try
{
    Log.Information("Starting Surefire Gateway");
    
    var app = builder.Build();

    // Configure the HTTP request pipeline.
    if (app.Environment.IsDevelopment())
    {
        app.UseDeveloperExceptionPage();
        Log.Information("Running in Development environment");
    }
    else
    {
        Log.Information("Running in Production environment");
    }

    // Global exception handler
    app.UseExceptionHandler(errorApp =>
    {
        errorApp.Run(async context =>
        {
            var exceptionHandlerPathFeature = context.Features.Get<Microsoft.AspNetCore.Diagnostics.IExceptionHandlerPathFeature>();
            var exception = exceptionHandlerPathFeature?.Error;
            
            Log.Error(exception, "Unhandled exception in the application");
            
            context.Response.StatusCode = 500;
            await context.Response.WriteAsJsonAsync(new { error = "An unexpected error occurred" });
        });
    });

    app.UseHttpsRedirection();

    // Use CORS
    app.UseCors("MainAppPolicy");

    // Add request logging middleware
    app.Use(async (context, next) =>
    {
        var requestId = Guid.NewGuid().ToString("N").Substring(0, 8);
        context.Items["RequestId"] = requestId;
        
        Log.Information("Request {RequestId} started: {Method} {Path}{QueryString}",
            requestId, context.Request.Method, context.Request.Path, context.Request.QueryString);
        
        var originalBodyStream = context.Response.Body;
        using var responseBody = new MemoryStream();
        context.Response.Body = responseBody;
        
        var startTime = DateTime.UtcNow;
        try
        {
            await next();
            
            var elapsedMs = (DateTime.UtcNow - startTime).TotalMilliseconds;
            Log.Information("Request {RequestId} completed in {ElapsedMs}ms with status code {StatusCode}",
                requestId, elapsedMs, context.Response.StatusCode);
        }
        catch (Exception ex)
        {
            var elapsedMs = (DateTime.UtcNow - startTime).TotalMilliseconds;
            Log.Error(ex, "Request {RequestId} failed after {ElapsedMs}ms", requestId, elapsedMs);
            throw;
        }
        finally
        {
            responseBody.Seek(0, SeekOrigin.Begin);
            await responseBody.CopyToAsync(originalBodyStream);
        }
    });

    // Add health check endpoint
    app.MapHealthChecks("/health");

    app.UseAuthorization();

    app.MapControllers();

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
