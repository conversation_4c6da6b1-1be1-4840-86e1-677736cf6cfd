# Surefire Gateway

The Surefire Gateway is a secure, public-facing API gateway that handles external webhook integrations and forwards them to the main Surefire application.

## Architecture

The Gateway follows a simple proxy pattern:

1. **Receive** - Gateway receives webhook requests from external services (e.g., RingCentral)
2. **Validate** - Gateway validates the request authenticity using service-specific validation
3. **Forward** - Gateway forwards validated requests to the main Surefire application
4. **Respond** - Gateway returns appropriate responses to the external service

```
External Service (RingCentral) → Gateway → Main Surefire Application
```

## SMS Webhook Flow

For SMS messages, the flow is:

1. RingCentral sends webhook to Gateway
2. Gateway validates the webhook
3. Gateway forwards payload to main Surefire application
4. Main app processes the SMS:
   - Saves to database
   - Broadcasts via SignalR to connected clients
5. UI updates in real-time

## Security Features

- **API Key Authentication**: Gateway and main app authenticate using API keys
- **Webhook Validation**: Validates incoming webhooks using service-specific methods
- **IP Filtering**: Optional IP address allowlist for additional security
- **No Database Access**: Gateway has no direct database access for enhanced security
- **Minimal Attack Surface**: Only exposes necessary endpoints

## Setup Instructions

### Configuration

1. Update `appsettings.json` with:
   - Main app URL and webhook endpoint
   - API key (use a strong, randomly generated key)
   - RingCentral validation secret

2. In the main Surefire application, add matching API key in `GatewaySettings`

### IIS Setup

1. Create a new IIS website for the Gateway
2. Configure with its own application pool
3. Set up HTTPS with a valid SSL certificate
4. Configure URL rewrite rules if needed

### RingCentral Configuration

1. In RingCentral developer portal, create a webhook subscription
2. Point the webhook URL to `https://your-gateway-domain.com/api/SmsWebhook`
3. Set up the validation method according to RingCentral documentation

## Troubleshooting

- Check logs for validation failures
- Verify API keys match between Gateway and main app
- Ensure firewall allows communication between Gateway and main app
- Use the `/health` endpoint to verify Gateway is running properly 