{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "System": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/gateway-debug-.txt", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}{NewLine}", "retainedFileCountLimit": 7}}]}, "MainAppSettings": {"BaseUrl": "https://localhost:7074", "WebhookForwardUrl": "https://localhost:7074/api/internal/smswebhook", "ApiKey": "MetroGateway_07122025"}, "RingCentral": {"ValidationSecret": "dev_validation_secret_for_testing"}, "DevelopmentMode": true, "EnableDebugLogging": true}