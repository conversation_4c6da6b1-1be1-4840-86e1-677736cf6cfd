{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/gateway-log-.txt", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}{NewLine}", "retainedFileCountLimit": 31}}, {"Name": "File", "Args": {"path": "logs/gateway-errors-.txt", "rollingInterval": "Day", "restrictedToMinimumLevel": "Error", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}{NewLine}", "retainedFileCountLimit": 31}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "MainAppSettings": {"BaseUrl": "https://surefire.local", "WebhookForwardUrl": "https://surefire.local/api/internal/smswebhook", "ApiKey": "MetroGateway_07122025"}, "RingCentral": {"ValidationSecret": "784316794367834267432679432"}, "DevelopmentMode": true, "EnableDebugLogging": true}