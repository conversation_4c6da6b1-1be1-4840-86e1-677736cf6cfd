﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.35027.167
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Surefire", "Surefire\Surefire.csproj", "{6C9F7FD0-469D-4B43-9FFB-4F29D2D7617B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Plugins", "Plugins", "{2A0E3944-BB6A-4FFE-8088-9699E502C2F6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AppliedEpic", "Plugins\AppliedEpicCRM\AppliedEpic.csproj", "{BC154E37-A3EE-41BB-82FE-98AB9D21885D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RingCentral", "Plugins\RingCentral\RingCentral.csproj", "{0BA53E63-D714-435F-AED3-13E28DDACBC7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ePayPolicy", "Plugins\ePayPolicy\ePayPolicy.csproj", "{90F54A2C-F922-4EEA-A91B-47845B4F28A2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Windows", "Windows", "{104A90E1-18F2-4B52-B97B-39AB8AAF2131}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Surefire.Tray", "Applications\Surefire.Tray\Surefire.Tray.csproj", "{5A5B9286-22B3-47B5-B0DE-2B21D095BCD1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Surefire.Call", "Applications\Surefire.Call\Surefire.Call.csproj", "{93C365D2-C4A4-45D9-9CEE-22A02E668C1E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Surefire.Desktop", "Applications\Surefire.Desktop\Surefire.Desktop.csproj", "{0FA5252D-E8DB-4BA6-9389-1AD7BC6957BA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Surefire.Outlook", "Applications\Surefire.Outlook\Surefire.Outlook.csproj", "{F3804C7D-4725-432C-A5D3-5EB594D049F4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Surefire.Goodies", "Applications\Surefire.Goodies\Surefire.Goodies.csproj", "{0E21C8C5-8741-3473-C52A-5A7289A62CD9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Surefire.Gateway", "Surefire.Gateway\Surefire.Gateway.csproj", "{83555F15-B3C6-4B56-9648-68290E8C4964}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6C9F7FD0-469D-4B43-9FFB-4F29D2D7617B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6C9F7FD0-469D-4B43-9FFB-4F29D2D7617B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6C9F7FD0-469D-4B43-9FFB-4F29D2D7617B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6C9F7FD0-469D-4B43-9FFB-4F29D2D7617B}.Release|Any CPU.Build.0 = Release|Any CPU
		{BC154E37-A3EE-41BB-82FE-98AB9D21885D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BC154E37-A3EE-41BB-82FE-98AB9D21885D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0BA53E63-D714-435F-AED3-13E28DDACBC7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0BA53E63-D714-435F-AED3-13E28DDACBC7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{90F54A2C-F922-4EEA-A91B-47845B4F28A2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{90F54A2C-F922-4EEA-A91B-47845B4F28A2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A5B9286-22B3-47B5-B0DE-2B21D095BCD1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A5B9286-22B3-47B5-B0DE-2B21D095BCD1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A5B9286-22B3-47B5-B0DE-2B21D095BCD1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A5B9286-22B3-47B5-B0DE-2B21D095BCD1}.Release|Any CPU.Build.0 = Release|Any CPU
		{93C365D2-C4A4-45D9-9CEE-22A02E668C1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93C365D2-C4A4-45D9-9CEE-22A02E668C1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0FA5252D-E8DB-4BA6-9389-1AD7BC6957BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0FA5252D-E8DB-4BA6-9389-1AD7BC6957BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F3804C7D-4725-432C-A5D3-5EB594D049F4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F3804C7D-4725-432C-A5D3-5EB594D049F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F3804C7D-4725-432C-A5D3-5EB594D049F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F3804C7D-4725-432C-A5D3-5EB594D049F4}.Release|Any CPU.Build.0 = Release|Any CPU
		{0E21C8C5-8741-3473-C52A-5A7289A62CD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0E21C8C5-8741-3473-C52A-5A7289A62CD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{83555F15-B3C6-4B56-9648-68290E8C4964}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{83555F15-B3C6-4B56-9648-68290E8C4964}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{83555F15-B3C6-4B56-9648-68290E8C4964}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{83555F15-B3C6-4B56-9648-68290E8C4964}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{BC154E37-A3EE-41BB-82FE-98AB9D21885D} = {2A0E3944-BB6A-4FFE-8088-9699E502C2F6}
		{0BA53E63-D714-435F-AED3-13E28DDACBC7} = {2A0E3944-BB6A-4FFE-8088-9699E502C2F6}
		{90F54A2C-F922-4EEA-A91B-47845B4F28A2} = {2A0E3944-BB6A-4FFE-8088-9699E502C2F6}
		{5A5B9286-22B3-47B5-B0DE-2B21D095BCD1} = {104A90E1-18F2-4B52-B97B-39AB8AAF2131}
		{93C365D2-C4A4-45D9-9CEE-22A02E668C1E} = {104A90E1-18F2-4B52-B97B-39AB8AAF2131}
		{0FA5252D-E8DB-4BA6-9389-1AD7BC6957BA} = {104A90E1-18F2-4B52-B97B-39AB8AAF2131}
		{F3804C7D-4725-432C-A5D3-5EB594D049F4} = {104A90E1-18F2-4B52-B97B-39AB8AAF2131}
		{0E21C8C5-8741-3473-C52A-5A7289A62CD9} = {104A90E1-18F2-4B52-B97B-39AB8AAF2131}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1DB27740-CCD5-4B49-8E91-2DF484708326}
	EndGlobalSection
EndGlobal
