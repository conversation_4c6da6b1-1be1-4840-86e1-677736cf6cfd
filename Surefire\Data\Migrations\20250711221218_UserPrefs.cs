﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Surefire.Migrations
{
    /// <inheritdoc />
    public partial class UserPrefs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "EnableAnimations",
                table: "AspNetUsers",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "EnableAudio",
                table: "AspNetUsers",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "EnableSimpleMode",
                table: "AspNetUsers",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HomepageLayoutJSON",
                table: "AspNetUsers",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EnableAnimations",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "EnableAudio",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "EnableSimpleMode",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "HomepageLayoutJSON",
                table: "AspNetUsers");
        }
    }
}
