using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Surefire.Domain.Chat;
using Surefire.Domain.Chat.Services;
using Surefire.Hubs;
using Surefire.Domain.Logs;
using System.Text.Json;

namespace Surefire.Domain.Chat.Controllers
{
    [ApiController]
    [Route("api/internal/[controller]")]
    public class SmsWebhookController : ControllerBase
    {
        private readonly ILogger<SmsWebhookController> _logger;
        private readonly SmsMessageService _smsMessageService;
        private readonly IHubContext<MessagingHub> _hubContext;
        private readonly IConfiguration _configuration;
        private readonly ILoggingService _loggingService;

        public SmsWebhookController(
            ILogger<SmsWebhookController> logger,
            SmsMessageService smsMessageService,
            IHubContext<MessagingHub> hubContext,
            IConfiguration configuration,
            ILoggingService loggingService)
        {
            _logger = logger;
            _smsMessageService = smsMessageService;
            _hubContext = hubContext;
            _configuration = configuration;
            _loggingService = loggingService;
        }

        [HttpGet("test")]
        public async Task<IActionResult> TestConnection()
        {
            var testId = Guid.NewGuid().ToString("N").Substring(0, 8);
            
            try
            {
                await _loggingService.LogAsync(
                    LogLevel.Information,
                    $"Gateway connectivity test endpoint called [TestId: {testId}]",
                    "GatewayConnectivityTest");
                
                var response = new
                {
                    success = true,
                    message = "Gateway connectivity test successful",
                    testId = testId,
                    timestamp = DateTime.UtcNow,
                    serverTime = DateTime.Now,
                    configuration = new
                    {
                        expectedApiKey = _configuration["GatewaySettings:ApiKey"],
                        allowedIpAddresses = _configuration.GetSection("GatewaySettings:AllowedIpAddresses").Get<string[]>()
                    }
                };
                
                await _loggingService.LogAsync(
                    LogLevel.Information,
                    $"Gateway connectivity test completed successfully [TestId: {testId}]",
                    "GatewayConnectivityTest");
                
                return Ok(response);
            }
            catch (Exception ex)
            {
                await _loggingService.LogAsync(
                    LogLevel.Error,
                    $"Gateway connectivity test failed [TestId: {testId}]: {ex.Message}",
                    "GatewayConnectivityTest",
                    ex);
                
                return StatusCode(500, new { success = false, error = ex.Message, testId = testId });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ReceiveForwardedWebhook([FromBody] JsonElement payload, [FromHeader(Name = "X-API-Key")] string apiKey, [FromHeader(Name = "X-Request-ID")] string requestId = "")
        {
            requestId = !string.IsNullOrEmpty(requestId) ? requestId : Guid.NewGuid().ToString("N").Substring(0, 8);
            
            try
            {
                await _loggingService.LogAsync(
                    LogLevel.Information,
                    $"=== INTERNAL SMS WEBHOOK RECEIVED [RequestId: {requestId}] ===",
                    "InternalSmsWebhook");
                
                _logger.LogInformation("=== INTERNAL SMS WEBHOOK RECEIVED [RequestId: {RequestId}] ===", requestId);
                
                // Log the incoming webhook
                await _loggingService.LogAsync(
                    LogLevel.Information, 
                    $"Received SMS webhook from Gateway [RequestId: {requestId}]", 
                    "InternalSmsWebhook");
                
                // Log raw payload for debugging
                var rawPayload = payload.GetRawText();
                await _loggingService.LogAsync(
                    LogLevel.Information,
                    $"Raw payload received [RequestId: {requestId}]: {rawPayload}",
                    "InternalSmsWebhook");
                
                _logger.LogInformation("Raw payload received [RequestId: {RequestId}]: {Payload}", requestId, rawPayload);
                
                // Validate API key for internal communication
                var expectedApiKey = _configuration["GatewaySettings:ApiKey"];
                
                await _loggingService.LogAsync(
                    LogLevel.Information,
                    $"Validating API key [RequestId: {requestId}] - Expected: {expectedApiKey}, Received: {apiKey}",
                    "InternalSmsWebhook");
                
                _logger.LogInformation("Validating API key [RequestId: {RequestId}] - Expected: {Expected}, Received: {Received}", 
                    requestId, expectedApiKey, apiKey);
                
                if (string.IsNullOrEmpty(expectedApiKey) || apiKey != expectedApiKey)
                {
                    string errorMessage = $"Invalid API key for internal webhook - Expected: {expectedApiKey}, Received: {apiKey}";
                    _logger.LogWarning(errorMessage);
                    await _loggingService.LogAsync(LogLevel.Warning, errorMessage, "InternalSmsWebhook");
                    return Unauthorized(new { error = "Invalid API key" });
                }

                await _loggingService.LogAsync(
                    LogLevel.Information,
                    $"API key validation successful [RequestId: {requestId}]",
                    "InternalSmsWebhook");
                
                _logger.LogInformation("API key validation successful [RequestId: {RequestId}]", requestId);

                // Deserialize the payload
                dynamic? webhookData = null;
                try
                {
                    webhookData = JsonSerializer.Deserialize<dynamic>(rawPayload);
                    
                    await _loggingService.LogAsync(
                        LogLevel.Information,
                        $"Successfully deserialized webhook payload [RequestId: {requestId}]",
                        "InternalSmsWebhook");
                    
                    _logger.LogInformation("Successfully deserialized webhook payload [RequestId: {RequestId}]", requestId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to deserialize webhook payload [RequestId: {RequestId}]", requestId);
                    await _loggingService.LogAsync(LogLevel.Error, $"Failed to deserialize webhook payload: {ex.Message}", "InternalSmsWebhook", ex);
                    return BadRequest(new { error = "Invalid payload format" });
                }

                await _loggingService.LogAsync(
                    LogLevel.Information,
                    $"Processing forwarded webhook from Gateway [RequestId: {requestId}]",
                    "InternalSmsWebhook");
                
                _logger.LogInformation("Processing forwarded webhook from Gateway [RequestId: {RequestId}]", requestId);

                // Process SMS messages from the webhook
                var smsMessages = ExtractSmsMessages(payload);
                
                await _loggingService.LogAsync(
                    LogLevel.Information, 
                    $"Extracted {smsMessages.Count} SMS messages from webhook [RequestId: {requestId}]", 
                    "InternalSmsWebhook");
                
                if (smsMessages.Count == 0)
                {
                    await _loggingService.LogAsync(
                        LogLevel.Information,
                        $"No SMS messages found in webhook payload [RequestId: {requestId}]",
                        "InternalSmsWebhook");
                    
                    _logger.LogInformation("No SMS messages found in webhook payload [RequestId: {RequestId}]", requestId);
                    return Ok(new { success = true, messagesProcessed = 0, message = "No SMS messages found" });
                }
                
                foreach (var smsMessage in smsMessages)
                {
                    await _loggingService.LogAsync(
                        LogLevel.Information,
                        $"Processing SMS message [RequestId: {requestId}] - ID: {smsMessage.Id}, Phone: {smsMessage.PhoneNumber}, Inbound: {smsMessage.IsInbound}",
                        "InternalSmsWebhook");
                    
                    _logger.LogInformation("Processing SMS message [RequestId: {RequestId}] - ID: {MessageId}, Phone: {Phone}, Inbound: {Inbound}", 
                        requestId, smsMessage.Id, smsMessage.PhoneNumber, smsMessage.IsInbound);
                    
                    // Store in database
                    var storedMessage = await _smsMessageService.StoreSmsMessageAsync(smsMessage);
                    
                    if (storedMessage != null)
                    {
                        string logMessage = $"Stored SMS message: ID={smsMessage.Id}, Phone={smsMessage.PhoneNumber}, Inbound={smsMessage.IsInbound}";
                        _logger.LogInformation(logMessage);
                        await _loggingService.LogAsync(LogLevel.Information, logMessage, "InternalSmsWebhook");
                        
                        // Broadcast to all clients for conversation list updates
                        await _hubContext.Clients.All.SendAsync("ReceiveSmsMessage", smsMessage);
                        
                        // Also broadcast to specific SMS chat group for this phone number
                        var normalizedPhone = Shared.Helpers.StringHelper.NormalizePhoneNumber(smsMessage.PhoneNumber);
                        await _hubContext.Clients.Group($"SmsChat_{normalizedPhone}")
                            .SendAsync("ReceiveSmsMessage", smsMessage);
                        
                        await _loggingService.LogAsync(
                            LogLevel.Information, 
                            $"Broadcasted SMS message via SignalR: Phone={normalizedPhone}, Inbound={smsMessage.IsInbound}", 
                            "InternalSmsWebhook");
                    }
                    else
                    {
                        string errorMessage = $"Failed to store SMS message: ID={smsMessage.Id}";
                        _logger.LogWarning(errorMessage);
                        await _loggingService.LogAsync(LogLevel.Warning, errorMessage, "InternalSmsWebhook");
                    }
                }

                await _loggingService.LogAsync(
                    LogLevel.Information,
                    $"Successfully processed webhook [RequestId: {requestId}] - Messages: {smsMessages.Count}",
                    "InternalSmsWebhook");
                
                _logger.LogInformation("Successfully processed webhook [RequestId: {RequestId}] - Messages: {Count}", requestId, smsMessages.Count);
                return Ok(new { success = true, messagesProcessed = smsMessages.Count });
            }
            catch (Exception ex)
            {
                string errorMessage = $"Error processing forwarded webhook: {ex.Message}";
                _logger.LogError(ex, errorMessage);
                await _loggingService.LogAsync(LogLevel.Error, errorMessage, "InternalSmsWebhook", ex);
                return StatusCode(500, new { success = false, error = "Internal server error" });
            }
        }

        private List<SmsMessage> ExtractSmsMessages(JsonElement payload)
        {
            var messages = new List<SmsMessage>();
            
            try
            {
                _loggingService.LogAsync(
                    LogLevel.Information,
                    "Starting SMS message extraction from payload",
                    "InternalSmsWebhook").Wait();
                
                _logger.LogInformation("Starting SMS message extraction from payload");
                
                // Check if this is a valid webhook payload with the expected structure
                if (payload.TryGetProperty("body", out var body))
                {
                    _loggingService.LogAsync(
                        LogLevel.Information,
                        "Found 'body' property in payload",
                        "InternalSmsWebhook").Wait();
                    
                    _logger.LogInformation("Found 'body' property in payload");
                    
                    // Check if this is a direct SMS message (RingCentral sends SMS data directly in body)
                    if (body.TryGetProperty("type", out var typeProperty) && 
                        typeProperty.GetString() == "SMS" &&
                        body.TryGetProperty("id", out var idProperty))
                    {
                        _loggingService.LogAsync(
                            LogLevel.Information,
                            "Found direct SMS message in body",
                            "InternalSmsWebhook").Wait();
                        
                        _logger.LogInformation("Found direct SMS message in body");
                        
                        var smsMessage = ParseMessageRecord(body);
                        if (smsMessage != null)
                        {
                            messages.Add(smsMessage);
                            
                            _loggingService.LogAsync(
                                LogLevel.Information,
                                $"Added direct SMS message: {smsMessage.Id}",
                                "InternalSmsWebhook").Wait();
                            
                            _logger.LogInformation("Added direct SMS message: {Id}", smsMessage.Id);
                        }
                    }
                    // Check for changes array format
                    else if (body.TryGetProperty("changes", out var changes))
                    {
                        _loggingService.LogAsync(
                            LogLevel.Information,
                            $"Found 'changes' property in body - ValueKind: {changes.ValueKind}",
                            "InternalSmsWebhook").Wait();
                        
                        _logger.LogInformation("Found 'changes' property in body - ValueKind: {ValueKind}", changes.ValueKind);
                        
                        if (changes.ValueKind == JsonValueKind.Array)
                        {
                            _loggingService.LogAsync(
                                LogLevel.Information,
                                $"Changes is an array with {changes.GetArrayLength()} items",
                                "InternalSmsWebhook").Wait();
                            
                            _logger.LogInformation("Changes is an array with {Length} items", changes.GetArrayLength());
                            
                            // Process each change
                            foreach (var change in changes.EnumerateArray())
                            {
                                _loggingService.LogAsync(
                                    LogLevel.Information,
                                    "Processing change item",
                                    "InternalSmsWebhook").Wait();
                                
                                _logger.LogInformation("Processing change item");
                                
                                // Check if this is an SMS change
                                if (change.TryGetProperty("type", out var type))
                                {
                                    var changeType = type.GetString();
                                    
                                    _loggingService.LogAsync(
                                        LogLevel.Information,
                                        $"Change type: {changeType}",
                                        "InternalSmsWebhook").Wait();
                                    
                                    _logger.LogInformation("Change type: {Type}", changeType);
                                    
                                    if (changeType == "SMS")
                                    {
                                        _loggingService.LogAsync(
                                            LogLevel.Information,
                                            "Found SMS change, processing records",
                                            "InternalSmsWebhook").Wait();
                                        
                                        _logger.LogInformation("Found SMS change, processing records");
                                        
                                        // Process new records
                                        if (change.TryGetProperty("newRecords", out var newRecords) &&
                                            newRecords.ValueKind == JsonValueKind.Array)
                                        {
                                            _loggingService.LogAsync(
                                                LogLevel.Information,
                                                $"Processing {newRecords.GetArrayLength()} new records",
                                                "InternalSmsWebhook").Wait();
                                            
                                            _logger.LogInformation("Processing {Count} new records", newRecords.GetArrayLength());
                                            
                                            foreach (var record in newRecords.EnumerateArray())
                                            {
                                                var smsMessage = ParseMessageRecord(record);
                                                if (smsMessage != null)
                                                {
                                                    messages.Add(smsMessage);
                                                    
                                                    _loggingService.LogAsync(
                                                        LogLevel.Information,
                                                        $"Added new SMS message: {smsMessage.Id}",
                                                        "InternalSmsWebhook").Wait();
                                                    
                                                    _logger.LogInformation("Added new SMS message: {Id}", smsMessage.Id);
                                                }
                                            }
                                        }
                                        else
                                        {
                                            _loggingService.LogAsync(
                                                LogLevel.Information,
                                                "No new records found or not an array",
                                                "InternalSmsWebhook").Wait();
                                            
                                            _logger.LogInformation("No new records found or not an array");
                                        }
                                        
                                        // Process updated records
                                        if (change.TryGetProperty("updatedRecords", out var updatedRecords) &&
                                            updatedRecords.ValueKind == JsonValueKind.Array)
                                        {
                                            _loggingService.LogAsync(
                                                LogLevel.Information,
                                                $"Processing {updatedRecords.GetArrayLength()} updated records",
                                                "InternalSmsWebhook").Wait();
                                            
                                            _logger.LogInformation("Processing {Count} updated records", updatedRecords.GetArrayLength());
                                            
                                            foreach (var record in updatedRecords.EnumerateArray())
                                            {
                                                var smsMessage = ParseMessageRecord(record);
                                                if (smsMessage != null)
                                                {
                                                    messages.Add(smsMessage);
                                                    
                                                    _loggingService.LogAsync(
                                                        LogLevel.Information,
                                                        $"Added updated SMS message: {smsMessage.Id}",
                                                        "InternalSmsWebhook").Wait();
                                                    
                                                    _logger.LogInformation("Added updated SMS message: {Id}", smsMessage.Id);
                                                }
                                            }
                                        }
                                        else
                                        {
                                            _loggingService.LogAsync(
                                                LogLevel.Information,
                                                "No updated records found or not an array",
                                                "InternalSmsWebhook").Wait();
                                            
                                            _logger.LogInformation("No updated records found or not an array");
                                        }
                                    }
                                }
                                else
                                {
                                    _loggingService.LogAsync(
                                        LogLevel.Warning,
                                        "Change item missing 'type' property",
                                        "InternalSmsWebhook").Wait();
                                    
                                    _logger.LogWarning("Change item missing 'type' property");
                                }
                            }
                        }
                        else
                        {
                            _loggingService.LogAsync(
                                LogLevel.Warning,
                                $"Changes property is not an array - ValueKind: {changes.ValueKind}",
                                "InternalSmsWebhook").Wait();
                            
                            _logger.LogWarning("Changes property is not an array - ValueKind: {ValueKind}", changes.ValueKind);
                        }
                    }
                    else
                    {
                        _loggingService.LogAsync(
                            LogLevel.Warning,
                            "No 'changes' property found in body and no direct SMS message",
                            "InternalSmsWebhook").Wait();
                        
                        _logger.LogWarning("No 'changes' property found in body and no direct SMS message");
                    }
                }
                else
                {
                    _loggingService.LogAsync(
                        LogLevel.Warning,
                        "No 'body' property found in payload",
                        "InternalSmsWebhook").Wait();
                    
                    _logger.LogWarning("No 'body' property found in payload");
                }
                
                _loggingService.LogAsync(
                    LogLevel.Information,
                    $"SMS message extraction completed - Found {messages.Count} messages",
                    "InternalSmsWebhook").Wait();
                
                _logger.LogInformation("SMS message extraction completed - Found {Count} messages", messages.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting SMS messages from webhook: {Message}", ex.Message);
                _loggingService.LogAsync(LogLevel.Error, $"Error extracting SMS messages from webhook: {ex.Message}", "InternalSmsWebhook", ex).Wait();
            }
            
            return messages;
        }

        private SmsMessage ParseMessageRecord(JsonElement record)
        {
            try
            {
                _loggingService.LogAsync(
                    LogLevel.Information,
                    "Starting to parse message record",
                    "InternalSmsWebhook").Wait();
                
                // Get message ID
                if (!record.TryGetProperty("id", out var id))
                {
                    _loggingService.LogAsync(
                        LogLevel.Warning,
                        "Message record missing 'id' property",
                        "InternalSmsWebhook").Wait();
                    return null;
                }
                
                // Handle both string and numeric IDs from RingCentral
                string messageId;
                if (id.ValueKind == JsonValueKind.String)
                {
                    var idString = id.GetString();
                    if (string.IsNullOrEmpty(idString))
                    {
                        _loggingService.LogAsync(
                            LogLevel.Warning,
                            "Message record has empty string ID",
                            "InternalSmsWebhook").Wait();
                        return null;
                    }
                    messageId = idString;
                }
                else if (id.ValueKind == JsonValueKind.Number)
                {
                    messageId = id.GetInt64().ToString();
                }
                else
                {
                    _loggingService.LogAsync(
                        LogLevel.Warning,
                        $"Message record has invalid 'id' property type: {id.ValueKind}",
                        "InternalSmsWebhook").Wait();
                    return null;
                }
                _loggingService.LogAsync(
                    LogLevel.Information,
                    $"Parsing message record with ID: {messageId}",
                    "InternalSmsWebhook").Wait();

                _loggingService.LogAsync(
                    LogLevel.Information,
                    "About to parse direction",
                    "InternalSmsWebhook").Wait();
                
                // Get message direction to determine if inbound or outbound
                bool isInbound = false;
                if (record.TryGetProperty("direction", out var direction))
                {
                    var directionValue = direction.GetString();
                    if (!string.IsNullOrEmpty(directionValue))
                    {
                        isInbound = directionValue == "Inbound";

                        _loggingService.LogAsync(
                            LogLevel.Information,
                            $"Message direction: {directionValue}, IsInbound: {isInbound}",
                            "InternalSmsWebhook").Wait();
                    }
                }

                _loggingService.LogAsync(
                    LogLevel.Information,
                    "About to parse creation time",
                    "InternalSmsWebhook").Wait();

                // Get message creation time
                DateTime timestamp = DateTime.UtcNow;
                if (record.TryGetProperty("creationTime", out var creationTime))
                {
                    var creationTimeString = creationTime.GetString();
                    if (!string.IsNullOrEmpty(creationTimeString) &&
                        DateTime.TryParse(creationTimeString, out var parsedTime))
                    {
                        timestamp = parsedTime;

                        _loggingService.LogAsync(
                            LogLevel.Information,
                            $"Message creation time: {timestamp}",
                            "InternalSmsWebhook").Wait();
                    }
                }
                
                // Get phone number and text from the message
                string phoneNumber = "";
                string text = "";
                
                // For inbound messages, get the sender's phone number
                if (isInbound && record.TryGetProperty("from", out var from))
                {
                    if (from.TryGetProperty("phoneNumber", out var fromPhone))
                    {
                        var fromPhoneNumber = fromPhone.GetString();
                        if (!string.IsNullOrEmpty(fromPhoneNumber))
                        {
                            phoneNumber = fromPhoneNumber;

                            _loggingService.LogAsync(
                                LogLevel.Information,
                                $"Inbound message from phone: {phoneNumber}",
                                "InternalSmsWebhook").Wait();
                        }
                    }
                }
                // For outbound messages, get the recipient's phone number
                else if (!isInbound && record.TryGetProperty("to", out var to) &&
                    to.ValueKind == JsonValueKind.Array &&
                    to.GetArrayLength() > 0)
                {
                    var firstRecipient = to[0];
                    if (firstRecipient.TryGetProperty("phoneNumber", out var toPhone))
                    {
                        var toPhoneNumber = toPhone.GetString();
                        if (!string.IsNullOrEmpty(toPhoneNumber))
                        {
                            phoneNumber = toPhoneNumber;

                            _loggingService.LogAsync(
                                LogLevel.Information,
                                $"Outbound message to phone: {phoneNumber}",
                                "InternalSmsWebhook").Wait();
                        }
                    }
                }

                _loggingService.LogAsync(
                    LogLevel.Information,
                    "About to process attachments for message text",
                    "InternalSmsWebhook").Wait();

                // Get message text from attachments or subject
                if (record.TryGetProperty("attachments", out var attachments) &&
                    attachments.ValueKind == JsonValueKind.Array)
                {
                    _loggingService.LogAsync(
                        LogLevel.Information,
                        $"Processing {attachments.GetArrayLength()} attachments",
                        "InternalSmsWebhook").Wait();
                    
                    foreach (var attachment in attachments.EnumerateArray())
                    {
                        if (attachment.TryGetProperty("type", out var attachType) &&
                            attachType.GetString() == "Text")
                        {
                            // For text attachments, we need to get the content via API call or use subject
                            // For now, let's use the subject field as the message text
                            if (record.TryGetProperty("subject", out var subject))
                            {
                                var subjectText = subject.GetString();
                                if (!string.IsNullOrEmpty(subjectText))
                                {
                                    text = subjectText;

                                    _loggingService.LogAsync(
                                        LogLevel.Information,
                                        $"Found text from subject field with length: {text.Length}",
                                        "InternalSmsWebhook").Wait();
                                }
                            }

                            // Also check if attachment has content property
                            if (attachment.TryGetProperty("content", out var content))
                            {
                                var contentText = content.GetString();
                                if (!string.IsNullOrEmpty(contentText))
                                {
                                    text = contentText;

                                    _loggingService.LogAsync(
                                        LogLevel.Information,
                                        $"Found text attachment with content length: {text.Length}",
                                        "InternalSmsWebhook").Wait();
                                }
                            }
                            break;
                        }
                    }
                }
                
                // If no text found from attachments, try subject field
                if (string.IsNullOrEmpty(text) && record.TryGetProperty("subject", out var subjectFallback))
                {
                    var fallbackText = subjectFallback.GetString();
                    if (!string.IsNullOrEmpty(fallbackText))
                    {
                        text = fallbackText;

                        _loggingService.LogAsync(
                            LogLevel.Information,
                            $"Using subject as message text: {text}",
                            "InternalSmsWebhook").Wait();
                    }
                }
                
                // Log the parsed message
                var textPreview = string.IsNullOrEmpty(text) ? "[empty]" :
                    text.Length > 30 ? text.Substring(0, 30) + "..." : text;

                _loggingService.LogAsync(
                    LogLevel.Information,
                    $"Parsed SMS message: ID={messageId}, Phone={phoneNumber}, Direction={(isInbound ? "Inbound" : "Outbound")}, Text={textPreview}",
                    "InternalSmsWebhook").Wait();
                
                // Validate required fields before creating SMS message
                if (string.IsNullOrEmpty(messageId))
                {
                    _loggingService.LogAsync(
                        LogLevel.Warning,
                        "Cannot create SMS message: messageId is null or empty",
                        "InternalSmsWebhook").Wait();
                    return null;
                }

                if (string.IsNullOrEmpty(phoneNumber))
                {
                    _loggingService.LogAsync(
                        LogLevel.Warning,
                        $"Cannot create SMS message: phoneNumber is null or empty for message ID {messageId}",
                        "InternalSmsWebhook").Wait();
                    return null;
                }

                // Create and return the SMS message
                var smsMessage = new SmsMessage
                {
                    Id = messageId,
                    PhoneNumber = phoneNumber,
                    Text = text ?? "", // Ensure text is never null
                    Timestamp = timestamp,
                    IsInbound = isInbound
                };

                _loggingService.LogAsync(
                    LogLevel.Information,
                    $"Successfully created SMS message object for ID: {messageId}",
                    "InternalSmsWebhook").Wait();

                return smsMessage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing message record: {Message}", ex.Message);
                _loggingService.LogAsync(LogLevel.Error, $"Error parsing message record: {ex.Message}\nStack trace: {ex.StackTrace}", "InternalSmsWebhook", ex).Wait();
                return null;
            }
        }
    }
} 