using Microsoft.AspNetCore.SignalR;
using Surefire.Domain.Chat;
using System.Threading.Tasks;
using Surefire.Domain.Chat.Components;

namespace Surefire.Hubs
{
    public class MessagingHub : Hub
    {
        private readonly ChatService _chatService;

        public MessagingHub(ChatService chatService)
        {
            _chatService = chatService;
        }

        public async Task SendStaffMessage(string message, string? userId = null, string? userName = null, string? userFullName = null, string? userPictureUrl = null)
        {
            // Create a message object
            var messageItem = new MessageItem
            {
                Content = message,
                Timestamp = DateTime.UtcNow, // Use UTC for consistency
                IsFromCurrentUser = false,
                UserId = userId ?? "unknown",
                UserName = userName ?? "Unknown User",
                UserFullName = userFullName ?? "Unknown User",
                UserPictureUrl = userPictureUrl
            };
            
            // Store the message in the chat service
            _chatService.AddStaffChatMessage(messageItem);
            
            // Broadcast the message to all connected clients
            await Clients.All.SendAsync("ReceiveStaffMessage", messageItem);
        }

        public async Task JoinStaffChat()
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "StaffChat");
        }

        public async Task LeaveStaffChat()
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, "StaffChat");
        }

        public override async Task OnConnectedAsync()
        {
            // Automatically join staff chat when connected
            await JoinStaffChat();
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            await LeaveStaffChat();
            await base.OnDisconnectedAsync(exception);
        }

        // Legacy method for backward compatibility
        public async Task SendMessage(string message, string? userId = null, string? userName = null, string? userFullName = null, string? userPictureUrl = null)
        {
            await SendStaffMessage(message, userId, userName, userFullName, userPictureUrl);
        }

        // SMS Message Methods
        public async Task SendSmsMessage(string phoneNumber, string message, string? messageId = null)
        {
            Console.WriteLine($"MessagingHub: SendSmsMessage called - Phone: {phoneNumber}, Message: {message?.Substring(0, Math.Min(message?.Length ?? 0, 30))}, MessageId: {messageId}");
            
            // Send the SMS via the chat service
            var success = await _chatService.SendSmsAsync(phoneNumber, message);
            
            if (success)
            {
                // Create SMS message for broadcasting (will be stored by RingCentral webhook when confirmed)
                var smsMessage = new SmsMessage
                {
                    Id = messageId ?? Guid.NewGuid().ToString(),
                    PhoneNumber = phoneNumber,
                    Text = message,
                    Timestamp = DateTime.UtcNow, // Use UTC to match RingCentral timestamps
                    IsInbound = false
                };
                
                Console.WriteLine($"MessagingHub: Broadcasting SMS message - ID: {smsMessage.Id}, Phone: {phoneNumber}");
                
                // Normalize phone number for consistent group targeting
                var normalizedPhone = Surefire.Domain.Shared.Helpers.StringHelper.NormalizePhoneNumber(phoneNumber);
                
                // Broadcast to specific SMS chat group only (avoid broadcasting to all clients for sent messages)
                await Clients.Group($"SmsChat_{normalizedPhone}").SendAsync("ReceiveSmsMessage", smsMessage);
                
                // No cache invalidation needed - database-first approach
            }
            else
            {
                Console.WriteLine($"MessagingHub: Failed to send SMS message to {phoneNumber}");
            }
        }

        public async Task BroadcastSmsMessage(SmsMessage message)
        {
            // Broadcast an SMS message to specific SMS chat group and all clients for conversation list updates
            if (!string.IsNullOrEmpty(message.PhoneNumber))
            {
                var normalizedPhone = Surefire.Domain.Shared.Helpers.StringHelper.NormalizePhoneNumber(message.PhoneNumber);
                
                // Broadcast to all clients for conversation list updates
                await Clients.All.SendAsync("ReceiveSmsMessage", message);
                
                // Also broadcast to specific SMS chat group for this phone number
                await Clients.Group($"SmsChat_{normalizedPhone}").SendAsync("ReceiveSmsMessage", message);
            }
            
            // No cache invalidation needed - database-first approach
        }

        public async Task JoinSmsChat(string phoneNumber)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"SmsChat_{phoneNumber}");
        }

        public async Task LeaveSmsChat(string phoneNumber)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"SmsChat_{phoneNumber}");
        }
    }
} 