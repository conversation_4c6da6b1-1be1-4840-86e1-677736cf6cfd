﻿using Surefire.Data;

namespace Surefire.Domain.Chat
{
    public class SmsMessage
    {
        public string Id { get; set; }
        public string PhoneNumber { get; set; }
        public string Text { get; set; }
        public DateTime Timestamp { get; set; }
        public bool IsInbound { get; set; } // true if received, false if sent
    }

    public class SmsConversation
    {
        public string PhoneNumber { get; set; }
        public string ContactName { get; set; }
        public List<SmsMessage> Messages { get; set; } = new List<SmsMessage>();
    }

    // Database entity for storing SMS messages with confirmation tracking
    public class SmsMessageEntity
    {
        public int Id { get; set; }
        public string RingCentralId { get; set; } // The original RingCentral message ID
        public string PhoneNumber { get; set; }
        public string Text { get; set; }
        public DateTime Timestamp { get; set; }
        public bool IsInbound { get; set; }
        
        // Confirmation tracking
        public string? ConfirmedBy { get; set; } // ApplicationUserId
        public DateTime? ConfirmedOn { get; set; }
        
        // Navigation property for the user who confirmed
        public ApplicationUser? ConfirmedByUser { get; set; }
    }

    // Webhook subscription information
    public class WebhookSubscription
    {
        public string Id { get; set; } = "";
        public string Status { get; set; } = "";
        public string? WebhookUrl { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime ExpirationTime { get; set; }
        public List<string> EventFilters { get; set; } = new();
        
        public bool IsActive => Status == "Active";
        public bool IsExpiringSoon => (ExpirationTime - DateTime.UtcNow).TotalHours < 24;
        public string StatusDisplay => IsActive ? "🟢 Active" : "🔴 Inactive";
    }
}
