@page "/webhook-test"
@using Surefire.Domain.Chat
@using Surefire.Domain.Chat.Services
@using Surefire.Domain.Logs
@using Surefire.Domain.Shared.Helpers
@using Microsoft.AspNetCore.SignalR.Client
@using System.Text.Json
@inject NavigationManager NavigationManager
@inject SmsMessageService SmsMessageService
@inject ILoggingService LoggingService

<h3>SMS Webhook Test Page</h3>

<div class="webhooktest mb-3">
    <h4>Test Webhook Flow</h4>
    <p>This page allows you to test the webhook flow from Gateway to ChopperMessaging.</p>
    
    <div class="row mb-3">
        <div class="col-md-6">
            <FluentCard>
                <div class="p-3">
                    <h5>Simulate Inbound SMS</h5>
                    <FluentStack Orientation="Orientation.Vertical" Gap="8">
                        <FluentTextField @bind-Value="phoneNumber" Placeholder="Phone Number (e.g., +15551234567)" />
                        <FluentTextArea @bind-Value="messageText" Placeholder="Message Text" Rows="3" />
                        <FluentButton Appearance="Appearance.Accent" OnClick="SimulateInboundSms" Loading="@isProcessing">
                            Send Test Message
                        </FluentButton>
                    </FluentStack>
                </div>
            </FluentCard>
        </div>
        
        <div class="col-md-6">
            <FluentCard>
                <div class="p-3">
                    <h5>Connection Status</h5>
                    <div class="d-flex align-items-center mb-2">
                        <div class="status-indicator @(isConnected ? "connected" : "disconnected")"></div>
                        <span>SignalR: @(isConnected ? "Connected" : "Disconnected")</span>
                    </div>
                    <FluentButton Appearance="Appearance.Lightweight" OnClick="ConnectToSignalR" Disabled="@isConnected">
                        Connect
                    </FluentButton>
                    <FluentButton Appearance="Appearance.Lightweight" OnClick="DisconnectFromSignalR" Disabled="@(!isConnected)">
                        Disconnect
                    </FluentButton>
                </div>
            </FluentCard>
            
            <FluentCard Class="mt-3">
                <div class="p-3">
                    <h5>Received Messages</h5>
                    <div class="message-list">
                        @foreach (var message in receivedMessages)
                        {
                            <div class="message-item @(message.IsInbound ? "inbound" : "outbound")">
                                <div class="message-header">
                                    <span class="message-direction">@(message.IsInbound ? "Inbound" : "Outbound")</span>
                                    <span class="message-phone">@StringHelper.FormatPhoneNumber(message.PhoneNumber)</span>
                                    <span class="message-time">@message.Timestamp.ToString("g")</span>
                                </div>
                                <div class="message-content">
                                    @message.Text
                                </div>
                            </div>
                        }
                        
                        @if (!receivedMessages.Any())
                        {
                            <div class="no-messages">
                                No messages received yet. Connect to SignalR and send a test message.
                            </div>
                        }
                    </div>
                </div>
            </FluentCard>
        </div>
    </div>
    
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <FluentMessageBar Intent="@(isError ? MessageIntent.Error : MessageIntent.Success)" AllowDismiss="true" OnDismiss="() => resultMessage = string.Empty">
            @resultMessage
        </FluentMessageBar>
    }
    
    <h4 class="mt-4">Logs</h4>
    <FluentCard>
        <div class="p-3 logs-container">
            @foreach (var log in logs)
            {
                <div class="log-entry @GetLogLevelClass(log.LogLevel)">
                    <span class="log-timestamp">@log.Timestamp.ToLocalTime().ToString("HH:mm:ss")</span>
                    <span class="log-level">[@log.LogLevel]</span>
                    <span class="log-message">@log.Message</span>
                </div>
            }
            
            @if (!logs.Any())
            {
                <div class="no-logs">
                    No logs available. Actions will be logged here.
                </div>
            }
        </div>
    </FluentCard>
</div>

<style>
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .connected {
        background-color: #4caf50;
    }
    
    .disconnected {
        background-color: #f44336;
    }
    
    .message-list {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .message-item {
        padding: 8px;
        margin-bottom: 8px;
        border-radius: 4px;
    }
    
    .inbound {
        background-color: #e3f2fd;
    }
    
    .outbound {
        background-color: #e8f5e9;
    }
    
    .message-header {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        margin-bottom: 4px;
    }
    
    .message-direction {
        font-weight: bold;
    }
    
    .message-content {
        white-space: pre-wrap;
    }
    
    .no-messages, .no-logs {
        color: #757575;
        text-align: center;
        padding: 16px;
    }
    
    .logs-container {
        max-height: 300px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 0.9rem;
    }
    
    .log-entry {
        padding: 2px 4px;
        margin-bottom: 2px;
    }
    
    .log-timestamp {
        margin-right: 8px;
        color: #757575;
    }
    
    .log-level {
        margin-right: 8px;
        font-weight: bold;
    }
    
    .log-level-information {
        background-color: #e3f2fd;
    }
    
    .log-level-warning {
        background-color: #fff8e1;
    }
    
    .log-level-error {
        background-color: #ffebee;
    }
    
    .log-level-debug {
        background-color: #f5f5f5;
    }
    .webhooktest {
        height:80vh;
        overflow-y: scroll;
    }
</style>

@code {
    private string phoneNumber = "";
    private string messageText = "";
    private bool isProcessing = false;
    private bool isError = false;
    private string resultMessage = "";
    private bool isConnected = false;
    private HubConnection? hubConnection;
    private List<SmsMessage> receivedMessages = new();
    private List<Log> logs = new();
    
    protected override async Task OnInitializedAsync()
    {
        await LoadLogs();
        await ConnectToSignalR();
    }
    
    private async Task LoadLogs()
    {
        try
        {
            logs = await LoggingService.GetLogsForCurrentUserAsync();
            logs = logs.Where(l => l.Source == "InternalSmsWebhook" || l.Source == "SmsMessageService")
                .OrderByDescending(l => l.Timestamp)
                .Take(50)
                .ToList();
        }
        catch (Exception ex)
        {
            resultMessage = $"Error loading logs: {ex.Message}";
            isError = true;
        }
    }
    
    private async Task ConnectToSignalR()
    {
        try
        {
            hubConnection = new HubConnectionBuilder()
                .WithUrl(NavigationManager.ToAbsoluteUri("/messagingHub"))
                .WithAutomaticReconnect()
                .Build();
            
            hubConnection.On<SmsMessage>("ReceiveSmsMessage", (message) =>
            {
                receivedMessages.Insert(0, message);
                if (receivedMessages.Count > 10)
                {
                    receivedMessages.RemoveAt(receivedMessages.Count - 1);
                }
                
                InvokeAsync(StateHasChanged);
            });
            
            await hubConnection.StartAsync();
            isConnected = true;
        }
        catch (Exception ex)
        {
            resultMessage = $"Error connecting to SignalR: {ex.Message}";
            isError = true;
        }
    }
    
    private async Task DisconnectFromSignalR()
    {
        if (hubConnection != null)
        {
            await hubConnection.StopAsync();
            await hubConnection.DisposeAsync();
            hubConnection = null;
            isConnected = false;
        }
    }
    
    private async Task SimulateInboundSms()
    {
        if (string.IsNullOrWhiteSpace(phoneNumber) || string.IsNullOrWhiteSpace(messageText))
        {
            resultMessage = "Please enter both phone number and message text";
            isError = true;
            return;
        }
        
        isProcessing = true;
        resultMessage = "";
        
        try
        {
            // Create a test SMS message
            var message = new SmsMessage
            {
                Id = Guid.NewGuid().ToString(),
                PhoneNumber = StringHelper.NormalizePhoneNumber(phoneNumber),
                Text = messageText,
                Timestamp = DateTime.UtcNow,
                IsInbound = true
            };
            
            // Store in database
            var storedMessage = await SmsMessageService.StoreSmsMessageAsync(message);
            
            if (storedMessage != null)
            {
                resultMessage = "Test message sent successfully!";
                isError = false;
                
                // Broadcast via SignalR if connected
                if (hubConnection?.State == HubConnectionState.Connected)
                {
                    await hubConnection.SendAsync("BroadcastSmsMessage", message);
                }
                
                // Refresh logs
                await LoadLogs();
            }
            else
            {
                resultMessage = "Failed to store test message";
                isError = true;
            }
        }
        catch (Exception ex)
        {
            resultMessage = $"Error sending test message: {ex.Message}";
            isError = true;
        }
        finally
        {
            isProcessing = false;
        }
    }
    
    private string GetLogLevelClass(LogLevel logLevel)
    {
        return logLevel switch
        {
            LogLevel.Information => "log-level-information",
            LogLevel.Warning => "log-level-warning",
            LogLevel.Error => "log-level-error",
            LogLevel.Debug => "log-level-debug",
            _ => ""
        };
    }
    
    public async ValueTask DisposeAsync()
    {
        if (hubConnection != null)
        {
            await hubConnection.DisposeAsync();
        }
    }
} 