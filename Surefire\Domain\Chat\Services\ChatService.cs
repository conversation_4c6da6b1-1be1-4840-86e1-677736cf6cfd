﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Collections.Concurrent;
using Microsoft.Extensions.Configuration;
using System.Linq;
using System.IO;
//Collections.Concurrent removed - no longer using caching

namespace Surefire.Domain.Chat
{
    public partial class ChatService
    {
        private readonly HttpClient _httpClient;
        private readonly string _clientId;
        private readonly string _clientSecret;
        private readonly string _serverUrl;
        private readonly string _jwt;
        private string _accessToken;
        private DateTime _tokenExpiration = DateTime.MinValue;

        // Call log caching
        private static CallLogRecordFire[] _cachedCallLogs;
        private static DateTime _callLogsCacheExpiration = DateTime.MinValue;
        private static readonly TimeSpan _callLogsCacheTTL = TimeSpan.FromMinutes(5); // Cache for 5 minutes
        private static readonly object _callLogsCacheLock = new object();

        // Staff Chat in-memory storage
        private static readonly ConcurrentQueue<MessageItem> _staffChatMessages = new();
        private static readonly object _staffChatLock = new object();
        private static readonly int _maxStaffChatMessages = 1000; // Limit to prevent memory issues

        // SMS caching completely removed - using database-first approach

        public ChatService(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            
            // Get RingCentral credentials from environment variables
            _clientId = Environment.GetEnvironmentVariable("RINGCENTRAL_CLIENT_ID");
            _clientSecret = Environment.GetEnvironmentVariable("RINGCENTRAL_CLIENT_SECRET");
            _serverUrl = Environment.GetEnvironmentVariable("RINGCENTRAL_SERVER_URL");
            _jwt = Environment.GetEnvironmentVariable("RINGCENTRAL_JWT");
            
            if (string.IsNullOrEmpty(_clientId) || string.IsNullOrEmpty(_clientSecret) || 
                string.IsNullOrEmpty(_serverUrl) || string.IsNullOrEmpty(_jwt))
            {
                throw new InvalidOperationException("RingCentral credentials not found in environment variables");
            }
        }
        
        /// <summary>
        /// Authenticates with RingCentral API using JWT grant type
        /// </summary>
        private async Task AuthenticateAsync()
        {
            // If token is still valid, don't re-authenticate
            if (_accessToken != null && DateTime.UtcNow < _tokenExpiration)
            {
                return;
            }
            
            var content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("grant_type", "urn:ietf:params:oauth:grant-type:jwt-bearer"),
                new KeyValuePair<string, string>("assertion", _jwt)
            });
            
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_serverUrl}/restapi/oauth/token")
            {
                Content = content
            };
            
            // Add authorization header with Basic Auth (client_id:client_secret)
            var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{_clientId}:{_clientSecret}"));
            request.Headers.Authorization = new AuthenticationHeaderValue("Basic", credentials);
            
            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();
            
            var responseContent = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
            
            _accessToken = tokenResponse.access_token;
            _tokenExpiration = DateTime.UtcNow.AddSeconds(tokenResponse.expires_in - 60); // Subtract a minute for safety
        }

        // Staff Chat Methods
        /// <summary>
        /// Adds a message to the staff chat in-memory storage
        /// </summary>
        public void AddStaffChatMessage(MessageItem message)
        {
            lock (_staffChatLock)
            {
                _staffChatMessages.Enqueue(message);
                
                // Remove old messages if we exceed the limit
                while (_staffChatMessages.Count > _maxStaffChatMessages)
                {
                    _staffChatMessages.TryDequeue(out _);
                }
            }
        }

        /// <summary>
        /// Gets all staff chat messages
        /// </summary>
        public List<MessageItem> GetStaffChatMessages()
        {
            lock (_staffChatLock)
            {
                return _staffChatMessages.ToList();
            }
        }


        /// <summary>
        /// Clears all staff chat messages (for testing/admin purposes)
        /// </summary>
        public void ClearStaffChatMessages()
        {
            lock (_staffChatLock)
            {
                _staffChatMessages.Clear();
            }
        }


        // All SMS caching methods removed - using database-first approach via SmsMessageService

        // Webhook Management Methods
        /// <summary>
        /// Registers a webhook subscription with RingCentral for SMS messages
        /// </summary>
        public async Task<(bool Success, string Message)> RegisterSmsWebhookAsync(string webhookUrl)
        {
            try
            {
                await AuthenticateAsync();
                
                var webhookRequest = new
                {
                    eventFilters = new[]
                    {
                        "/restapi/v1.0/account/~/extension/~/message-store/instant?type=SMS"
                    },
                    deliveryMode = new
                    {
                        transportType = "WebHook",
                        address = webhookUrl
                    },
                    expiresIn = 604800 // 7 days
                };
                
                var json = JsonSerializer.Serialize(webhookRequest, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
                
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                
                var response = await _httpClient.PostAsync($"{_serverUrl}/restapi/v1.0/subscription", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var subscriptionResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                    
                    if (subscriptionResponse.TryGetProperty("id", out var idElement))
                    {
                        var subscriptionId = idElement.GetString();
                        return (true, $"Webhook registered successfully. Subscription ID: {subscriptionId}");
                    }
                    
                    return (true, "Webhook registered successfully.");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (false, $"Failed to register webhook: {response.StatusCode} - {errorContent}");
                }
            }
            catch (Exception ex)
            {
                return (false, $"Error registering webhook: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Gets all active webhook subscriptions
        /// </summary>
        public async Task<(bool Success, string Message, List<WebhookSubscription> Subscriptions)> GetWebhookSubscriptionsAsync()
        {
            try
            {
                await AuthenticateAsync();
                
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                
                var response = await _httpClient.GetAsync($"{_serverUrl}/restapi/v1.0/subscription");
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var subscriptionsResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                    
                    var subscriptions = new List<WebhookSubscription>();
                    
                    if (subscriptionsResponse.TryGetProperty("records", out var recordsElement) && 
                        recordsElement.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var record in recordsElement.EnumerateArray())
                        {
                            var subscription = new WebhookSubscription
                            {
                                Id = record.TryGetProperty("id", out var id) ? id.GetString() : "",
                                Status = record.TryGetProperty("status", out var status) ? status.GetString() : "",
                                CreationTime = DateTime.UtcNow,
                                ExpirationTime = DateTime.UtcNow.AddDays(7)
                            };
                            
                            if (record.TryGetProperty("creationTime", out var createTime) && 
                                DateTime.TryParse(createTime.GetString(), out var parsedCreateTime))
                            {
                                subscription.CreationTime = parsedCreateTime;
                            }
                            
                            if (record.TryGetProperty("expirationTime", out var expTime) && 
                                DateTime.TryParse(expTime.GetString(), out var parsedExpTime))
                            {
                                subscription.ExpirationTime = parsedExpTime;
                            }
                            
                            if (record.TryGetProperty("deliveryMode", out var deliveryMode) && 
                                deliveryMode.TryGetProperty("address", out var address))
                            {
                                subscription.WebhookUrl = address.GetString();
                            }
                            
                            if (record.TryGetProperty("eventFilters", out var eventFilters) && 
                                eventFilters.ValueKind == JsonValueKind.Array)
                            {
                                subscription.EventFilters = eventFilters.EnumerateArray()
                                    .Select(e => e.GetString())
                                    .Where(s => !string.IsNullOrEmpty(s))
                                    .ToList();
                            }
                            
                            subscriptions.Add(subscription);
                        }
                    }
                    
                    return (true, $"Found {subscriptions.Count} webhook subscriptions.", subscriptions);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (false, $"Failed to get subscriptions: {response.StatusCode} - {errorContent}", new List<WebhookSubscription>());
                }
            }
            catch (Exception ex)
            {
                return (false, $"Error getting subscriptions: {ex.Message}", new List<WebhookSubscription>());
            }
        }
        
        /// <summary>
        /// Deletes a webhook subscription
        /// </summary>
        public async Task<(bool Success, string Message)> DeleteWebhookSubscriptionAsync(string subscriptionId)
        {
            try
            {
                await AuthenticateAsync();
                
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                
                var response = await _httpClient.DeleteAsync($"{_serverUrl}/restapi/v1.0/subscription/{subscriptionId}");
                
                if (response.IsSuccessStatusCode)
                {
                    return (true, $"Webhook subscription {subscriptionId} deleted successfully.");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (false, $"Failed to delete subscription: {response.StatusCode} - {errorContent}");
                }
            }
            catch (Exception ex)
            {
                return (false, $"Error deleting subscription: {ex.Message}");
            }
        }

        // Method to check for new SMS messages and update unread counts
        public async Task<List<SmsMessage>> GetNewSmsMessagesSinceAsync(DateTime since)
        {
            try
            {
                await AuthenticateAsync();
                
                var allMessages = await GetAllSmsMessagesAsync();
                
                // Filter messages on our side to find ones newer than 'since'
                var newMessages = allMessages?.Messages?.Where(m => m.Timestamp > since).ToList() ?? new List<SmsMessage>();
                
                return newMessages;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting new SMS messages: {ex.Message}");
                return new List<SmsMessage>();
            }
        }

        // Method to process new SMS messages and update unread counts
        public async Task ProcessNewSmsMessagesAsync(DateTime since, Action<SmsMessage> onNewMessage)
        {
            try
            {
                var newMessages = await GetNewSmsMessagesSinceAsync(since);
                
                foreach (var message in newMessages)
                {                    
                    // Only process inbound messages for unread counts
                    if (message.IsInbound)
                    {
                        onNewMessage?.Invoke(message);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing new SMS messages: {ex.Message}");
            }
        }
    }
    
}
