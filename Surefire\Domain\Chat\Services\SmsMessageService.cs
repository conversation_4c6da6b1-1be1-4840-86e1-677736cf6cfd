using Microsoft.EntityFrameworkCore;
using Surefire.Data;
using Surefire.Domain.Chat;
using Surefire.Domain.Shared.Services;
using Surefire.Domain.Shared.Helpers;
using Surefire.Domain.Logs;

namespace Surefire.Domain.Chat.Services
{
    public class SmsMessageService
    {
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly StateService _stateService;
        private readonly ILoggingService _loggingService;

        public SmsMessageService(
            IDbContextFactory<ApplicationDbContext> dbContextFactory, 
            StateService stateService,
            ILoggingService loggingService)
        {
            _dbContextFactory = dbContextFactory;
            _stateService = stateService;
            _loggingService = loggingService;
        }

        /// <summary>
        /// Stores an SMS message in the database
        /// </summary>
        public async Task<SmsMessageEntity?> StoreSmsMessageAsync(SmsMessage message)
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                
                // Check if message already exists
                var existingMessage = await context.SmsMessages
                    .FirstOrDefaultAsync(m => m.RingCentralId == message.Id);

                if (existingMessage != null)
                {
                    await _loggingService.LogAsync(
                        LogLevel.Debug, 
                        $"SMS message already exists: ID={message.Id}, Phone={message.PhoneNumber}", 
                        "SmsMessageService");
                    return existingMessage;
                }

                // Create new message entity with normalized phone number
                var messageEntity = new SmsMessageEntity
                {
                    RingCentralId = message.Id,
                    PhoneNumber = StringHelper.NormalizePhoneNumber(message.PhoneNumber),
                    Text = message.Text,
                    Timestamp = message.Timestamp,
                    IsInbound = message.IsInbound,
                    ConfirmedBy = null,
                    ConfirmedOn = null
                };

                context.SmsMessages.Add(messageEntity);
                await context.SaveChangesAsync();

                await _loggingService.LogAsync(
                    LogLevel.Information, 
                    $"Stored new SMS message: ID={message.Id}, Phone={messageEntity.PhoneNumber}, Inbound={message.IsInbound}", 
                    "SmsMessageService");

                // DATABASE-FIRST: Refresh StateService counts if this is a new inbound message
                if (message.IsInbound)
                {
                    await RefreshStateServiceUnreadCountsAsync();
                }

                return messageEntity;
            }
            catch (Exception ex)
            {
                string errorMessage = $"Error storing SMS message: {ex.Message}";
                Console.WriteLine(errorMessage);
                await _loggingService.LogAsync(LogLevel.Error, errorMessage, "SmsMessageService", ex);
                return null;
            }
        }

        /// <summary>
        /// Confirms an SMS message by setting the confirmed user and timestamp
        /// </summary>
        public async Task<bool> ConfirmSmsMessageAsync(string ringCentralId, string userId)
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                
                var message = await context.SmsMessages
                    .Include(m => m.ConfirmedByUser)
                    .FirstOrDefaultAsync(m => m.RingCentralId == ringCentralId);

                if (message == null)
                {
                    string errorMessage = $"Message with RingCentralId {ringCentralId} not found";
                    Console.WriteLine(errorMessage);
                    await _loggingService.LogAsync(LogLevel.Warning, errorMessage, "SmsMessageService");
                    return false;
                }

                // Check if already confirmed
                if (message.ConfirmedBy != null)
                {
                    string logMessage = $"Message {ringCentralId} is already confirmed by {message.ConfirmedBy}";
                    Console.WriteLine(logMessage);
                    await _loggingService.LogAsync(LogLevel.Information, logMessage, "SmsMessageService");
                    return true; // Return true since it's already confirmed
                }

                message.ConfirmedBy = userId;
                message.ConfirmedOn = DateTime.UtcNow;

                await context.SaveChangesAsync();

                // DATABASE-FIRST: Immediately update StateService counts from database
                await RefreshStateServiceUnreadCountsAsync();

                string successMessage = $"Successfully confirmed message {ringCentralId} by user {userId}";
                Console.WriteLine(successMessage);
                await _loggingService.LogAsync(LogLevel.Information, successMessage, "SmsMessageService");
                return true;
            }
            catch (Exception ex)
            {
                string errorMessage = $"Error confirming SMS message: {ex.Message}";
                Console.WriteLine(errorMessage);
                await _loggingService.LogAsync(LogLevel.Error, errorMessage, "SmsMessageService", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets the count of unconfirmed SMS messages for a specific phone number
        /// </summary>
        public async Task<int> GetUnconfirmedCountAsync(string phoneNumber)
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                var normalizedPhone = StringHelper.NormalizePhoneNumber(phoneNumber);
                var count = await context.SmsMessages
                    .CountAsync(m => m.PhoneNumber == normalizedPhone && m.IsInbound && m.ConfirmedBy == null);
                
                await _loggingService.LogAsync(
                    LogLevel.Debug, 
                    $"Unconfirmed count for {normalizedPhone}: {count}", 
                    "SmsMessageService");
                
                return count;
            }
            catch (Exception ex)
            {
                string errorMessage = $"Error getting unconfirmed count: {ex.Message}";
                Console.WriteLine(errorMessage);
                await _loggingService.LogAsync(LogLevel.Error, errorMessage, "SmsMessageService", ex);
                return 0;
            }
        }

        /// <summary>
        /// Gets all phone numbers with unconfirmed messages and their counts
        /// </summary>
        public async Task<Dictionary<string, int>> GetUnconfirmedCountsByPhoneAsync()
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                var results = await context.SmsMessages
                    .Where(m => m.IsInbound && m.ConfirmedBy == null)
                    .GroupBy(m => m.PhoneNumber)
                    .Select(g => new { PhoneNumber = g.Key, Count = g.Count() })
                    .ToListAsync();

                var countDict = results.ToDictionary(r => r.PhoneNumber, r => r.Count);
                
                await _loggingService.LogAsync(
                    LogLevel.Debug, 
                    $"Retrieved unconfirmed counts for {countDict.Count} phone numbers", 
                    "SmsMessageService");
                
                return countDict;
            }
            catch (Exception ex)
            {
                string errorMessage = $"Error getting unconfirmed counts by phone: {ex.Message}";
                Console.WriteLine(errorMessage);
                await _loggingService.LogAsync(LogLevel.Error, errorMessage, "SmsMessageService", ex);
                return new Dictionary<string, int>();
            }
        }
        
        /// <summary>
        /// Refreshes StateService unread counts directly from database (DATABASE-FIRST approach)
        /// </summary>
        public async Task RefreshStateServiceUnreadCountsAsync()
        {
            try
            {
                var unconfirmedCounts = await GetUnconfirmedCountsByPhoneAsync();
                
                // Update StateService with current unconfirmed counts from database
                _stateService.UpdateUnconfirmedSmsCounts(unconfirmedCounts);
                
                string logMessage = $"Refreshed StateService with {unconfirmedCounts.Count} phone numbers having unconfirmed messages";
                Console.WriteLine($"SmsMessageService: {logMessage}");
                await _loggingService.LogAsync(LogLevel.Information, logMessage, "SmsMessageService");
            }
            catch (Exception ex)
            {
                string errorMessage = $"Error refreshing StateService unread counts: {ex.Message}";
                Console.WriteLine(errorMessage);
                await _loggingService.LogAsync(LogLevel.Error, errorMessage, "SmsMessageService", ex);
            }
        }

        /// <summary>
        /// Gets SMS messages for a conversation with confirmation status
        /// </summary>
        public async Task<List<SmsMessageEntity>> GetConversationMessagesAsync(string phoneNumber)
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                var normalizedPhone = StringHelper.NormalizePhoneNumber(phoneNumber);
                var messages = await context.SmsMessages
                    .Include(m => m.ConfirmedByUser)
                    .Where(m => m.PhoneNumber == normalizedPhone)
                    .OrderBy(m => m.Timestamp)
                    .ToListAsync();

                // Ensure ConfirmedByUser is loaded for all confirmed messages
                await EnsureConfirmedByUserLoadedAsync(messages);
                
                await _loggingService.LogAsync(
                    LogLevel.Debug, 
                    $"Retrieved {messages.Count} messages for conversation with {normalizedPhone}", 
                    "SmsMessageService");
                
                return messages;
            }
            catch (Exception ex)
            {
                string errorMessage = $"Error getting conversation messages: {ex.Message}";
                Console.WriteLine(errorMessage);
                await _loggingService.LogAsync(LogLevel.Error, errorMessage, "SmsMessageService", ex);
                return new List<SmsMessageEntity>();
            }
        }

        /// <summary>
        /// Ensures that ConfirmedByUser is properly loaded for all confirmed messages
        /// </summary>
        private async Task EnsureConfirmedByUserLoadedAsync(List<SmsMessageEntity> messages)
        {
            try
            {
                // Get all unique user IDs that need to be loaded
                var userIdsToLoad = messages
                    .Where(m => m.ConfirmedBy != null && m.ConfirmedByUser == null)
                    .Select(m => m.ConfirmedBy)
                    .Distinct()
                    .ToList();

                if (!userIdsToLoad.Any())
                    return;

                // Load all users in a single query
                using var context = await _dbContextFactory.CreateDbContextAsync();
                var users = await context.Users
                    .Where(u => userIdsToLoad.Contains(u.Id))
                    .ToDictionaryAsync(u => u.Id, u => u);

                // Assign users to messages
                foreach (var message in messages.Where(m => m.ConfirmedBy != null && m.ConfirmedByUser == null))
                {
                    if (users.TryGetValue(message.ConfirmedBy, out var user))
                    {
                        message.ConfirmedByUser = user;
                    }
                }
                
                await _loggingService.LogAsync(
                    LogLevel.Debug, 
                    $"Loaded user data for {userIdsToLoad.Count} confirmed messages", 
                    "SmsMessageService");
            }
            catch (Exception ex)
            {
                string errorMessage = $"Error loading confirmed by user data: {ex.Message}";
                Console.WriteLine(errorMessage);
                await _loggingService.LogAsync(LogLevel.Error, errorMessage, "SmsMessageService", ex);
            }
        }
    }
} 