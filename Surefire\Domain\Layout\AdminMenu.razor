@namespace Surefire.Components.Layout
@using Microsoft.FluentUI.AspNetCore.Components
@inject NavigationManager NavigationManager

<div class="admin-menu">
    <h5>Admin Tools</h5>
    <div class="admin-links">
        <FluentNavLink Href="webhook-test" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.WebAsset())" IconColor="Color.Accent">
            SMS Webhook Test
        </FluentNavLink>
        
        <!-- Add more admin links here -->
    </div>
</div>

<style>
    .admin-menu {
        padding: 16px;
        background-color: #f9f9f9;
        border-radius: 4px;
        margin-bottom: 16px;
    }
    
    .admin-menu h5 {
        margin-top: 0;
        margin-bottom: 12px;
        color: #555;
    }
    
    .admin-links {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
</style> 