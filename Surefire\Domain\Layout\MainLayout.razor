@namespace Surefire.Components.Layout
@inherits LayoutComponentBase
@using Surefire.Data;
@using Surefire.Domain.Ember
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Contacts.Models
@using Surefire.Components.Layout
@using Surefire.Domain.Chat.Components
@using Surefire.Domain.Agents.Interfaces
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.SignalR.Client
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.FluentUI.AspNetCore.Components
@using System.Security.Claims;

@inject AuthenticationStateProvider AuthStateProvider
@inject UserManager<ApplicationUser> UserManager
@inject NavigationManager NavigationManager
@inject StateService _stateService
@inject EmberService EmberService
@inject HomeService HomeService
@inject IToastService ToastService
@inject IJSRuntime JSRuntime
@inject IVoiceRecordingService VoiceRecordingService
@inject ILogger<MainLayout> Logger

<CascadingValue Value="_stateService">
    <div id="sf-top-bar" class="top-bar">
        <div class="left-icons">
            <div class="surefire-main-logo @(isRecording ? "recording" : "")"
                 @onmousedown="StartPushToTalk"
                 @onmouseup="StopPushToTalk"
                 @onmouseleave="StopPushToTalk"
                 @onkeydown="HandleKeyDown"
                 @onkeydown:preventDefault="true"
                 @onkeyup="HandleKeyUp"
                 @onkeyup:preventDefault="true"
                 @oncontextmenu:preventDefault="true"
                 title="@(isRecording ? "Recording... Release to send (or press and hold Space)" : "Hold to record voice message (or press and hold Space)")"
                 tabindex="0">
                <img src="/img/home/<USER>/sflogo_00000.png" alt="App Logo" class="app-logo" />
            </div>
        </div>

        <_searchbar />

        <div class="right-icons">
            <AuthorizeView>
                <Authorized>
                    <FluentStack HorizontalAlignment="@HorizontalAlignment.End" VerticalAlignment="@VerticalAlignment.Center" Style="height: 48px; position:relative; left:-20px;">
                        <div class="chat-btn-container">
                            <span class="chat-btn" @onclick="ToggleChopper"><FluentIcon Value="@(new Icons.Regular.Size28.Chat())" Color="Color.Custom" CustomColor="#fff" /></span>
                            @if ((unreadStaffMessageCount + unreadSmsMessageCount) > 0)
                            {
                                <FluentCounterBadge Count="@(unreadStaffMessageCount + unreadSmsMessageCount)"
                                                    Color="Color.Fill"
                                                    BackgroundColor="Color.Error"
                                                    Class="unread-counter" />
                            }
                        </div>
                        <_profilemenu UserId="@UserId" />
                    </FluentStack>
                </Authorized>
                <NotAuthorized>
                    <a href="/Account/Manage" class="e-icon-btn e-icons">
                        <FluentIcon Value="@(new Icons.Regular.Size24.Person())" />
                    </a>
                </NotAuthorized>
            </AuthorizeView>
        </div>
    </div>

    <div class="page">
        <NavMenu />
        <main>
            <article>
                @if (_stateService.IsInitialized)
                {
                    @if (IsAdmin)
                    {
                        <AdminMenu />
                    }
                    @Body
                }
                else
                {
                    <div class="emptyLoader">
                        <svg id="Layer_1" data-name="Layer 1" class="rotate-scale-down-hor" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 300 299.65">
                            <defs>
                                <style>
                                    .cls-1 {
                                        fill: url(#linear-gradient);
                                        stroke-width: 0px;
                                    }
                                </style>
                                <linearGradient id="linear-gradient" x1="0" y1="149.83" x2="300" y2="149.83" gradientUnits="userSpaceOnUse">
                                    <stop offset="0" stop-color="#f89520" />
                                    <stop offset="1" stop-color="#eb1f27" />
                                </linearGradient>
                            </defs>
                            <path class="cls-1" d="m224.93,107.35c-8.74-3.66-17.76-7.23-25.48-12.55,0-.03,0-.05,0-.05-6.22-4.3-11.57-10.64-15.56-17.14-3.52-5.74-3.87-13.17,2.13-18.33,5.74-4.93,11.19-7.69,19.23-2.03,8.03,5.65,6.89,23.92,26.65,37.01,15.03,7.51,34.87,19.01,68.09-11.28-51.57,24.04-10.74-35.47-35.39-41.14-24.64-5.67-41.99-6.57-75.23-26.82-5.67-3.46-11.59-6.69-17.74-9.17C162.42,2.13,152.95.22,143.73.02c-20.22-.47-39.2,7.26-51.54,22.09.03,0,.07-.01.1-.02,0,0-.01.01-.02.02,0,0,.03-.01.07-.03,22.94-4.33,45.39-6.81,62,14.24,6.85,8.68,7.99,21.09,3.54,30.11-2.66-17-11.32-28.68-28.5-30.38-9.52-.94-19.74.16-28.99,2.72-14.63,4.05-28.54,10.62-43.06,15.15-12.38,3.87-25.25,7.18-37.11-3.07,1.87,16.73,9.87,25.28,25.55,25.44,17.42.18,34.86-1.71,52.29-1.62,12.27.06,19.8,6.43,22.83,16.7,2.69,9.11-.96,19.81-8.67,25.42-8.3,6.04-20.76,6.26-29.64.49-2.41-1.57-4.74-3.25-7.84-5.39,3.85,19.99,10.2,26.83,28.38,29.71,7.41,1.17,15.12.87,22.67.6,18.37-.66,36.86-3.52,55.07-2.21,39.27,2.83,65.18,36.63,59.62,74.12-.13,0-.21-.01-.21-.01,0,0-.46,3.55-2.71,8.64-.52-5.33-.96-10.67-1.71-15.97-3.56-25.2-18.77-41.15-41.54-50.22-22.27-8.87-60.95-2.38-75.39,28.11.35-.74,6.54-1.4,7.35-1.56,5.33-1.06,10.75-1.89,16.18-2.02,11.47-.26,21.42,4.01,16.69,17.09-8.54,26.23-35,29.78-58.74,14.97-19.07-11.9-33.1-18.51-45.52-43.34,2.83,32.23,33.2,56.82,46.6,65.33,15.24,9.8,64.8,26.49,84.06,10.06,8.51-6.28,13.95-12.5,17.19-18.48-1.61,18.2-16.1,51.14-82.09,31.76-35.93-12.41-44.43-21.78-61.1-38.17-15.77-15.5-27.02-16.42-39.34-16.28-8.62.1-16.72,5.88-20.2,8.49,5.26-2.03,19.54,1.39,28.53,8.28,8.99,6.89,18.51,36.59,38.11,50.52,28.42,20.36,40.67,28.31,86.08,28.31s69.65-15.24,79.56-21.22c5.02-2.62,9.61-5.41,13.44-8.39,53.14-41.32,51.2-132.49-20.79-162.69Z" />
                        </svg>
                    </div>
                }

            </article>
            <chopper class="chopper @chopperClass">
                <ChopperMessaging OnMinimize="ToggleChopper" />
            </chopper>
        </main>
    </div>
</CascadingValue>
<div id="blazor-error-ui">An unhandled error has occurred.<a href="" class="reload">Reload</a><a class="dismiss">🗙</a></div>
<_statusbar />
<FluentToastProvider MaxToastCount="10" RemoveToastsOnNavigation="false" Position="ToastPosition.BottomRight" />
<FluentDialogProvider />
<FluentTooltipProvider />
<FluentMessageBarProvider />
<FluentMenuProvider />
<FluentKeyCodeProvider />

<!-- Staff Message Reminder Audio -->
<audio id="staffMessageReminderAudio" preload="auto">
    <source src="/img/StaffMessageReminder.mp3" type="audio/mpeg">
    Your browser does not support the audio element.
</audio>

@code {
    [CascadingParameter] private Task<AuthenticationState> authenticationStateTask { get; set; }

    public string UserId { get; set; }
    public int CallerClientId { get; set; }
    public int CallerContactId { get; set; }
    private bool isUserInitialized = false;
    private bool isInitialized = false;
    private bool isLoading = true;
    private bool chopperExpanded = false;
    private bool componentInitialized = false;
    private bool isRecording = false;
    private bool isProcessingAudio = false;
    private string headerText = "Home";
    private string chopperClass = "chopper-expand-False";
    private ClaimsPrincipal User { get; set; }
    private HubConnection? hubConnection;
    private CancellationTokenSource? currentProcessingCancellation;
    private int unreadStaffMessageCount = 0;
    private int unreadSmsMessageCount = 0;
    private bool isDisposed = false;
    private bool IsAdmin = true; // For simplicity, showing admin menu to everyone for testing

    // Staff message reminder properties
    private Timer? staffMessageReminderTimer;
    private readonly TimeSpan reminderInterval = TimeSpan.FromMinutes(5);

    protected override async Task OnInitializedAsync()
    {
        _ = Task.Run(async () =>
        {
            if (!_stateService.IsInitialized)
            {
                await _stateService.InitializeStateAsync(AuthStateProvider.GetAuthenticationStateAsync()).ConfigureAwait(false);
            }

            // Initialize and start SignalR outside UI sync context
            hubConnection = new HubConnectionBuilder()
                .WithUrl(NavigationManager.ToAbsoluteUri("/notificationHub"))
                .Build();

            await hubConnection.StartAsync().ConfigureAwait(false);

            hubConnection.On<CallInfo>("ReceiveCallNotification", callInfo =>
            {
                // Hop back onto UI thread via InvokeAsync
                _ = InvokeAsync(() => ShowIncomingCallToast(callInfo));
            });

            await InvokeAsync(() =>
            {
                isInitialized = true;
                isLoading = false;

                // Subscribe to unread message count changes
                _stateService.OnUnreadStaffMessageCountChanged += OnUnreadStaffMessageCountChanged;
                _stateService.OnUnreadSmsMessageCountChanged += OnUnreadSmsMessageCountChanged;
                unreadStaffMessageCount = _stateService.UnreadStaffMessageCount;
                unreadSmsMessageCount = _stateService.UnreadSmsMessageCount;

                // Start the staff message reminder timer
                StartStaffMessageReminderTimer();

                StateHasChanged();
            });
        });
    }
    protected override async Task OnAfterRenderAsync(bool firstRender) { if (firstRender) { await JSRuntime.InvokeVoidAsync("initializeTopBarAnimations"); } } // Initialize top bar animations once the component is rendered
    protected override void OnParametersSet()
    {
        base.OnParametersSet();
    }

    private void ToggleChopper()
    {
        chopperExpanded = !chopperExpanded;
        chopperClass = "chopper-expand-" + chopperExpanded;

        // Update chopper visibility in StateService
        _stateService.SetChopperVisibility(chopperExpanded);

        // Inform chopper component only; avoid full layout re-render
        if (chopperExpanded && !componentInitialized)
        {
            componentInitialized = true;
        }
    }

    private void OnUnreadStaffMessageCountChanged()
    {
        if (isDisposed) return;

        unreadStaffMessageCount = _stateService.UnreadStaffMessageCount;
        InvokeAsync(() =>
        {
            if (!isDisposed)
            {
                StateHasChanged();
            }
        });
    }

    private void OnUnreadSmsMessageCountChanged()
    {
        if (isDisposed) return;

        unreadSmsMessageCount = _stateService.UnreadSmsMessageCount;
        InvokeAsync(() =>
        {
            if (!isDisposed)
            {
                StateHasChanged();
            }
        });
    }

    private void StartStaffMessageReminderTimer()
    {
        // Create timer that checks every 5 minutes for playing the reminder audio alert
        staffMessageReminderTimer = new Timer(async _ => await CheckAndPlayStaffMessageReminder(), null, reminderInterval, reminderInterval);
    }

    private async Task CheckAndPlayStaffMessageReminder()
    {
        if (isDisposed) return;

        try
        {
            // Check if there are unread staff messages
            if (_stateService.UnreadStaffMessageCount > 0)
            {
                await InvokeAsync(async () =>
                {
                    if (!isDisposed)
                    {
                        await PlayStaffMessageReminderSound();
                    }
                });
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "[StaffMessageReminder] Error checking and playing reminder");
        }
    }

    private async Task PlayStaffMessageReminderSound()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("playStaffMessageReminder");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "[StaffMessageReminder] Error playing reminder sound");
        }
    }
    private void ClickedPrimary(ToastResult result)
    {
        Navigation.NavigateTo($"/Clients/{CallerClientId}");
    }
    public async Task StartMainLogoLoadingAnimation()
    {
        await JSRuntime.InvokeVoidAsync("startLogoLoading");
    }
    public async Task StopMainLogoLoadingAnimation()
    {
        await JSRuntime.InvokeVoidAsync("stopLogoLoading");
    }
    public async Task ShowIncomingCallToast(CallInfo callInfo)
    {
        try
        {
            // Extract caller ID and name from callInfo
            string callerId = callInfo.CallerId;
            var callerName = callInfo.CallerName;

            // Skip internal extension calls (numbers with fewer than 7 digits)
            if (!string.IsNullOrEmpty(callerId) && callerId.Count(c => char.IsDigit(c)) < 7)
            {
                return;
            }

            // Search for matches in the database and initialize notification parameters
            var matches = await HomeService.GetCallerInfo(callInfo);
            string title = "Incoming Call";
            string message = $"From: {callerName}";
            string clientId = null; // Default to null if no client match

            if (matches == null)
            {
                ToastService.ShowInfo($"Incoming Call from {callerName} ({callerId}) - No matches found in database.", 20000);
            }
            else
            {
                // Common variable for matched client
                var matchedClient = matches.MatchedClient;

                // If we have a matched contact, use their client info
                if (matches.MatchedContact != null)
                {
                    CallerContactId = matches.MatchedContact.ContactId;
                    CallerClientId = matches.MatchedContact.Client.ClientId;
                    matchedClient = matches.MatchedContact.Client;

                    // Set contact-specific details
                    title = $"Incoming Call from {matchedClient.Name}";
                    message = $"CONTACT: {matches.MatchedContact.FirstName} {matches.MatchedContact.LastName}";
                    clientId = CallerClientId.ToString();

                    // Display success toast with contact information
                    ToastService.ShowCommunicationToast(new ToastParameters<CommunicationToastContent>()
                    {
                        Intent = ToastIntent.Success,
                        Title = title,
                        Timeout = 30000,
                        PrimaryAction = "Open Client Screen",
                        OnPrimaryAction = EventCallback.Factory.Create<ToastResult>(this, ClickedPrimary),
                        Content = new CommunicationToastContent()
                        {
                            Subtitle = message,
                            Details = $"{callerName} ({callerId})",
                        },
                    });
                }
                // If we just have a client match without contact
                else if (matchedClient != null)
                {
                    CallerClientId = matchedClient.ClientId;

                    // Set client-specific details
                    title = $"Incoming Call from {matchedClient.Name}";
                    message = $"Call ID: {callerName}";
                    clientId = CallerClientId.ToString();

                    // Display success toast with client information
                    ToastService.ShowCommunicationToast(new ToastParameters<CommunicationToastContent>()
                    {
                        Intent = ToastIntent.Success,
                        Title = title,
                        Timeout = 30000,
                        PrimaryAction = "Open Client Screen",
                        OnPrimaryAction = EventCallback.Factory.Create<ToastResult>(this, ClickedPrimary),
                        Content = new CommunicationToastContent()
                        {
                            Subtitle = message,
                            Details = $"Call Number: {callerId}",
                        },
                    });
                }
            }
            var notificationParams = new List<string> { title, message, clientId };
            await EmberService.RunEmberFunction("ShowTrayNotification", notificationParams);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.StackTrace);
        }
    }
    public async ValueTask DisposeAsync()
    {
        // Set disposed flag first to prevent any new operations
        isDisposed = true;

        // Unsubscribe from events
        _stateService.OnUnreadStaffMessageCountChanged -= OnUnreadStaffMessageCountChanged;
        _stateService.OnUnreadSmsMessageCountChanged -= OnUnreadSmsMessageCountChanged;

        // Dispose of the staff message reminder timer
        staffMessageReminderTimer?.Dispose();

        // Cancel any ongoing audio processing
        if (currentProcessingCancellation != null)
        {
            currentProcessingCancellation.Cancel();
            currentProcessingCancellation.Dispose();
        }

        if (hubConnection != null)
        {
            await hubConnection.DisposeAsync();
        }
    }

    private async Task StartPushToTalk()
    {
        try
        {
            // Prevent starting new recording if already recording or processing
            if (isRecording || isProcessingAudio) return;

            // Cancel any ongoing processing from previous recording
            if (currentProcessingCancellation != null)
            {
                currentProcessingCancellation.Cancel();
                currentProcessingCancellation.Dispose();
                currentProcessingCancellation = null;
            }

            // Clear any pending transcriptions from previous recordings
            await JSRuntime.InvokeVoidAsync("clearPendingVoiceTranscriptions");

            isRecording = true;
            StateHasChanged();

            // Start the logo spinning animation
            await JSRuntime.InvokeVoidAsync("startPushToTalkRecording");

            Logger.LogInformation("[PushToTalk] Recording started");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "[PushToTalk] Error starting recording");
            isRecording = false;
            StateHasChanged();
        }
    }

    private async Task StopPushToTalk()
    {
        try
        {
            if (!isRecording) return;

            isRecording = false;
            StateHasChanged();

            Logger.LogInformation("[PushToTalk] Recording stopped, processing audio...");

            // Stop recording and get audio data
            var base64Audio = await JSRuntime.InvokeAsync<string>("stopPushToTalkRecording");

            if (!string.IsNullOrWhiteSpace(base64Audio))
            {
                // Navigate to Enhanced AI Chat first
                NavigationManager.NavigateTo("/agents");

                // Process the audio in the background with cancellation support
                isProcessingAudio = true;
                currentProcessingCancellation = new CancellationTokenSource();
                _ = ProcessAudioInBackground(base64Audio, currentProcessingCancellation.Token);
            }
            else
            {
                Logger.LogWarning("[PushToTalk] No audio data received");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "[PushToTalk] Error stopping recording");
            isRecording = false;
            StateHasChanged();
        }
    }

    private async Task HandleKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == " " || e.Key == "Spacebar") // Space key
        {
            await StartPushToTalk();
        }
    }

    private async Task HandleKeyUp(KeyboardEventArgs e)
    {
        if (e.Key == " " || e.Key == "Spacebar") // Space key
        {
            await StopPushToTalk();
        }
    }

    private async Task ProcessAudioInBackground(string base64Audio, CancellationToken cancellationToken)
    {
        try
        {
            // Convert base64 to byte array
            cancellationToken.ThrowIfCancellationRequested();
            var audioData = Convert.FromBase64String(base64Audio);
            cancellationToken.ThrowIfCancellationRequested();

            // Transcribe the audio
            var transcription = await VoiceRecordingService.TranscribeAudioAsync(audioData);
            cancellationToken.ThrowIfCancellationRequested();

            // Send the transcription to the Enhanced AI Chat page
            if (!string.IsNullOrWhiteSpace(transcription) &&
                !transcription.StartsWith("Audio validation failed") &&
                !transcription.StartsWith("Sorry, I couldn't process"))
            {
                await JSRuntime.InvokeVoidAsync("sendTranscriptionToChat", transcription);
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("showTranscriptionError",
                    transcription.StartsWith("Audio validation failed") || transcription.StartsWith("Sorry, I couldn't process")
                        ? transcription
                        : "No speech detected. Please try speaking more clearly.");
            }
        }
        catch (OperationCanceledException)
        {
            Logger.LogInformation("[PushToTalk] Audio processing cancelled");
        }
        catch (Exception ex)
        {
            if (!cancellationToken.IsCancellationRequested)
            {
                Logger.LogError(ex, "[PushToTalk] Error processing transcription");
                await JSRuntime.InvokeVoidAsync("showTranscriptionError", "Error processing voice recording. Please try again.");
            }
        }
        finally
        {
            isProcessingAudio = false;
            if (currentProcessingCancellation != null)
            {
                currentProcessingCancellation.Dispose();
                currentProcessingCancellation = null;
            }
            StateHasChanged();
        }
    }
}