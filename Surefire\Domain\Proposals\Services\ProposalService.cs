﻿using iText.Kernel.Pdf;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Surefire.Data;
using Surefire.Domain.Logs;
using Surefire.Domain.Shared.Services;
using System.Net.Http.Headers;

namespace Surefire.Domain.Proposals
{
    public class ProposalService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly ILoggingService _log;
        private readonly StateService _stateService;
        protected readonly string _formRecognizerEndpoint;
        protected readonly string _formRecognizerKey;
        protected readonly string _openAiApiKey;
        protected readonly HttpClient _httpClient;

        public ProposalService(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            IHttpContextAccessor httpContextAccessor,
            ILoggingService log,
            IDbContextFactory<ApplicationDbContext> dbContextFactory,
            StateService stateService)
        {
            _context = context;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
            _dbContextFactory = dbContextFactory;
            _log = log;
            _stateService = stateService;

            _formRecognizerEndpoint = Environment.GetEnvironmentVariable("AZURE_FORM_RECOGNIZER_ENDPOINT")
                ?? throw new Exception("Missing AZURE_FORM_RECOGNIZER_ENDPOINT");
            _formRecognizerKey = Environment.GetEnvironmentVariable("AZURE_FORM_RECOGNIZER_KEY")
                ?? throw new Exception("Missing AZURE_FORM_RECOGNIZER_KEY");
            _openAiApiKey = Environment.GetEnvironmentVariable("OPENAI")
                ?? throw new Exception("Missing OPENAI");

            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _openAiApiKey);
            
            // Initialize the ExtractorService
            InitializeExtractorService();
        }

        /// <summary>
        /// Returns all proposals for a given client.
        /// </summary>
        public async Task<List<Proposal>> GetProposalsForClientAsync(int clientId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.Proposals
                .Where(p => p.ClientId == clientId)
                .OrderByDescending(p => p.DateCreated)
                .ToListAsync();
        }

        /// <summary>
        /// Returns a filtered list of proposals for the homepage display.
        /// Shows all proposals with status 0-2, status 10 (renewal mailouts), and status 3 proposals from the last 15 days.
        /// </summary>
        public async Task<List<Proposal>> GetProposalHomepageListAsync()
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            var cutoffDate = DateTime.UtcNow.AddDays(-15);
            
            return await context.Proposals
                .Include(p => p.Renewal)
                    .ThenInclude(r => r.Client)
                .Include(p => p.CreatedBy)
                .Include(p => p.Policy)
                    .ThenInclude(policy => policy.Product)
                .Where(p => 
                    // Always show status 0-2
                    p.Status <= 2 ||
                    // Always show status 10 (renewal mailouts)
                    p.Status == 10 ||
                    // Show status 3 only if within last 15 days
                    (p.Status == 3 && p.DateCreated >= cutoffDate)
                )
                .OrderByDescending(p => p.DateCreated)
                .ToListAsync();
        }

        /// <summary>
        /// Creates a new proposal for the specified client and returns its ID.
        /// </summary>
        public async Task<int> CreateProposalForClientAsync(int clientId)
        {
            await _log.LogAsync(LogLevel.Information, $"Creating new proposal for client: {clientId}", "ProposalService");
            
            // Get the current user
            var userId = _userManager.GetUserId(_httpContextAccessor.HttpContext.User);
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("No user is currently logged in");
            }

            using var context = await _dbContextFactory.CreateDbContextAsync();
            var proposal = new Proposal
            {
                ClientId = clientId,
                RawJson = "{}",
                ApprovedJson = "{}",
                DateCreated = DateTime.UtcNow,
                DateModified = DateTime.UtcNow,
                CreatedById = userId,
                ModifiedById = userId,
                IsApproved = false
            };
            
            context.Proposals.Add(proposal);
            await context.SaveChangesAsync();
            
            await _log.LogAsync(LogLevel.Information, $"Created proposal ID: {proposal.ProposalId} for client: {clientId}", "ProposalService");
            return proposal.ProposalId;
        }

        /// <summary>
        /// Returns the proposal for a given renewalId, or null if not found.
        /// </summary>
        public async Task<Proposal> GetProposalByRenewalIdAsync(int renewalId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            var proposal = await context.Proposals
                .Include(p => p.Renewal)
                .Include(p => p.Renewal.Client)
                .FirstOrDefaultAsync(p => p.RenewalId == renewalId);

            if (proposal?.Renewal == null)
            {
                var renewal = await context.Renewals.FindAsync(renewalId);
                // Create a new proposal
                proposal = new Proposal
                {
                    RenewalId = renewalId,
                    CreatedById = _userManager.GetUserId(_httpContextAccessor.HttpContext.User),
                    Status = -1,
                    SendDate = renewal.RenewalDate.AddDays(-15)
                };
                context.Proposals.Add(proposal);
                await context.SaveChangesAsync();
            }

            return proposal;
        }

        /// <summary>
        /// Saves (inserts or updates) a Proposal entity.
        /// </summary>
        public async Task SaveProposalAsync(Proposal proposal)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            var userId = _userManager.GetUserId(_httpContextAccessor.HttpContext.User);
            proposal.DateModified = DateTime.UtcNow;
            proposal.ModifiedById = userId;

            // Ensure proposal has an attachment group
            await EnsureProposalHasAttachmentGroupAsync(proposal, context);

            if (proposal.ProposalId == 0)
            {
                proposal.DateCreated = DateTime.UtcNow;
                proposal.CreatedById = userId;
                context.Proposals.Add(proposal);
            }
            else
            {
                context.Proposals.Update(proposal);
            }
            await context.SaveChangesAsync();
            
            // Invalidate homepage cache since proposal data changed
            _stateService.InvalidateHomepageCache();
        }

        /// <summary>
        /// Ensures a proposal has an attachment group. Creates one if it doesn't exist.
        /// </summary>
        private async Task EnsureProposalHasAttachmentGroupAsync(Proposal proposal, ApplicationDbContext context)
        {
            if (!proposal.AttachmentGroupId.HasValue)
            {
                var attachmentGroup = new Surefire.Domain.Attachments.Models.AttachmentGroup
                {
                    CreatedAt = DateTime.UtcNow,
                    Name = proposal.ProposalId == 0 
                        ? $"New Proposal Attachments" 
                        : $"Proposal {proposal.ProposalId} Attachments"
                };
                context.AttachmentGroups.Add(attachmentGroup);
                await context.SaveChangesAsync();
                proposal.AttachmentGroupId = attachmentGroup.AttachmentGroupId;
            }
        }

        //-------------------------- PDF Functions --------------------------------//
        /// <summary>
        /// Given the original PDF bytes and a list of selected pages (1-indexed),
        /// returns a new PDF containing only those pages.
        /// </summary>
        public byte[] ExtractSelectedPages(byte[] originalPdfBytes, int[] selectedPages)
        {
            using var inputStream = new MemoryStream(originalPdfBytes);
            using var reader = new PdfReader(inputStream);
            using var inputPdf = new PdfDocument(reader);
            using var outputStream = new MemoryStream();
            using var writer = new PdfWriter(outputStream);
            using var outputPdf = new PdfDocument(writer);

            int totalPages = inputPdf.GetNumberOfPages();
            foreach (int page in selectedPages)
            {
                if (page >= 1 && page <= totalPages)
                {
                    inputPdf.CopyPagesTo(page, page, outputPdf);
                }
                else
                {
                    throw new Exception($"Page number {page} is out of range. Total pages: {totalPages}");
                }
            }
            outputPdf.Close();
            return outputStream.ToArray();
        }

        /// <summary>
        /// Extracts specified pages from a PDF and saves them as filedataforai.pdf in the same folder.
        /// </summary>
        /// <param name="originalPdfPath">Path to the original PDF file</param>
        /// <param name="pageNumbers">Comma-delimited list of page numbers to extract (1-indexed)</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> ExtractPages(string originalPdfPath, string pageNumbers)
        {
            try
            {
                await _log.LogAsync(LogLevel.Information, $"Starting page extraction from {originalPdfPath}", "ProposalService");

                // Parse the page numbers
                var selectedPages = pageNumbers.Split(',')
                    .Select(p => int.TryParse(p.Trim(), out int page) ? page : 0)
                    .Where(p => p > 0)
                    .ToArray();

                if (selectedPages.Length == 0)
                {
                    throw new Exception("No valid page numbers provided");
                }

                // Read the original PDF
                byte[] originalPdfBytes = await File.ReadAllBytesAsync(originalPdfPath);

                // Extract the selected pages
                byte[] extractedPdfBytes = ExtractSelectedPages(originalPdfBytes, selectedPages);

                // Get the directory of the original file
                string directory = Path.GetDirectoryName(originalPdfPath);
                string outputPath = Path.Combine(directory, "filedataforai.pdf");

                // Save the extracted pages
                await File.WriteAllBytesAsync(outputPath, extractedPdfBytes);

                await _log.LogAsync(LogLevel.Information, $"Successfully extracted pages to {outputPath}", "ProposalService");
                return true;
            }
            catch (Exception ex)
            {
                await _log.LogAsync(LogLevel.Error, $"Error extracting pages: {ex.Message}", "ProposalService", ex);
                return false;
            }
        }

        //--------------------------- Extractor Methods ------------------------------//
        public ExtractorService ExtractorService { get; private set; }

        // Constructor to initialize the ExtractorService
        private void InitializeExtractorService()
        {
            ExtractorService = new ExtractorService(_formRecognizerEndpoint, _formRecognizerKey, _log);
        }

        /// <summary>
        /// Updates a proposal with extracted data and attachments
        /// </summary>
        public async Task<bool> UpdateProposalWithExtractedDataAsync(int proposalId, int pdfAttachmentId, int jsonAttachmentId, string jsonData)
        {
            await _log.LogAsync(LogLevel.Information, $"Updating proposal {proposalId} with extracted data", "ProposalService");

            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();

                var proposal = await context.Proposals
                    .FirstOrDefaultAsync(p => p.ProposalId == proposalId);

                if (proposal == null)
                {
                    throw new Exception($"Proposal with ID {proposalId} not found");
                }

                // Update the proposal with the attachment IDs and JSON data
                proposal.AttachmentId = pdfAttachmentId;
                proposal.RawJsonAttachmentId = jsonAttachmentId;
                proposal.RawJson = jsonData;
                proposal.DateModified = DateTime.UtcNow;

                // Get the current user ID for the modified by field
                var userId = _userManager.GetUserId(_httpContextAccessor.HttpContext.User);
                if (!string.IsNullOrEmpty(userId))
                {
                    proposal.ModifiedById = userId;
                }

                // Save the changes to the database
                await context.SaveChangesAsync();

                await _log.LogAsync(LogLevel.Information, $"Successfully updated proposal {proposalId} with extracted data", "ProposalService");

                return true;
            }
            catch (Exception ex)
            {
                await _log.LogAsync(LogLevel.Error, $"Error updating proposal {proposalId} with extracted data: {ex.Message}", "ProposalService");
                throw;
            }
        }
    }
}
