﻿@using Surefire.Domain.Plugins
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Shared.Helpers
@using RingCentral
@using Surefire.Domain.Chat
@inject StateService _stateService
@inject ChatService _chatService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager

<div class="d-flex justify-content-between align-items-center mb-2">
    <h5>Recent Phone Calls</h5>
    <FluentStack Orientation="Orientation.Horizontal" Gap="8">
        <FluentButton Appearance="Appearance.Lightweight" OnClick="ToggleWebhookManagement" IconStart="@(new Icons.Regular.Size16.Settings())" Title="Manage SMS Webhooks">
            SMS Webhooks
        </FluentButton>
        <FluentButton Appearance="Appearance.Lightweight" OnClick="RefreshCallLogs" IconStart="@(new Icons.Regular.Size16.ArrowClockwise())" Title="Refresh Call Logs">
            Refresh
        </FluentButton>
    </FluentStack>
</div>

@if (showWebhookManagement)
{
    <div class="webhook-management-panel" style="background: #f5f5f5; padding: 16px; border-radius: 8px; margin-bottom: 16px;">
        <h6>SMS Webhook Management</h6>
        
        <FluentStack Orientation="Orientation.Horizontal" Gap="12" Style="margin-bottom: 12px;">
            <FluentTextField @bind-Value="webhookUrl" Placeholder="https://yourdomain.com/api/webhook/sms" Style="flex: 1;" />
            <FluentButton Appearance="Appearance.Accent" OnClick="RegisterWebhook" IconStart="@(new Icons.Regular.Size16.Add())" Loading="@isRegisteringWebhook">
                Register Webhook
            </FluentButton>
            <FluentButton Appearance="Appearance.Lightweight" OnClick="RefreshWebhooks" IconStart="@(new Icons.Regular.Size16.ArrowSync())" Loading="@isLoadingWebhooks">
                Refresh
            </FluentButton>
        </FluentStack>

        @if (!string.IsNullOrEmpty(webhookMessage))
        {
            <FluentMessageBar Intent="@(webhookMessageIsError ? MessageIntent.Error : MessageIntent.Success)" AllowDismiss="true" OnDismiss="() => webhookMessage = string.Empty">
                @webhookMessage
            </FluentMessageBar>
        }

        @if (isLoadingWebhooks)
        {
            <FluentProgressRing Width="30px" />
        }
        else if (webhookSubscriptions.Any())
        {
            <div class="webhook-subscriptions">
                <h6>Active Subscriptions</h6>
                @foreach (var subscription in webhookSubscriptions)
                {
                    <div class="webhook-item" style="border: 1px solid #ddd; padding: 12px; margin-bottom: 8px; border-radius: 4px;">
                        <FluentStack Orientation="Orientation.Horizontal" VerticalAlignment="VerticalAlignment.Center" Gap="12">
                            <div style="flex: 1;">
                                <div><strong>@subscription.StatusDisplay</strong> ID: @subscription.Id</div>
                                <div><small>URL: @subscription.WebhookUrl</small></div>
                                <div><small>Created: @subscription.CreationTime.ToString("yyyy-MM-dd HH:mm") | Expires: @subscription.ExpirationTime.ToString("yyyy-MM-dd HH:mm")</small></div>
                                @if (subscription.IsExpiringSoon)
                                {
                                    <div style="color: orange;"><small>⚠️ Expires in less than 24 hours</small></div>
                                }
                            </div>
                            <FluentButton Appearance="Appearance.Stealth" OnClick="() => DeleteWebhook(subscription.Id)" IconStart="@(new Icons.Regular.Size16.Delete())" Title="Delete Webhook">
                                Delete
                            </FluentButton>
                        </FluentStack>
                    </div>
                }
            </div>
        }
        else
        {
            <div style="text-align: center; color: #666; padding: 20px;">
                No webhook subscriptions found. Register one above to enable real-time SMS delivery.
            </div>
        }
    </div>
}

@if (recentCalls == null)
{
    <FluentProgressRing Width="30px" Color="#1b8ce3" />
}
else if (!recentCalls.Any())
{
    <FluentIcon Value="@(new Icons.Regular.Size24.CallDismiss())" CustomColor="#b7b7b7" Color="Color.Custom" />
    <span class="phone-none">No recent phone conversations.</span>
}
else
{
    
    <div class="fluent-data-grid">
        <FluentDataGrid Items="@recentCalls" ResizableColumns="true" ShowHover="true" Pagination="@pagination" TGridItem="CallLogRecordFire">
            <PropertyColumn Property="@(p => p.from.phoneNumber)" Title="From" Sortable="true" />
            <PropertyColumn Property="@(p => p.to.phoneNumber)" Title="To" Sortable="true" />
            <PropertyColumn Property="@(p => p.direction)" Title="Direction" Sortable="true" />
            <PropertyColumn Property="@(p => p.startTime)" Title="Start Time" Format="yyyy-MM-dd HH:mm:ss" Sortable="true" />
            <PropertyColumn Property="@(p => TimeSpan.FromSeconds(p.duration ?? 0))" Title="Duration" Format="hh\:mm\:ss" Sortable="true" />
            <PropertyColumn Property="@(p => p.action)" Title="Action" Sortable="true" />
            <PropertyColumn Property="@(p => p.result)" Title="Result" Sortable="true" />
            <TemplateColumn Title="Recording" Align="Align.Start">
                @if (context.recording != null && !string.IsNullOrEmpty(context.recording.contentUri))
                {
                    <FluentAnchor Href="@context.recording.contentUri" Target="_blank" Appearance="Appearance.Hypertext">Listen</FluentAnchor>
                }
                else
                {
                    <span>N/A</span>
                }
            </TemplateColumn>
            <PropertyColumn Property="@(p => p.type)" Title="Type" Sortable="true" />
            <PropertyColumn Property="@(p => p.reason)" Title="Reason" Sortable="true" />
            <PropertyColumn Property="@(p => p.sessionId)" Title="Session ID" Sortable="true" />
        </FluentDataGrid>
        <div class="fluent-data-grid__bottombar">
            <div class="fluent-data-grid__pagination">
                <FluentPaginator State="@pagination" />
            </div>
        </div>
    </div>
}

@code {
    private IQueryable<CallLogRecordFire> recentCalls = Enumerable.Empty<CallLogRecordFire>().AsQueryable();
    private PaginationState pagination = new PaginationState { ItemsPerPage = 17 };
    private bool isLoading = false;
    private string errorMessage = string.Empty;

    // Webhook management properties
    private bool showWebhookManagement = false;
    private bool isLoadingWebhooks = false;
    private bool isRegisteringWebhook = false;
    private string webhookUrl = "";
    private string webhookMessage = "";
    private bool webhookMessageIsError = false;
    private List<WebhookSubscription> webhookSubscriptions = new();

    protected override async Task OnInitializedAsync()
    {
        Console.WriteLine("Initializing new call logs...");
        
        // Set default webhook URL based on current domain
        var baseUri = NavigationManager.BaseUri.TrimEnd('/');
        webhookUrl = $"{baseUri}/api/webhook/sms";
        
        await LoadPhoneCallLogsAsync();
    }

    private async Task LoadPhoneCallLogsAsync()
    {
        isLoading = true;
        errorMessage = string.Empty;
        try
        {
            Console.WriteLine("Fetching call logs using ChatService...");
            var logs = await _chatService.GetRecentCallLogsAsync();
            recentCalls = logs.AsQueryable();
            Console.WriteLine($"Loaded {recentCalls.Count()} call logs.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading call logs: {ex.Message}");
            errorMessage = "Failed to load call logs. Please try again later.";
            recentCalls = Enumerable.Empty<CallLogRecordFire>().AsQueryable(); // Clear existing logs on error
        }
        finally
        {
            isLoading = false;
            await InvokeAsync(StateHasChanged); // Ensure UI updates
        }
    }

    private async Task RefreshCallLogs()
    {
        Console.WriteLine("Refresh button clicked.");
        await LoadPhoneCallLogsAsync();
    }

    // Webhook Management Methods
    private async Task ToggleWebhookManagement()
    {
        showWebhookManagement = !showWebhookManagement;
        if (showWebhookManagement)
        {
            await RefreshWebhooks();
        }
    }

    private async Task RefreshWebhooks()
    {
        isLoadingWebhooks = true;
        webhookMessage = "";
        StateHasChanged();

        try
        {
            var result = await _chatService.GetWebhookSubscriptionsAsync();
            if (result.Success)
            {
                webhookSubscriptions = result.Subscriptions;
                webhookMessage = result.Message;
                webhookMessageIsError = false;
            }
            else
            {
                webhookMessage = result.Message;
                webhookMessageIsError = true;
                webhookSubscriptions = new();
            }
        }
        catch (Exception ex)
        {
            webhookMessage = $"Error loading webhooks: {ex.Message}";
            webhookMessageIsError = true;
            webhookSubscriptions = new();
        }
        finally
        {
            isLoadingWebhooks = false;
            StateHasChanged();
        }
    }

    private async Task RegisterWebhook()
    {
        if (string.IsNullOrWhiteSpace(webhookUrl))
        {
            webhookMessage = "Please enter a webhook URL";
            webhookMessageIsError = true;
            return;
        }

        isRegisteringWebhook = true;
        webhookMessage = "";
        StateHasChanged();

        try
        {
            var result = await _chatService.RegisterSmsWebhookAsync(webhookUrl);
            webhookMessage = result.Message;
            webhookMessageIsError = !result.Success;

            if (result.Success)
            {
                // Refresh the webhook list to show the new subscription
                await RefreshWebhooks();
            }
        }
        catch (Exception ex)
        {
            webhookMessage = $"Error registering webhook: {ex.Message}";
            webhookMessageIsError = true;
        }
        finally
        {
            isRegisteringWebhook = false;
            StateHasChanged();
        }
    }

    private async Task DeleteWebhook(string subscriptionId)
    {
        try
        {
            var result = await _chatService.DeleteWebhookSubscriptionAsync(subscriptionId);
            webhookMessage = result.Message;
            webhookMessageIsError = !result.Success;

            if (result.Success)
            {
                // Refresh the webhook list to remove the deleted subscription
                await RefreshWebhooks();
            }
        }
        catch (Exception ex)
        {
            webhookMessage = $"Error deleting webhook: {ex.Message}";
            webhookMessageIsError = true;
        }

        StateHasChanged();
    }
}
