using Surefire.Data;
using Surefire.Domain.Logs;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Net.Http.Headers;
using Microsoft.EntityFrameworkCore;
using Surefire.Domain.Shared.Services;
using Surefire.Domain.Attachments.Models;
using Surefire.Domain.Attachments.Services;

namespace Surefire.Domain.Shared.Services
{
    public class OpenAIPro
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly StateService _stateService;
        private readonly ILoggingService _logService;
        private readonly AttachmentService _attachmentService;

        public OpenAIPro(IHttpClientFactory httpClientFactory, IConfiguration configuration, StateService stateService, 
            IDbContextFactory<ApplicationDbContext> dbContextFactory, ILoggingService logService,
            AttachmentService attachmentService)
        {
            _httpClientFactory = httpClientFactory;
            _httpClient = _httpClientFactory.CreateClient("OpenAI");
            _apiKey = Environment.GetEnvironmentVariable("OPENAI") ?? "MISSING";
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _apiKey);
            _dbContextFactory = dbContextFactory;
            _stateService = stateService;
            _logService = logService;
            _attachmentService = attachmentService;
        }

        public async Task<Attachment> RefineProposalDataAsync(Attachment jsonAttachment, Attachment txtAttachment, int attachmentGroupId, int clientId, int renewalId)
        {
            try
            {
                await _logService.LogAsync(LogLevel.Information, $"Starting proposal data refinement for JSON: {jsonAttachment?.OriginalFileName}, TXT: {txtAttachment?.OriginalFileName}", "OpenAIPro");

                // Read the prompt template
                var promptPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "prompts", "proposal-extractor.txt");
                var promptTemplate = await File.ReadAllTextAsync(promptPath);

                // Read the strict schema
                var schemaPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "schema", "proposal-strict.json");
                var strictSchema = await File.ReadAllTextAsync(schemaPath);

                // Read the JSON file content
                string jsonContent = "";
                if (jsonAttachment != null)
                {
                    var jsonFilePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", jsonAttachment.LocalPath, jsonAttachment.HashedFileName);
                    if (File.Exists(jsonFilePath))
                    {
                        jsonContent = await File.ReadAllTextAsync(jsonFilePath);
                        
                        // Optimize JSON content by removing text_content since we have LLM Whisperer TXT
                        jsonContent = OptimizeJsonContent(jsonContent);
                        
                        await _logService.LogAsync(LogLevel.Information, $"Optimized JSON content length: {jsonContent.Length} characters", "OpenAIPro");
                    }
                }

                // Read the TXT file content
                string txtContent = "";
                if (txtAttachment != null)
                {
                    var txtFilePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", txtAttachment.LocalPath, txtAttachment.HashedFileName);
                    if (File.Exists(txtFilePath))
                    {
                        txtContent = await File.ReadAllTextAsync(txtFilePath);
                        await _logService.LogAsync(LogLevel.Information, $"TXT content length: {txtContent.Length} characters", "OpenAIPro");
                    }
                }

                // Construct the full prompt
                var fullPrompt = $@"{promptTemplate}

ATTACHED FILES:
1. strict-schema.json:
{strictSchema}

2. ocr-json.json (Azure Document Intelligence output):
{jsonContent}

3. layout.txt (LLM Whisperer text output):
{txtContent}

Please process these files according to the instructions above and return only the refined JSON that conforms to the strict schema.";

                // Log prompt length for debugging
                await _logService.LogAsync(LogLevel.Information, $"Full prompt length: {fullPrompt.Length} characters", "OpenAIPro");

                // Prepare the OpenAI request
                var requestBody = new
                {
                    model = "gpt-4o",
                    messages = new[]
                    {
                        new { role = "user", content = fullPrompt }
                    },
                    temperature = 0.1,
                    max_tokens = 16000 // Increased from 4000
                };

                var jsonRequestBody = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(jsonRequestBody, Encoding.UTF8, "application/json");

                // Send request to OpenAI
                var response = await _httpClient.PostAsync("https://api.openai.com/v1/chat/completions", content);
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    await _logService.LogAsync(LogLevel.Error, $"OpenAI API error: {response.StatusCode} - {errorContent}", "OpenAIPro");
                    throw new Exception($"OpenAI API error: {response.StatusCode}");
                }

                var responseString = await response.Content.ReadAsStringAsync();
                var openAiResponse = JsonSerializer.Deserialize<OpenAiResponse>(responseString);
                var refinedJson = openAiResponse?.Choices?[0]?.Message?.Content ?? string.Empty;

                // Log the raw response for debugging
                await _logService.LogAsync(LogLevel.Information, $"Raw OpenAI response: {responseString}", "OpenAIPro");
                await _logService.LogAsync(LogLevel.Information, $"Extracted content: {refinedJson}", "OpenAIPro");
                
                // Check finish reason
                var finishReason = openAiResponse?.Choices?[0]?.FinishReason;
                await _logService.LogAsync(LogLevel.Information, $"OpenAI finish reason: {finishReason}", "OpenAIPro");
                
                // Log token usage
                if (openAiResponse?.Usage != null)
                {
                    await _logService.LogAsync(LogLevel.Information, $"Token usage - Prompt: {openAiResponse.Usage.PromptTokens}, Completion: {openAiResponse.Usage.CompletionTokens}, Total: {openAiResponse.Usage.TotalTokens}", "OpenAIPro");
                }
                
                if (finishReason == "length")
                {
                    await _logService.LogAsync(LogLevel.Warning, "OpenAI response was truncated due to token limit", "OpenAIPro");
                }

                // Clean up the response (remove markdown formatting if present)
                refinedJson = CleanJsonResponse(refinedJson);

                await _logService.LogAsync(LogLevel.Information, $"Cleaned JSON: {refinedJson}", "OpenAIPro");

                // Validate that it's proper JSON
                try
                {
                    JsonDocument.Parse(refinedJson);
                }
                catch (JsonException ex)
                {
                    await _logService.LogAsync(LogLevel.Error, $"JSON validation failed. Content: {refinedJson}", "OpenAIPro");
                    await _logService.LogAsync(LogLevel.Error, $"JSON validation error: {ex.Message}", "OpenAIPro");
                    throw new Exception($"OpenAI returned invalid JSON: {ex.Message}");
                }

                // Save the refined JSON as a new attachment
                var refinedAttachment = await SaveRefinedJsonAsync(refinedJson, attachmentGroupId, clientId, renewalId);
                
                await _logService.LogAsync(LogLevel.Information, $"Successfully created refined JSON: {refinedAttachment.OriginalFileName}", "OpenAIPro");
                
                return refinedAttachment;
            }
            catch (Exception ex)
            {
                await _logService.LogAsync(LogLevel.Error, $"Error refining proposal data: {ex.Message}", "OpenAIPro", ex);
                throw;
            }
        }

        public async Task<Attachment> ExtractSupplementalFieldsFromTextAsync(Attachment txtAttachment, int attachmentGroupId, int clientId, int renewalId, string fieldsFile, string promptFile)
        {
            try
            {
                await _logService.LogAsync(LogLevel.Information, $"Starting supplemental field extraction for TXT: {txtAttachment?.OriginalFileName}", "OpenAIPro");

                // Read the prompt template (dynamic)
                var promptPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "prompts", promptFile);
                var promptTemplate = await File.ReadAllTextAsync(promptPath);

                // Read the list of fields (dynamic, can be .txt or .pdf)
                var fieldsPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "forms", fieldsFile);
                string listOfFields;
                if (fieldsFile.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                {
                    // If the fields file is a PDF, just note the filename (or implement PDF text extraction if needed)
                    listOfFields = $"[PDF fields file: {fieldsFile}]";
                }
                else
                {
                    listOfFields = await File.ReadAllTextAsync(fieldsPath);
                }

                // Read the TXT file content
                string txtContent = "";
                if (txtAttachment != null)
                {
                    var txtFilePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", txtAttachment.LocalPath, txtAttachment.HashedFileName);
                    if (File.Exists(txtFilePath))
                    {
                        txtContent = await File.ReadAllTextAsync(txtFilePath);
                        await _logService.LogAsync(LogLevel.Information, $"TXT content length: {txtContent.Length} characters", "OpenAIPro");
                    }
                }

                // Construct the full prompt
                var fullPrompt = $@"{promptTemplate}\n\nFORM LAYOUT (formdata):\n{txtContent}\n\nLIST OF FIELDS (listoffields):\n{listOfFields}\n";
                Console.WriteLine(fullPrompt); // For debugging purposes
                await _logService.LogAsync(LogLevel.Information, $"Full prompt length: {fullPrompt.Length} characters", "OpenAIPro");

                // Prepare the OpenAI request
                var requestBody = new
                {
                    model = "o4-mini-2025-04-16",
                    messages = new[]
                    {
                        new { role = "user", content = fullPrompt }
                    },
                    max_completion_tokens = 16000
                };

                var jsonRequestBody = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(jsonRequestBody, Encoding.UTF8, "application/json");

                // Send request to OpenAI
                var response = await _httpClient.PostAsync("https://api.openai.com/v1/chat/completions", content);
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    await _logService.LogAsync(LogLevel.Error, $"OpenAI API error: {response.StatusCode} - {errorContent}", "OpenAIPro");
                    throw new Exception($"OpenAI API error: {response.StatusCode}");
                }

                var responseString = await response.Content.ReadAsStringAsync();
                var openAiResponse = JsonSerializer.Deserialize<OpenAiResponse>(responseString);
                var extractedJson = openAiResponse?.Choices?[0]?.Message?.Content ?? string.Empty;

                await _logService.LogAsync(LogLevel.Information, $"Raw OpenAI response: {responseString}", "OpenAIPro");
                await _logService.LogAsync(LogLevel.Information, $"Extracted content: {extractedJson}", "OpenAIPro");

                // Clean up the response (remove markdown formatting if present)
                extractedJson = CleanJsonResponse(extractedJson);
                await _logService.LogAsync(LogLevel.Information, $"Cleaned JSON: {extractedJson}", "OpenAIPro");

                // Validate that it's proper JSON
                try
                {
                    JsonDocument.Parse(extractedJson);
                }
                catch (JsonException ex)
                {
                    await _logService.LogAsync(LogLevel.Error, $"JSON validation failed. Content: {extractedJson}", "OpenAIPro");
                    await _logService.LogAsync(LogLevel.Error, $"JSON validation error: {ex.Message}", "OpenAIPro");
                    throw new Exception($"OpenAI returned invalid JSON: {ex.Message}");
                }

                // Save the extracted JSON as a new attachment
                var extractedAttachment = await SaveRefinedJsonAsync(extractedJson, attachmentGroupId, clientId, renewalId);
                await _logService.LogAsync(LogLevel.Information, $"Successfully created extracted JSON: {extractedAttachment.OriginalFileName}", "OpenAIPro");
                return extractedAttachment;
            }
            catch (Exception ex)
            {
                await _logService.LogAsync(LogLevel.Error, $"Error extracting supplemental fields: {ex.Message}", "OpenAIPro", ex);
                throw;
            }
        }

        public async Task<Attachment> AutoDetectJsonFromDocJsonAsync(string docJson, string fieldDefsJson, int attachmentGroupId, int clientId, int renewalId, string promptFile)
        {
            try
            {
                await _logService.LogAsync(LogLevel.Information, "Starting autodetect (docjson) extraction", "OpenAIPro");

                // Read prompt template
                var promptPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "prompts", promptFile);
                if (!File.Exists(promptPath))
                    throw new FileNotFoundException($"Prompt template not found: {promptPath}");
                var promptTemplate = await File.ReadAllTextAsync(promptPath);

                var fullPrompt = $"{promptTemplate}\n\nDOCJSON:\n{docJson}\n\nFIELDDEFS:\n{fieldDefsJson}\n";
                await _logService.LogAsync(LogLevel.Information, $"Prompt chars: {fullPrompt.Length}", "OpenAIPro");

                var requestBody = new
                {
                    model = "gpt-4o",
                    messages = new[]
                    {
                        new { role = "user", content = fullPrompt }
                    },
                    temperature = 0.1,
                    max_tokens = 16000
                };
                var jsonRequestBody = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(jsonRequestBody, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync("https://api.openai.com/v1/chat/completions", content);
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    await _logService.LogAsync(LogLevel.Error, $"OpenAI API error: {response.StatusCode} - {errorContent}", "OpenAIPro");
                    throw new Exception($"OpenAI API error: {response.StatusCode}");
                }
                var responseString = await response.Content.ReadAsStringAsync();
                var openAiResponse = JsonSerializer.Deserialize<OpenAiResponse>(responseString);
                var extractedJson = openAiResponse?.Choices?[0]?.Message?.Content ?? string.Empty;
                extractedJson = CleanJsonResponse(extractedJson);
                // Validate JSON
                JsonDocument.Parse(extractedJson);
                // Save
                var jsonAttachment = await SaveRefinedJsonAsync(extractedJson, attachmentGroupId, clientId, renewalId);
                return jsonAttachment;
            }
            catch (Exception ex)
            {
                await _logService.LogAsync(LogLevel.Error, $"Error in AutoDetectJsonFromDocJsonAsync: {ex.Message}", "OpenAIPro", ex);
                throw;
            }
        }

        public async Task<Attachment> AutoDetectJsonFromTextAsync(Attachment txtAttachment, int attachmentGroupId, int clientId, int renewalId, string promptFile)
        {
            try
            {
                await _logService.LogAsync(LogLevel.Information, $"Starting autodetect JSON extraction for TXT: {txtAttachment?.OriginalFileName}", "OpenAIPro");

                // Read prompt template (dynamic)
                var promptPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "schema", promptFile);
                if (!File.Exists(promptPath))
                {
                    throw new FileNotFoundException($"Prompt template not found: {promptPath}");
                }
                var promptTemplate = await File.ReadAllTextAsync(promptPath);

                // Read TXT content from Whisperer output
                string txtContent = string.Empty;
                if (txtAttachment != null)
                {
                    var txtFilePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", txtAttachment.LocalPath, txtAttachment.HashedFileName);
                    if (File.Exists(txtFilePath))
                    {
                        txtContent = await File.ReadAllTextAsync(txtFilePath);
                        await _logService.LogAsync(LogLevel.Information, $"TXT content length: {txtContent.Length} characters", "OpenAIPro");
                    }
                    else
                    {
                        throw new FileNotFoundException($"TXT attachment file not found: {txtFilePath}");
                    }
                }

                // Build full prompt – template first then the extracted layout
                var fullPrompt = $"{promptTemplate}\n\nFORM LAYOUT (formdata):\n{txtContent}\n";
                await _logService.LogAsync(LogLevel.Information, $"Full prompt length: {fullPrompt.Length} characters", "OpenAIPro");

                var requestBody = new
                {
                    model = "gpt-4o",
                    messages = new[]
                    {
                        new { role = "user", content = fullPrompt }
                    },
                    temperature = 0.1,
                    max_tokens = 16000
                };

                var jsonRequestBody = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(jsonRequestBody, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("https://api.openai.com/v1/chat/completions", content);
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    await _logService.LogAsync(LogLevel.Error, $"OpenAI API error: {response.StatusCode} - {errorContent}", "OpenAIPro");
                    throw new Exception($"OpenAI API error: {response.StatusCode}");
                }

                var responseString = await response.Content.ReadAsStringAsync();
                var openAiResponse = JsonSerializer.Deserialize<OpenAiResponse>(responseString);
                var extractedJson = openAiResponse?.Choices?[0]?.Message?.Content ?? string.Empty;

                await _logService.LogAsync(LogLevel.Information, $"Raw OpenAI response: {responseString}", "OpenAIPro");
                extractedJson = CleanJsonResponse(extractedJson);
                await _logService.LogAsync(LogLevel.Information, $"Cleaned JSON: {extractedJson}", "OpenAIPro");

                // Validate JSON
                try
                {
                    JsonDocument.Parse(extractedJson);
                }
                catch (JsonException ex)
                {
                    await _logService.LogAsync(LogLevel.Error, $"JSON validation failed. Content: {extractedJson}", "OpenAIPro");
                    throw new Exception($"OpenAI returned invalid JSON: {ex.Message}");
                }

                // Save as attachment
                var jsonAttachment = await SaveRefinedJsonAsync(extractedJson, attachmentGroupId, clientId, renewalId);
                await _logService.LogAsync(LogLevel.Information, $"Successfully created autodetect JSON: {jsonAttachment.OriginalFileName}", "OpenAIPro");
                return jsonAttachment;
            }
            catch (Exception ex)
            {
                await _logService.LogAsync(LogLevel.Error, $"Error in autodetect extraction: {ex.Message}", "OpenAIPro", ex);
                throw;
            }
        }

        private string CleanJsonResponse(string jsonResponse)
        {
            if (string.IsNullOrWhiteSpace(jsonResponse))
                return jsonResponse;

            // Remove markdown code block formatting if present
            if (jsonResponse.StartsWith("```json"))
            {
                jsonResponse = jsonResponse.Substring(7);
            }
            else if (jsonResponse.StartsWith("```"))
            {
                jsonResponse = jsonResponse.Substring(3);
            }
            
            if (jsonResponse.EndsWith("```"))
            {
                jsonResponse = jsonResponse.Substring(0, jsonResponse.Length - 3);
            }
            
            // Trim whitespace
            jsonResponse = jsonResponse.Trim();
            
            // Look for JSON object boundaries if there's extra text
            var firstBrace = jsonResponse.IndexOf('{');
            var lastBrace = jsonResponse.LastIndexOf('}');
            
            if (firstBrace >= 0 && lastBrace > firstBrace)
            {
                jsonResponse = jsonResponse.Substring(firstBrace, lastBrace - firstBrace + 1);
            }
            
            return jsonResponse;
        }

        private string OptimizeJsonContent(string jsonContent)
        {
            try
            {
                // Parse the JSON
                var jsonDoc = JsonDocument.Parse(jsonContent);
                var root = jsonDoc.RootElement;

                // Create optimized structure with only key_value_pairs and tables
                var optimizedData = new
                {
                    metadata = root.TryGetProperty("metadata", out var metadata) ? 
                        JsonSerializer.Deserialize<object>(metadata.GetRawText()) : null,
                    key_value_pairs = root.TryGetProperty("key_value_pairs", out var kvPairs) ? 
                        JsonSerializer.Deserialize<object>(kvPairs.GetRawText()) : new List<object>(),
                    tables = root.TryGetProperty("tables", out var tables) ? 
                        JsonSerializer.Deserialize<object>(tables.GetRawText()) : new List<object>()
                    // Deliberately excluding text_content since we have LLM Whisperer TXT
                };

                // Serialize back to JSON
                var optimizedJson = JsonSerializer.Serialize(optimizedData, new JsonSerializerOptions 
                { 
                    WriteIndented = false // Compact format to save tokens
                });

                return optimizedJson;
            }
            catch (JsonException ex)
            {
                // If parsing fails, return original content
                _logService.LogAsync(LogLevel.Warning, $"Failed to optimize JSON content: {ex.Message}", "OpenAIPro");
                return jsonContent;
            }
        }

        private async Task<Attachment> SaveRefinedJsonAsync(string jsonContent, int attachmentGroupId, int clientId, int renewalId)
        {
            // Generate a unique filename
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var originalFileName = $"refined_proposal_{timestamp}.json";
            var hashedFileName = $"{Guid.NewGuid()}.json";

            // Determine the save path
            var entityFolder = $"clients/{clientId}";
            var saveDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", entityFolder, attachmentGroupId.ToString());
            
            // Ensure directory exists
            Directory.CreateDirectory(saveDirectory);
            
            var filePath = Path.Combine(saveDirectory, hashedFileName);
            
            // Write the JSON content to file
            await File.WriteAllTextAsync(filePath, jsonContent);

            // Create the attachment record
            var attachment = new Attachment
            {
                OriginalFileName = originalFileName,
                HashedFileName = hashedFileName,
                LocalPath = $"uploads/{entityFolder}/{attachmentGroupId}",
                FileFormat = ".json",
                FileSize = Encoding.UTF8.GetByteCount(jsonContent),
                DateCreated = DateTime.UtcNow,
                AttachmentGroupId = attachmentGroupId,
                ClientId = clientId,
                RenewalId = renewalId > 0 ? renewalId : null,
                Description = "Refined proposal data (OpenAI processed)",
                UploadedById = _stateService.CurrentUser?.Id,
                IsRefinedProposal = true // Add this flag to identify refined proposals
            };

            // Save to database
            await _attachmentService.SaveAttachmentDirectlyAsync(attachment);
            
            return attachment;
        }
    }

    // Response model for OpenAI API
    public class OpenAiResponse
    {
        [JsonPropertyName("choices")]
        public Choice[] Choices { get; set; }
        
        [JsonPropertyName("usage")]
        public Usage Usage { get; set; }
    }

    public class Choice
    {
        [JsonPropertyName("message")]
        public Message Message { get; set; }
        
        [JsonPropertyName("finish_reason")]
        public string FinishReason { get; set; }
    }

    public class Message
    {
        [JsonPropertyName("content")]
        public string Content { get; set; }
    }
    
    public class Usage
    {
        [JsonPropertyName("prompt_tokens")]
        public int PromptTokens { get; set; }
        
        [JsonPropertyName("completion_tokens")]
        public int CompletionTokens { get; set; }
        
        [JsonPropertyName("total_tokens")]
        public int TotalTokens { get; set; }
    }
} 