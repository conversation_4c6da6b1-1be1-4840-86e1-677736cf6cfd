#region Usings Statements
using DotNetEnv;
using Surefire.Data;
using Surefire.Domain.Accounting.Services;
using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.Services;
using Surefire.Domain.Agents.TaskAgents;
using Surefire.Domain.Attachments.Services;
using Surefire.Domain.Carriers.Services;
using Surefire.Domain.Chat;
using Surefire.Domain.Chat.Services;
using Surefire.Domain.Clients.Services;
using Surefire.Domain.Contacts.Services;
using Surefire.Domain.DocuSign;
using Surefire.Domain.Ember;
using Surefire.Domain.Forms.Services;
using Surefire.Domain.Graph;
using Surefire.Domain.Logs;
using Surefire.Domain.OpenAI;
using Surefire.Domain.OpenAI.Simple;
using Surefire.Domain.Plugins;
using Surefire.Domain.Policies.Services;
using Surefire.Domain.Proposals;
using Surefire.Domain.Proposals.Services;
using Surefire.Domain.Renewals.Services;
using Surefire.Domain.Shared.Services;
using Surefire.Domain.Users.Services;
using Surefire.Hubs;
using Surefire.Interfaces;
using Surefire.Components.Account;
using Surefire.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.FluentUI.AspNetCore.Components;
using Microsoft.FluentUI.AspNetCore.Components.Components.Tooltip;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.EntityFrameworkCore;
using Syncfusion.Blazor;
using Syncfusion.Blazor.SmartComponents;
using System.Net.Http.Headers;
#endregion

// INITIAL VARIABLES -- -- -- -   -     -     -                -           -              -            -   -       -  -   -  - -  ---  -
WebApplicationBuilder builder = WebApplication.CreateBuilder(args);
builder.Services.AddHttpClient();
builder.Services.AddRazorComponents().AddInteractiveServerComponents();
builder.Services.AddMemoryCache();

// Add Controllers support
builder.Services.AddControllers();

Env.Load();
bool detailedErrorsEnabled = builder.Configuration.GetValue<bool>("DetailedErrors:Enabled");

// IDEN AND AUTH -- -- -- -   -     -     -      -             -           -            -           -   -      -  -   -  --  ---  --
builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<IdentityUserAccessor>();
builder.Services.AddScoped<IdentityRedirectManager>();
builder.Services.AddAuthorization();
builder.Services.AddScoped<AuthenticationStateProvider, IdentityRevalidatingAuthenticationStateProvider>();
builder.Services.AddAuthentication(options => { 
    options.DefaultScheme = IdentityConstants.ApplicationScheme;
    options.DefaultSignInScheme = IdentityConstants.ExternalScheme;
}).AddIdentityCookies();
builder.Services.AddIdentityCore<ApplicationUser>(options => options.SignIn.RequireConfirmedAccount = true)
    .AddEntityFrameworkStores<ApplicationDbContext>().AddSignInManager().AddDefaultTokenProviders();

// DATABASE  -- -- -- -   -     -
string? connectionString = Environment.GetEnvironmentVariable("DEFAULTCONNECTION");
builder.Services.AddDbContextFactory<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString, sqlOptions =>
    {
        sqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
        sqlOptions.EnableRetryOnFailure(maxRetryCount: 5, maxRetryDelay: TimeSpan.FromSeconds(30), errorNumbersToAdd: null);
    }));

// LLMs -- -- -- -   -     -  -     -      -                -           -              -            -   -       -  -   -  - -  ---  -
builder.Services.AddHttpClient<OpenAiService>();
builder.Services.AddHttpClient("OpenAI", client => {
    var openAiKey = builder.Configuration["OpenAi:ApiKey"] ?? throw new InvalidOperationException("OpenAI API key missing");
    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", openAiKey);
    client.Timeout = TimeSpan.FromSeconds(230);
    client.BaseAddress = new Uri("https://api.openai.com/v1/");
});
builder.Services.AddHttpClient("LLMWhisperer", client => {
    client.Timeout = TimeSpan.FromMinutes(10);
});
builder.Services.AddScoped<IOpenAISimpleService, OpenAISimpleService>();
string qdrantBaseUrl = Environment.GetEnvironmentVariable("QDRANT_BASE_URL") ?? "http://localhost:6333/";
string qdrantApiKey = Environment.GetEnvironmentVariable("QDRANT_API_KEY") ?? "api-key";
builder.Services.AddHttpClient("Qdrant", client => {
    client.BaseAddress = new Uri(qdrantBaseUrl);
    client.DefaultRequestHeaders.Add("api-key", qdrantApiKey);
});
builder.Services.AddScoped<IEmbeddingService, OpenAIEmbeddingService>();
builder.Services.AddScoped<IVectorStore, QdrantVectorStore>();
builder.Services.AddScoped<EmbeddingLoaderService>();
builder.Configuration["Qdrant:Collection"] = "surefirex1";
string openAiApiKey = Environment.GetEnvironmentVariable("OPENAI") ?? "MISSING";
builder.Configuration["OpenAi:ApiKey"] = openAiApiKey;


// COMPONENTS AND SYNCFUSION  -     -      -                -           -              -            -   -       -  -   -  - -  ---  -
builder.Services.AddSyncfusionBlazor();
builder.Services.AddFluentUIComponents();
builder.Services.AddDataGridEntityFrameworkAdapter();
builder.Services.AddSyncfusionSmartComponents().ConfigureCredentials(new AIServiceCredentials(openAiApiKey, "gpt-3.5-turbo", "")).InjectOpenAIInference();
string? syncFusionKey = Environment.GetEnvironmentVariable("SYNCFUSION");
Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(syncFusionKey);

// DEPENDENCIES -- -- -- -   -     -      -                -           -              -            -   -       -  -   -  - -  ---  -
builder.Services.AddScoped<AttachmentService>();
builder.Services.AddScoped<AzureFormService>();
builder.Services.AddScoped<LLMWhispererService>();
builder.Services.AddScoped<OpenAIPro>();
builder.Services.AddScoped<CarrierService>();
builder.Services.AddScoped<ClientService>();
builder.Services.AddScoped<ContactService>();
builder.Services.AddScoped<EmberService>();
builder.Services.AddScoped<FormService>();
builder.Services.AddScoped<ExternalPortalService>();
builder.Services.AddScoped<HomeService>();
builder.Services.AddScoped<PolicyService>();
builder.Services.AddScoped<RenewalService>();
builder.Services.AddScoped<AccountingService>();
builder.Services.AddScoped<SearchService>();
builder.Services.AddScoped<SharedService>();
builder.Services.AddScoped<TaskService>();
builder.Services.AddScoped<UserService>();
builder.Services.AddScoped<StateService>();
builder.Services.AddScoped<ProposalService>();
builder.Services.AddScoped<ClientStateService>();
builder.Services.AddScoped<PhoneLookupService>();
builder.Services.AddScoped<ProposalWordDocumentService>();
builder.Services.AddScoped<PolicyExtractorService>();
builder.Services.AddScoped<Surefire.Domain.Proposals.Services.PackagerService>();
builder.Services.AddScoped<GraphService>();
builder.Services.AddScoped<SurefireDialogService>();
builder.Services.AddScoped<ISubmissionService, SubmissionService>();
builder.Services.AddScoped<ILoggingService, LoggingService>();
builder.Services.AddScoped<ITooltipService, TooltipService>();
builder.Services.AddScoped<IEmailTemplateService, EmailTemplateService>();


// DOCUSIGN SERVICES   --   --   -  -   -       -             -                  -             -   -       -    -   -  - -   ---  -
builder.Services.AddScoped<IDocuSignConfigService, DocuSignConfigService>();
builder.Services.AddHttpClient<DocuSignService>();
builder.Services.AddScoped<IDocuSignService>(sp => {
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var configService = sp.GetRequiredService<IDocuSignConfigService>();
    var logger = sp.GetRequiredService<ILogger<DocuSignService>>();
    var cache = sp.GetRequiredService<IMemoryCache>();
    var httpClient = httpClientFactory.CreateClient(nameof(DocuSignService));
    return new DocuSignService(httpClient, configService, logger, cache);
});

// UNIFIED AGENTIC AI PIPELINE SERVICES -- -- -- -   -     -          -              -            -   -       -  -   -  - -  ---  -
builder.Services.AddScoped<INavigationAgent, NavigationAgent>();
builder.Services.AddScoped<IOpenAIAgent, OpenAIAgent>();

// TASK AGENT SERVICES -- -- -- -   -     -
builder.Services.AddSingleton<ITaskAgentRegistry, TaskAgentRegistry>();
builder.Services.AddScoped<IParameterExtractionService, ParameterExtractionService>();
builder.Services.AddScoped<ITaskAgentOrchestrator, TaskAgentOrchestrator>();

// REGISTER INDIVIDUAL TASK AGENTS -- -- -- -   -    -     -          -              -            -   -       -  -   -  - -  ---  -
builder.Services.AddScoped<LossRunRequestAgent>();
builder.Services.AddScoped<PaymentLinkAgent>();
builder.Services.AddScoped<SimplePayLinkSender>();
builder.Services.AddSingleton<IEmailSender<ApplicationUser>, IdentityNoOpEmailSender>();
builder.Services.AddSingleton<IConfiguration>(builder.Configuration);
builder.Services.AddSignalR(hubOptions =>
{
    // Increase buffer sizes for large data transfers (like Word documents)
    hubOptions.MaximumReceiveMessageSize = 10 * 1024 * 1024; // 10MB
    hubOptions.StreamBufferCapacity = 100;
    hubOptions.MaximumParallelInvocationsPerClient = 10;
    // Enable detailed errors in development
    #if DEBUG
    hubOptions.EnableDetailedErrors = true;
    #endif
});
builder.Services.AddHttpContextAccessor();
builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true).AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true).AddEnvironmentVariables();
builder.Services.AddServerSideBlazor().AddHubOptions(o => { o.MaximumReceiveMessageSize = 102400000; });

// PLUGINS -- -- -- -   -     -
builder.Services.AddScoped<PluginManager>();
#if DEBUG
    var pluginsPath = Path.Combine(Directory.GetCurrentDirectory(), "Plugins");
#else
    var pluginsPath = Path.Combine(Directory.GetCurrentDirectory(), "plugins");
#endif
var serviceProvider = builder.Services.BuildServiceProvider();
var logger = serviceProvider.GetRequiredService<ILoggingService>();
PluginLoader.LoadPlugins(builder.Services, pluginsPath, serviceProvider, logger);

// RINGCENTRAL AND CHAT SERVICES -- -- -- -   -  -     -            -                -            -   -       -  -   -  - -  ---  -
builder.Services.AddHttpClient<ChatService>(client => {
    client.Timeout = TimeSpan.FromSeconds(30);
});
builder.Services.AddScoped<ChatService>();
builder.Services.AddScoped<SmsMessageService>();
// SmsBackgroundService REMOVED: Now using webhook-only approach for real-time message delivery

// Background Services
// Add transcription service for call recordings (WebSocket-based with REST fallback)
builder.Services.AddHttpClient<TranscriptionService>(client => {
    client.Timeout = TimeSpan.FromSeconds(120); // Allow longer timeout for transcription
});
builder.Services.AddScoped<TranscriptionService>();

// Add voice recording service for Enhanced AI Chat
builder.Services.AddScoped<IVoiceRecordingService, VoiceRecordingService>();

// ---------------------------------------------------//
// App Configuration Protocols ---------------------- //
// ---------------------------------------------------//
Microsoft.AspNetCore.Builder.WebApplication app = builder.Build();
#if DEBUG
    app.UseDeveloperExceptionPage();
#endif

app.UseHsts();
app.UseHttpsRedirection(); //Turn off for WenView2 Desktop runtime
app.UseDeveloperExceptionPage();
app.UseMigrationsEndPoint();
app.MapStaticAssets();

// Configure static files
app.UseDefaultFiles();
app.UseStaticFiles(new StaticFileOptions
{
    OnPrepareResponse = ctx =>
    {
        // Cache static files for 1 day
        ctx.Context.Response.Headers.Append("Cache-Control", "public,max-age=86400");
    },
    ServeUnknownFileTypes = true,
    DefaultContentType = "application/octet-stream"
});

// Removed the UseStaticFiles for working with uploads directory for dynamically generated files

app.UseAntiforgery();
app.UseAuthentication();
app.UseAuthorization();
app.MapHub<NotificationHub>("/notificationHub");
app.MapHub<MessagingHub>("/messagingHub");
app.MapHub<EmberHub>("/emberHub");
app.MapRazorComponents<App>().AddInteractiveServerRenderMode();

// Map Controllers (for API controllers like InternalSmsWebhookController)
app.MapControllers();

//app.MapAdditionalIdentityEndpoints();

// -- -- -- -   -  -     -            -                -            -   -       -  -   -  - -  ---  -
#region API Endpoints

// Get a call recording
app.MapGet("/api/call/recording/{recordingId}", async (string recordingId, ChatService chatService) => {
    var recording = await chatService.GetCallRecordingAsync(recordingId);
    if (recording == null)
        return Results.NotFound("Recording not found");
        
    return Results.File(recording.Value.Data, recording.Value.ContentType);
}).AllowAnonymous();

// Get a call recording using its content URI
app.MapGet("/api/call/recording-by-uri", async ([FromQuery] string contentUri, ChatService chatService) => {
    if (string.IsNullOrEmpty(contentUri))
        return Results.BadRequest("Content URI is required");
        
    var recording = await chatService.GetCallRecordingByUriAsync(contentUri);
    if (recording == null)
        return Results.NotFound("Recording not found");
        
    return Results.File(recording.Value.Data, recording.Value.ContentType);
}).AllowAnonymous();

// Get a call transcription
app.MapGet("/api/call/transcription/{callId}", async (string callId, TranscriptionService transcriptionService) => {
    var transcription = await transcriptionService.GetTranscriptionAsync(callId);
    if (transcription == null)
        return Results.NotFound("Transcription not found");
        
    return Results.Ok(transcription);
}).AllowAnonymous();

// RingCentral SMS Webhook endpoint
app.MapPost("/api/webhook/sms", async (HttpContext context, SmsMessageService smsMessageService, IHubContext<MessagingHub> hubContext, ILogger<Program> logger, ILoggingService logService) => {
    try
    {
        // Log every webhook request
        await logService.LogAsync(LogLevel.Information, $"SMS Webhook endpoint called from IP: {context.Connection.RemoteIpAddress}", "WebHookSMS");
        
        // CRITICAL: Handle RingCentral validation token challenge first
        // This must be done before reading the request body
        if (context.Request.Headers.TryGetValue("Validation-Token", out var validationToken))
        {
            context.Response.Headers.Add("Validation-Token", validationToken.ToString());
            logger.LogInformation("SMS Webhook: Validation token challenge handled: {ValidationToken}", validationToken);
            await logService.LogAsync(LogLevel.Information, $"Validation token challenge handled: {validationToken}", "WebHookSMS");
            return Results.Ok();
        }
        
        // Read the raw request body
        using var reader = new StreamReader(context.Request.Body);
        var requestBody = await reader.ReadToEndAsync();
        
        logger.LogInformation("SMS Webhook received: {RequestBody}", requestBody);
        await logService.LogAsync(LogLevel.Information, $"Received webhook event: {requestBody}", "WebHookSMS");
        
        // Basic validation - check if it's a valid JSON
        if (string.IsNullOrWhiteSpace(requestBody))
        {
            logger.LogWarning("SMS Webhook: Empty request body");
            await logService.LogAsync(LogLevel.Warning, "Empty request body received", "WebHookSMS");
            return Results.BadRequest("Empty request body");
        }
        
        // Parse RingCentral webhook payload
        using var document = System.Text.Json.JsonDocument.Parse(requestBody);
        var root = document.RootElement;
        
        await logService.LogAsync(LogLevel.Information, "JSON parsed successfully, validating structure", "WebHookSMS");
        
        // Validate webhook structure
        if (!root.TryGetProperty("body", out var body))
        {
            logger.LogWarning("SMS Webhook: Missing 'body' property");
            await logService.LogAsync(LogLevel.Warning, "Missing 'body' property in webhook payload", "WebHookSMS");
            return Results.BadRequest("Invalid webhook format");
        }
        
        await logService.LogAsync(LogLevel.Information, "Processing AT&T Office@Hand webhook format", "WebHookSMS");
        
        int processedMessages = 0;
        
        // AT&T Office@Hand sends message data directly in body, not in changes array
        // Check if this is an SMS message by looking for required SMS properties
        if (body.TryGetProperty("type", out var messageType) && messageType.GetString() == "SMS")
        {
            await logService.LogAsync(LogLevel.Information, "Processing SMS message from AT&T Office@Hand", "WebHookSMS");
            
            // Extract message details from AT&T Office@Hand format
            var messageId = body.TryGetProperty("id", out var idProp) ? idProp.GetString() : null;
            var direction = body.TryGetProperty("direction", out var dirProp) ? dirProp.GetString() : "";
            
            // Extract text from subject field (AT&T Office@Hand puts SMS text in subject)
            var text = body.TryGetProperty("subject", out var textProp) ? textProp.GetString() : "";
            
            var creationTime = DateTime.UtcNow;
            if (body.TryGetProperty("creationTime", out var timeProp))
            {
                if (DateTime.TryParse(timeProp.GetString(), out var parsedTime))
                {
                    creationTime = parsedTime.ToUniversalTime();
                }
            }
            
            // Extract phone number based on direction
            string phoneNumber = null;
            bool isInbound = direction == "Inbound";
            
            if (isInbound && body.TryGetProperty("from", out var fromProp))
            {
                if (fromProp.TryGetProperty("phoneNumber", out var fromPhoneProp))
                {
                    phoneNumber = fromPhoneProp.GetString();
                }
            }
            else if (!isInbound && body.TryGetProperty("to", out var toProp) && toProp.ValueKind == System.Text.Json.JsonValueKind.Array)
            {
                var toArray = toProp.EnumerateArray().FirstOrDefault();
                if (toArray.TryGetProperty("phoneNumber", out var toPhoneProp))
                {
                    phoneNumber = toPhoneProp.GetString();
                }
            }
            
            // Log extracted message details
            await logService.LogAsync(LogLevel.Information, $"Extracted message - ID: {messageId}, Phone: {phoneNumber}, Direction: {direction}, Text: {text?.Substring(0, Math.Min(text?.Length ?? 0, 50))}...", "WebHookSMS");
            
            // Validate required fields
            if (string.IsNullOrEmpty(messageId) || string.IsNullOrEmpty(phoneNumber) || string.IsNullOrEmpty(text))
            {
                logger.LogWarning("SMS Webhook: Missing required fields - ID: {MessageId}, Phone: {PhoneNumber}, Text length: {TextLength}", 
                    messageId, phoneNumber, text?.Length ?? 0);
                await logService.LogAsync(LogLevel.Warning, $"Missing required fields - ID: {messageId}, Phone: {phoneNumber}, Text length: {text?.Length ?? 0}", "WebHookSMS");
                //continue;
            }
            
            // Create SmsMessage object for storage
            var smsMessage = new SmsMessage
            {
                Id = messageId,
                PhoneNumber = phoneNumber,
                Text = text,
                Timestamp = creationTime,
                IsInbound = isInbound
            };
            
            // Store message in database
            var storedMessage = await smsMessageService.StoreSmsMessageAsync(smsMessage);
            if (storedMessage != null)
            {
                processedMessages++;
                logger.LogInformation("SMS Webhook: Stored message ID {MessageId} from {PhoneNumber}", messageId, phoneNumber);
                await logService.LogAsync(LogLevel.Information, $"Successfully stored message ID {messageId} from {phoneNumber} in database", "WebHookSMS");
                
                // Broadcast the message via SignalR using existing MessagingHub pattern
                try
                {
                    // Normalize phone number for SignalR group targeting
                    var normalizedPhone = Surefire.Domain.Shared.Helpers.StringHelper.NormalizePhoneNumber(phoneNumber);
                    
                    // Broadcast to all clients for conversation list updates
                    await hubContext.Clients.All.SendAsync("ReceiveSmsMessage", smsMessage);
                    
                    // Also broadcast to specific SMS chat group for this phone number
                    await hubContext.Clients.Group($"SmsChat_{normalizedPhone}").SendAsync("ReceiveSmsMessage", smsMessage);
                    
                    logger.LogInformation("SMS Webhook: Broadcasted message ID {MessageId} via SignalR", messageId);
                    await logService.LogAsync(LogLevel.Information, $"Successfully broadcasted message ID {messageId} via SignalR to group SmsChat_{normalizedPhone}", "WebHookSMS");
                }
                catch (Exception signalREx)
                {
                    logger.LogError(signalREx, "SMS Webhook: Failed to broadcast message ID {MessageId} via SignalR", messageId);
                    await logService.LogAsync(LogLevel.Error, $"Failed to broadcast message ID {messageId} via SignalR: {signalREx.Message}", "WebHookSMS");
                    // Don't fail the whole webhook because of SignalR issues
                }
            }
            else
            {
                logger.LogWarning("SMS Webhook: Failed to store message ID {MessageId}", messageId);
                await logService.LogAsync(LogLevel.Warning, $"Failed to store message ID {messageId} in database", "WebHookSMS");
            }
        }
        else
        {
            await logService.LogAsync(LogLevel.Information, $"Skipping non-SMS message type: {messageType.GetString()}", "WebHookSMS");
        }
        
        logger.LogInformation("SMS Webhook: Processed {ProcessedMessages} messages", processedMessages);
        await logService.LogAsync(LogLevel.Information, $"Webhook processing completed - {processedMessages} messages processed", "WebHookSMS");
        
        // Return success response
        return Results.Ok(new { processed = processedMessages, status = "success" });
    }
    catch (System.Text.Json.JsonException ex)
    {
        logger.LogError(ex, "SMS Webhook: Invalid JSON in request body");
        await logService.LogAsync(LogLevel.Error, $"Invalid JSON in request body: {ex.Message}", "WebHookSMS");
        return Results.BadRequest("Invalid JSON format");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "SMS Webhook: Unexpected error processing webhook");
        await logService.LogAsync(LogLevel.Error, $"Unexpected error processing webhook: {ex.Message}", "WebHookSMS");
        return Results.Problem("Internal server error", statusCode: 500);
    }
}).AllowAnonymous(); 
// PRODUCTION NOTE: Configure firewall IP restrictions and/or add RingCentral signature validation

// Test endpoint for SMS webhook (for development/testing)
app.MapPost("/api/webhook/sms/test", async (SmsMessageService smsMessageService, IHubContext<MessagingHub> hubContext, ILogger<Program> logger) => {
    try
    {
        // Create a test SMS message
        var testMessage = new SmsMessage
        {
            Id = $"test_{Guid.NewGuid()}",
            PhoneNumber = "+17146187735",
            Text = "Test webhook message from API endpoint",
            Timestamp = DateTime.UtcNow,
            IsInbound = true
        };
        
        // Store in database
        var storedMessage = await smsMessageService.StoreSmsMessageAsync(testMessage);
        if (storedMessage != null)
        {
            logger.LogInformation("Test SMS Webhook: Stored test message");
            
            // Broadcast via SignalR
            var normalizedPhone = Surefire.Domain.Shared.Helpers.StringHelper.NormalizePhoneNumber(testMessage.PhoneNumber);
            await hubContext.Clients.All.SendAsync("ReceiveSmsMessage", testMessage);
            await hubContext.Clients.Group($"SmsChat_{normalizedPhone}").SendAsync("ReceiveSmsMessage", testMessage);
            
            logger.LogInformation("Test SMS Webhook: Broadcasted test message via SignalR");
            
            return Results.Ok(new { success = true, message = "Test message created and broadcasted", messageId = testMessage.Id });
        }
        else
        {
            return Results.Problem("Failed to store test message");
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Test SMS Webhook: Error creating test message");
        return Results.Problem($"Error: {ex.Message}");
    }
}).AllowAnonymous();

// Webhook health check endpoint
app.MapGet("/api/webhook/health", async (ILoggingService logService) => {
    await logService.LogAsync(LogLevel.Information, "Webhook health check endpoint called - logging is working!", "WebHookHealth");
    return Results.Ok(new { 
        status = "healthy", 
        timestamp = DateTime.UtcNow, 
        message = "SMS webhook endpoint is ready",
        version = "database-first-v1.0"
    });
}).AllowAnonymous();

// Endpoint to test vector search
app.MapPost("/api/embeddings/search", async (IEmbeddingService embeddingService, IVectorStore vectorStore, SearchRequest request) =>
{
    try
    {
        var searchVector = await embeddingService.GenerateEmbeddingAsync(request.Query);
        var results = await vectorStore.SearchAsync(searchVector, request.TopK ?? 5);

        return Results.Ok(new
        {
            Query = request.Query,
            ResultCount = results.Count,
            Results = results.Select(r => new
            {
                Id = r.Id,
                Score = r.Score,
                Entity = r.Metadata.TryGetValue("Entity", out var entity) ? entity?.ToString() ?? "Unknown" : "Unknown",
                EntityId = GetMetadataInt(r.Metadata, "EntityId"),
                Name = GetMetadataString(r.Metadata, "Name") ??
                       GetMetadataString(r.Metadata, "CarrierName") ??
                       $"{GetMetadataString(r.Metadata, "FirstName")} {GetMetadataString(r.Metadata, "LastName")}".Trim() ??
                       GetMetadataString(r.Metadata, "LineName") ?? ""
            })
        });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Search failed: {ex.Message}");
    }
}).AllowAnonymous();

// Trigger embedding upsert
app.MapPost("/api/embeddings/upsert", async (EmbeddingLoaderService loader, ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("Starting embedding upsert process...");
        await loader.UpsertAllAsync();
        logger.LogInformation("Embedding upsert completed successfully.");
        return Results.Ok("Embeddings upserted.");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error during embedding upsert: {ErrorMessage}", ex.Message);
        return Results.Problem($"Error during embedding upsert: {ex.Message}", statusCode: 500);
    }
}).AllowAnonymous();

// Initialize task agents
using (var scope = app.Services.CreateScope())
{
    var registry = scope.ServiceProvider.GetRequiredService<ITaskAgentRegistry>();
    if (registry is TaskAgentRegistry concreteRegistry)
    {
        concreteRegistry.InitializeAgents(scope.ServiceProvider);
    }
}
#endregion

app.Run();

static string? GetMetadataString(IDictionary<string, object> metadata, string key)
{
    // Helper method for metadata string extraction
    return metadata.TryGetValue(key, out var value) ? value?.ToString() : null;
}
static int GetMetadataInt(IDictionary<string, object> metadata, string key)
{
    // Helper method for metadata integer extraction
    if (!metadata.TryGetValue(key, out var value))
        return 0;
        
    return value switch
    {
        int i => i,
        long l => (int)l,
        string s when int.TryParse(s, out var parsed) => parsed,
        System.Text.Json.JsonElement je when je.ValueKind == System.Text.Json.JsonValueKind.Number => je.GetInt32(),
        _ => 0
    };
}
public record SearchRequest(string Query, int? TopK); // Record types for API endpoints