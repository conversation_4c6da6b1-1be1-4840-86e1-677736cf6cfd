﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<RootNamespace>Surefire</RootNamespace>
		<UserSecretsId>aspnet-Mantis-************************************</UserSecretsId>
		<ApplicationIcon>wwwroot\favicon.ico</ApplicationIcon>
		<PublishTrimmed>False</PublishTrimmed>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="bin\**" />
	  <Compile Remove="Components\**" />
	  <Compile Remove="Data\MigrationsLocal\**" />
	  <Compile Remove="DocuSignFixTool\**" />
	  <Compile Remove="Domain\Agents\Definitions\**" />
	  <Compile Remove="Domain\Agents\Extensions\**" />
	  <Compile Remove="Domain\Agents\Handlers\**" />
	  <Compile Remove="Domain\OpenAI\**" />
	  <Compile Remove="Domain\Users\**" />
	  <Compile Remove="KeyGenerator\**" />
	  <Compile Remove="Pages\**" />
	  <Compile Remove="Plugins\AppliedEpic\**" />
	  <Compile Remove="Plugins\ePayPolicy\**" />
	  <Compile Remove="Plugins\RingCentral\**" />
	  <Compile Remove="UpdateDocuSignConfig\**" />
	  <Compile Remove="wwwroot\psd\**" />
	  <Compile Remove="wwwroot\uploads\clients\101\**" />
	  <Compile Remove="wwwroot\uploads\clients\10\**" />
	  <Compile Remove="wwwroot\uploads\clients\121\**" />
	  <Compile Remove="wwwroot\uploads\clients\123\**" />
	  <Compile Remove="wwwroot\uploads\clients\137\**" />
	  <Compile Remove="wwwroot\uploads\clients\13\**" />
	  <Compile Remove="wwwroot\uploads\clients\143\**" />
	  <Compile Remove="wwwroot\uploads\clients\145\**" />
	  <Compile Remove="wwwroot\uploads\clients\206\**" />
	  <Compile Remove="wwwroot\uploads\clients\214\**" />
	  <Compile Remove="wwwroot\uploads\clients\63\**" />
	  <Compile Remove="wwwroot\uploads\clients\64\**" />
	  <Compile Remove="wwwroot\uploads\clients\65\**" />
	  <Compile Remove="wwwroot\uploads\clients\78\**" />
	  <Compile Remove="wwwroot\uploads\clients\91\**" />
	  <Compile Remove="wwwroot\uploads\policies\1823\**" />
	  <Compile Remove="wwwroot\uploads\policies\1843\**" />
	  <Compile Remove="wwwroot\vbscript\**" />
	  <Content Remove="bin\**" />
	  <Content Remove="Components\**" />
	  <Content Remove="Data\MigrationsLocal\**" />
	  <Content Remove="DocuSignFixTool\**" />
	  <Content Remove="Domain\Agents\Definitions\**" />
	  <Content Remove="Domain\Agents\Extensions\**" />
	  <Content Remove="Domain\Agents\Handlers\**" />
	  <Content Remove="Domain\OpenAI\**" />
	  <Content Remove="Domain\Users\**" />
	  <Content Remove="KeyGenerator\**" />
	  <Content Remove="Pages\**" />
	  <Content Remove="Plugins\AppliedEpic\**" />
	  <Content Remove="Plugins\ePayPolicy\**" />
	  <Content Remove="Plugins\RingCentral\**" />
	  <Content Remove="UpdateDocuSignConfig\**" />
	  <Content Remove="wwwroot\psd\**" />
	  <Content Remove="wwwroot\uploads\clients\101\**" />
	  <Content Remove="wwwroot\uploads\clients\10\**" />
	  <Content Remove="wwwroot\uploads\clients\121\**" />
	  <Content Remove="wwwroot\uploads\clients\123\**" />
	  <Content Remove="wwwroot\uploads\clients\137\**" />
	  <Content Remove="wwwroot\uploads\clients\13\**" />
	  <Content Remove="wwwroot\uploads\clients\143\**" />
	  <Content Remove="wwwroot\uploads\clients\145\**" />
	  <Content Remove="wwwroot\uploads\clients\206\**" />
	  <Content Remove="wwwroot\uploads\clients\214\**" />
	  <Content Remove="wwwroot\uploads\clients\63\**" />
	  <Content Remove="wwwroot\uploads\clients\64\**" />
	  <Content Remove="wwwroot\uploads\clients\65\**" />
	  <Content Remove="wwwroot\uploads\clients\78\**" />
	  <Content Remove="wwwroot\uploads\clients\91\**" />
	  <Content Remove="wwwroot\uploads\policies\1823\**" />
	  <Content Remove="wwwroot\uploads\policies\1843\**" />
	  <Content Remove="wwwroot\vbscript\**" />
	  <EmbeddedResource Remove="bin\**" />
	  <EmbeddedResource Remove="Components\**" />
	  <EmbeddedResource Remove="Data\MigrationsLocal\**" />
	  <EmbeddedResource Remove="DocuSignFixTool\**" />
	  <EmbeddedResource Remove="Domain\Agents\Definitions\**" />
	  <EmbeddedResource Remove="Domain\Agents\Extensions\**" />
	  <EmbeddedResource Remove="Domain\Agents\Handlers\**" />
	  <EmbeddedResource Remove="Domain\OpenAI\**" />
	  <EmbeddedResource Remove="Domain\Users\**" />
	  <EmbeddedResource Remove="KeyGenerator\**" />
	  <EmbeddedResource Remove="Pages\**" />
	  <EmbeddedResource Remove="Plugins\AppliedEpic\**" />
	  <EmbeddedResource Remove="Plugins\ePayPolicy\**" />
	  <EmbeddedResource Remove="Plugins\RingCentral\**" />
	  <EmbeddedResource Remove="UpdateDocuSignConfig\**" />
	  <EmbeddedResource Remove="wwwroot\psd\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\101\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\10\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\121\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\123\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\137\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\13\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\143\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\145\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\206\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\214\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\63\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\64\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\65\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\78\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\clients\91\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\policies\1823\**" />
	  <EmbeddedResource Remove="wwwroot\uploads\policies\1843\**" />
	  <EmbeddedResource Remove="wwwroot\vbscript\**" />
	  <None Remove="bin\**" />
	  <None Remove="Components\**" />
	  <None Remove="Data\MigrationsLocal\**" />
	  <None Remove="DocuSignFixTool\**" />
	  <None Remove="Domain\Agents\Definitions\**" />
	  <None Remove="Domain\Agents\Extensions\**" />
	  <None Remove="Domain\Agents\Handlers\**" />
	  <None Remove="Domain\OpenAI\**" />
	  <None Remove="Domain\Users\**" />
	  <None Remove="KeyGenerator\**" />
	  <None Remove="Pages\**" />
	  <None Remove="Plugins\AppliedEpic\**" />
	  <None Remove="Plugins\ePayPolicy\**" />
	  <None Remove="Plugins\RingCentral\**" />
	  <None Remove="UpdateDocuSignConfig\**" />
	  <None Remove="wwwroot\psd\**" />
	  <None Remove="wwwroot\uploads\clients\101\**" />
	  <None Remove="wwwroot\uploads\clients\10\**" />
	  <None Remove="wwwroot\uploads\clients\121\**" />
	  <None Remove="wwwroot\uploads\clients\123\**" />
	  <None Remove="wwwroot\uploads\clients\137\**" />
	  <None Remove="wwwroot\uploads\clients\13\**" />
	  <None Remove="wwwroot\uploads\clients\143\**" />
	  <None Remove="wwwroot\uploads\clients\145\**" />
	  <None Remove="wwwroot\uploads\clients\206\**" />
	  <None Remove="wwwroot\uploads\clients\214\**" />
	  <None Remove="wwwroot\uploads\clients\63\**" />
	  <None Remove="wwwroot\uploads\clients\64\**" />
	  <None Remove="wwwroot\uploads\clients\65\**" />
	  <None Remove="wwwroot\uploads\clients\78\**" />
	  <None Remove="wwwroot\uploads\clients\91\**" />
	  <None Remove="wwwroot\uploads\policies\1823\**" />
	  <None Remove="wwwroot\uploads\policies\1843\**" />
	  <None Remove="wwwroot\vbscript\**" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Remove="Domain\Account\Identity\IdentityComponentsEndpointRouteBuilderExtensions.cs" />
	  <Compile Remove="Domain\Agents\Interfaces\IAgentActionHandler.cs" />
	  <Compile Remove="Domain\Agents\Interfaces\IAgentRegistryService.cs" />
	  <Compile Remove="Domain\Agents\Interfaces\IDatabaseQueryHandler.cs" />
	  <Compile Remove="Domain\Agents\Interfaces\IDatabaseSchemaService.cs" />
	  <Compile Remove="Domain\Agents\Interfaces\IGeneralAIHandler.cs" />
	  <Compile Remove="Domain\Agents\Interfaces\IIntentDetectionService.cs" />
	  <Compile Remove="Domain\Agents\Models\AgentConfiguration.cs" />
	  <Compile Remove="Domain\Agents\Models\DatabaseQueryModels.cs" />
	  <Compile Remove="Domain\Agents\Services\AgentRegistryService.cs" />
	  <Compile Remove="Domain\Agents\Services\CommandParsingService.cs" />
	  <Compile Remove="Domain\Agents\Services\DatabaseSchemaService.cs" />
	  <Compile Remove="Domain\Agents\Services\EnhancedSemanticMemoryService.cs" />
	  <Compile Remove="Domain\Agents\Services\IEnhancedSemanticMemoryService.cs" />
	  <Compile Remove="Domain\Agents\Services\IntentDetectionService.cs" />
	  <Compile Remove="Domain\Agents\Services\IResponseStreamingService.cs" />
	  <Compile Remove="Domain\Agents\Services\IUnifiedInputHandler.cs" />
	  <Compile Remove="Domain\Agents\Services\ResponseStreamingService.cs" />
	  <Compile Remove="Domain\Agents\Services\UnifiedInputHandler.cs" />
	  <Compile Remove="Domain\Agents\Services\UnifiedInputHandlerAdapter.cs" />
	  <Compile Remove="Domain\Agents\Utilities\EmailHelper.cs" />
	  <Compile Remove="Domain\Agents\Utilities\PolicyQueryHelper.cs" />
	</ItemGroup>

	<ItemGroup>
	  <Content Remove="Domain\Agents\Pages\AgentTesting.razor" />
	  <Content Remove="Domain\Agents\Pages\UnifiedAIChat.razor" />
	  <Content Remove="Domain\Agents\Pages\UnifiedAIInput.razor" />
	  <Content Remove="Domain\Proposals\Components\ProposalCleaner.razor" />
	  <Content Remove="Domain\Renewals\Components\Create.razor" />
	  <Content Remove="wwwroot\forms\a125-2016-03-bad.pdf" />
	  <Content Remove="wwwroot\forms\a126-2016-09-good.pdf" />
	  <Content Remove="wwwroot\forms\form - Copy.pdf" />
	  <Content Remove="wwwroot\forms\form-fieldnamesoriginally.pdf" />
	  <Content Remove="wwwroot\forms\form-old3444.pdf" />
	  <Content Remove="wwwroot\forms\form-shjs.pdf" />
	  <Content Remove="wwwroot\forms\form-swapout-namesoriginally.pdf" />
	  <Content Remove="wwwroot\forms\form.pdf" />
	  <Content Remove="wwwroot\forms\form222.pdf" />
	  <Content Remove="wwwroot\forms\formnoclue.pdf" />
	  <Content Remove="wwwroot\img\banner-homepage-flat.jpg" />
	  <Content Remove="wwwroot\img\banner-homepage-flat2.jpg" />
	  <Content Remove="wwwroot\img\banner-homepage-flat3.jpg" />
	  <Content Remove="wwwroot\img\banner-homepage-flat4.jpg" />
	  <Content Remove="wwwroot\img\banner-homepage-topp.png" />
	  <Content Remove="wwwroot\prompts\proposal-extractor.txt" />
	  <Content Remove="wwwroot\schema\db_schema.min.json" />
	  <Content Remove="wwwroot\uploads\headshots\1-NathanSmith.jpg" />
	  <Content Remove="wwwroot\uploads\headshots\147-222-BorisVargas.jpg" />
	  <Content Remove="wwwroot\uploads\headshots\17-FaithTatro.jpg" />
	  <Content Remove="wwwroot\uploads\headshots\19-WayneMiller.jpeg" />
	  <Content Remove="wwwroot\uploads\headshots\19-WayneMiller.jpg" />
	  <Content Remove="wwwroot\uploads\headshots\219-JillSmith.png" />
	  <Content Remove="wwwroot\uploads\headshots\220-DillionPacinao.png" />
	  <Content Remove="wwwroot\uploads\headshots\220-NathanSmith.png" />
	  <Content Remove="wwwroot\uploads\headshots\222-BorisVargas.jpeg" />
	  <Content Remove="wwwroot\uploads\headshots\223-FredMartino.png" />
	  <Content Remove="wwwroot\uploads\headshots\224-SusanPrichard.jpg" />
	  <Content Remove="wwwroot\uploads\headshots\227-BrianStreuter.jpg" />
	  <Content Remove="wwwroot\uploads\headshots\249-JesseRuiz.jpg" />
	  <Content Remove="wwwroot\uploads\headshots\250-HenryChou.jpg" />
	  <Content Remove="wwwroot\uploads\headshots\40-SarahThomas.png" />
	  <Content Remove="wwwroot\uploads\headshots\46-JenniferRigby.jpg" />
	  <Content Remove="wwwroot\uploads\logos\17.jpg" />
	  <Content Remove="wwwroot\uploads\logos\172.png" />
	  <Content Remove="wwwroot\uploads\logos\175.png" />
	  <Content Remove="wwwroot\uploads\logos\18.png" />
	  <Content Remove="wwwroot\uploads\logos\211.jpg" />
	  <Content Remove="wwwroot\uploads\logos\40.jpg" />
	  <Content Remove="wwwroot\uploads\logos\44.png" />
	  <Content Remove="wwwroot\uploads\logos\83.png" />
	  <Content Remove="wwwroot\uploads\logos\carrier\1164.png" />
	  <Content Remove="wwwroot\uploads\logos\carrier\144.png" />
	  <Content Remove="wwwroot\uploads\logos\carrier\17.jpg" />
	  <Content Remove="wwwroot\uploads\SAMPLE-GL-AI-WITH-PNC.pdf" />
	  <Content Remove="wwwroot\uploads\SAMPLE-GL-AIPACKET.pdf" />
	  <Content Remove="wwwroot\uploads\SAMPLE-GL-WOS.pdf" />
	  <Content Remove="wwwroot\uploads\SCIF-WOS-ENDT.pdf" />
	  <Content Remove="wwwroot\uploads\temp\166.json" />
	  <Content Remove="wwwroot\uploads\temp\173.json" />
	  <Content Remove="wwwroot\uploads\temp\174.json" />
	  <Content Remove="wwwroot\uploads\temp\aquote112.pdf" />
	  <Content Remove="wwwroot\uploads\temp\aquote113.pdf" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="C:\Users\<USER>\.nuget\packages\xunit.runner.visualstudio\3.1.0\build\net8.0\xunit.abstractions.dll" />
	  <None Remove="C:\Users\<USER>\.nuget\packages\xunit.runner.visualstudio\3.1.0\build\net8.0\xunit.runner.visualstudio.testadapter.dll" />
	  <None Remove="Domain\Agents\Pages\AgentTesting.razor.css" />
	  <None Remove="Domain\Renewals\Components\Create.razor.css" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Azure.AI.FormRecognizer" Version="4.1.0" />
		<PackageReference Include="Azure.Identity" Version="1.13.2" />
		<PackageReference Include="DocumentFormat.OpenXml" Version="3.2.0" />
		<PackageReference Include="DotNetEnv" Version="3.1.1" />
		<PackageReference Include="itext7" Version="9.1.0" />
		<PackageReference Include="itext7.bouncy-castle-adapter" Version="9.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components.QuickGrid" Version="9.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.Components.QuickGrid.EntityFrameworkAdapter" Version="9.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.WebUtilities" Version="9.0.3" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="9.0.3" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.3" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.3" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.3">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.11.7" />
		<PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.DataGrid.EntityFrameworkAdapter" Version="4.11.7" />
		<PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.11.7" />
		<PackageReference Include="Microsoft.Graph" Version="5.74.0" />
		<PackageReference Include="Microsoft.Identity.Client" Version="4.70.0" />
		<PackageReference Include="Microsoft.SemanticKernel" Version="1.54.0" />
		<PackageReference Include="Microsoft.SemanticKernel.Connectors.OpenAI" Version="1.54.0" />
		<PackageReference Include="Microsoft.SemanticKernel.Connectors.Ollama" Version="1.54.0-alpha" />
		<PackageReference Include="Microsoft.SemanticKernel.Core" Version="1.54.0" />
		<PackageReference Include="Microsoft.SemanticKernel.Plugins.OpenApi" Version="1.54.0" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="OpenAI" Version="2.2.0-beta.4" />
		<PackageReference Include="RingCentral.Net" Version="6.3.1" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
		<PackageReference Include="Syncfusion.Blazor.Calendars" Version="29.1.33" />
		<PackageReference Include="Syncfusion.Blazor.DropDowns" Version="29.1.33" />
		<PackageReference Include="Syncfusion.Blazor.Grid" Version="29.1.33" />
		<PackageReference Include="Syncfusion.Blazor.InPlaceEditor" Version="29.1.33" />
		<PackageReference Include="Syncfusion.Blazor.ProgressBar" Version="29.1.33" />
		<PackageReference Include="Syncfusion.Blazor.RichTextEditor" Version="29.1.33" />
		<PackageReference Include="Syncfusion.Blazor.SfPdfViewer" Version="29.1.33" />
		<PackageReference Include="Syncfusion.Blazor.SmartComponents" Version="29.1.33" />
		<PackageReference Include="Syncfusion.Blazor.Spinner" Version="29.1.33" />
		<PackageReference Include="Syncfusion.Blazor.SplitButtons" Version="29.1.33" />
		<PackageReference Include="Syncfusion.Blazor.Themes" Version="29.1.33" />
		<PackageReference Include="Syncfusion.DocIORenderer.Net.Core" Version="29.1.33" />
		<PackageReference Include="Syncfusion.Pdf.Net.Core" Version="29.1.33" />
		<PackageReference Include="Syncfusion.PdfToImageConverter.Net" Version="29.1.33" />
		<PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
		<PackageReference Include="System.Drawing.Common" Version="9.0.3" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.1.0">
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		  <PrivateAssets>all</PrivateAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Domain\Carriers\Components\Dialogs\" />
		<Folder Include="Plugins\" />
		<Folder Include="wwwroot\fonts\" />
		<Folder Include="wwwroot\img\misc\" />
		<Folder Include="wwwroot\uploads\clients\" />
		<Folder Include="wwwroot\uploads\headshots\" />
		<Folder Include="wwwroot\uploads\logos\carrier\" />
		<Folder Include="wwwroot\uploads\policies\" />
		<Folder Include="wwwroot\uploads\temp\" />
	</ItemGroup>

	<ItemGroup>
	  <None Include="wwwroot\files\worddoc-template.docx" />
	  <None Include="wwwroot\prompts\proposal-extractor.txt" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Properties\Resources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>Resources.resx</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
		<Content Update="Domain\Profile\Components\Preferences.razor">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		</Content>
		<Content Update="Domain\Renewals\Pages\Renewals.razor">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		</Content>
		<Content Update="wwwroot\schema\proposal-strict.json">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Update="Properties\Resources.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>Resources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>
	
	<!--<Target Name="PreBuild" AfterTargets="PostBuildEvent">
		<Exec Command="xcopy /E /Y &quot;$(SolutionDir)Surefire\bin\Plugins&quot; &quot;$(SolutionDir)Surefire\Plugins&quot;&#xD;&#xA;" />
	</Target>-->

	<Target Name="PostBuild" AfterTargets="PostBuildEvent">
		<!-- Remove all "Plugins" folders in subdirectories -->
		<!--<Exec Command="for /d %%d in (&quot;$(SolutionDir)Surefire\bin\Plugins\*&quot;) do if exist %%d\Plugins rmdir /s /q &quot;%%d\Plugins&quot;" />-->

		<!-- Copy Plugins to the Surefire\Plugins folder -->
		<!--<Exec Command="xcopy /E /Y &quot;$(SolutionDir)Surefire\bin\Plugins&quot; &quot;$(SolutionDir)Surefire\Plugins&quot;" />-->

		<!-- Copy Debug output to the Build\Web folder -->
		<!--<Exec Command="xcopy /E /Y &quot;$(SolutionDir)Surefire\bin\Debug\net9.0&quot; &quot;$(SolutionDir)Build\Web&quot;" />-->
	</Target>
</Project>
