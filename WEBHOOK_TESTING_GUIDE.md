# SMS Webhook Testing Guide

## Overview

The SMS webhook system has two main components that need to be tested separately due to firewall restrictions:

1. **Main App** (`https://surefire.local`) - Internal server, behind firewall
2. **Gateway** (`https://gateway.metroinsurance.services`) - External server, accessible from internet

## Network Architecture

```
RingCentral (External) 
    ↓
Gateway (External - https://gateway.metroinsurance.services)
    ↓ [Forwards to]
Main App (Internal - https://surefire.local)
```

## Testing Strategy

### Phase 1: Internal Testing (Run on Server)

**Script**: `test_webhook_server_internal.ps1`
**Location**: Run directly on the server
**Purpose**: Test that the main app is ready to receive webhook requests

**What it tests**:
- ✅ Main app basic connectivity
- ✅ Webhook endpoint accessibility
- ✅ API key validation
- ✅ SMS payload processing
- ✅ Error handling

**Expected Results**:
```
✓ Main app is reachable (Status: 200)
✓ Main app test endpoint is reachable
✓ Webhook endpoint accepts requests with API key
✓ Realistic SMS payload test successful
✓ Wrong API key correctly rejected with 401 Unauthorized
```

### Phase 2: External Testing (Run from Outside Network)

**Script**: `test_gateway_external.ps1`
**Location**: Run from external location (not on server)
**Purpose**: Test that the Gateway is accessible from the internet

**What it tests**:
- ✅ Gateway basic connectivity
- ✅ Gateway health endpoint
- ✅ SMS webhook endpoint
- ✅ RingCentral payload handling
- ✅ Header processing

**Expected Results**:
```
✓ Gateway is reachable (Status: 200)
✓ Gateway health endpoint is reachable
✓ Gateway webhook endpoint accepts requests
✓ Gateway realistic SMS payload test successful
✓ Gateway RingCentral headers test successful
```

## Current Test Results

### Internal Testing ✅ PASSED
Your recent test showed:
- Main app is working correctly
- Webhook endpoint is accessible
- API key validation works
- SMS payload processing works

### External Testing ❌ FAILED (Due to Firewall)
The server cannot reach the external Gateway due to firewall restrictions blocking outbound internet access.

## Next Steps

### 1. Test Gateway Externally
Run `test_gateway_external.ps1` from a location outside your network (your local machine, another server, etc.):

```powershell
# Download and run from external location
.\test_gateway_external.ps1
```

### 2. Configure RingCentral Webhook
Once Gateway tests pass, configure RingCentral to send webhooks to:
```
https://gateway.metroinsurance.services/api/smswebhook
```

### 3. Monitor the Flow
When RingCentral sends a webhook:

1. **Check Gateway Logs**: Look for incoming webhook requests
2. **Check Main App Database**: Look for forwarded requests in the logs table
3. **Check SignalR**: Verify messages are broadcast to chat interface

## Database Monitoring

Check the main app database for webhook processing logs:

```sql
SELECT * FROM Logs 
WHERE Category IN ('InternalSmsWebhook', 'SmsWebhook', 'GatewayConnectivityTest')
ORDER BY Timestamp DESC
```

## Troubleshooting

### If Gateway Tests Fail:
- Check DNS resolution: `nslookup gateway.metroinsurance.services`
- Verify SSL certificate
- Check Gateway server status
- Verify firewall rules allow external access

### If Main App Tests Fail:
- Check IIS/Kestrel status
- Verify SSL certificate for surefire.local
- Check controller registration in Program.cs
- Verify API key configuration

### If End-to-End Flow Fails:
1. Verify Gateway can reach main app internally
2. Check API key matches between Gateway and main app
3. Check webhook payload structure
4. Monitor database logs for error messages

## Configuration Summary

### Gateway Configuration
```json
{
  "MainAppSettings": {
    "BaseUrl": "https://surefire.local",
    "WebhookForwardUrl": "https://surefire.local/api/internal/smswebhook",
    "ApiKey": "MetroGateway_07122025"
  }
}
```

### Main App Configuration
```json
{
  "GatewaySettings": {
    "ApiKey": "MetroGateway_07122025",
    "AllowedIpAddresses": ["127.0.0.1", "::1", "localhost"]
  }
}
```

## Test Scripts

1. **`test_webhook_server_internal.ps1`** - Internal server testing
2. **`test_gateway_external.ps1`** - External Gateway testing
3. **`test_webhook.ps1`** - Development testing (localhost)

## Success Criteria

The system is working correctly when:
- ✅ Internal tests pass (main app ready)
- ✅ External tests pass (Gateway accessible)
- ✅ RingCentral webhook configured
- ✅ SMS messages appear in database logs
- ✅ Messages broadcast via SignalR to chat interface

## Important Notes

1. **Firewall Limitation**: The server cannot test external Gateway due to outbound internet restrictions
2. **Two-Phase Testing**: Internal and external components must be tested separately
3. **API Key Security**: Ensure API keys match between Gateway and main app
4. **Database Logging**: All webhook processing is logged to database for monitoring
5. **Controller Registration**: Main app requires explicit controller registration in Program.cs 