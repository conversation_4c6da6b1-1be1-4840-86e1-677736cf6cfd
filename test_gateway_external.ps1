# External Gateway Test Script - Run this from OUTSIDE the firewall
# This script tests the Gateway that should be accessible from the internet

Write-Host "=== Testing Gateway from External Location ===" -ForegroundColor Green
Write-Host "Gateway URL: https://gateway.metroinsurance.services" -ForegroundColor Cyan
Write-Host "Note: This script should be run from outside the firewall/network" -ForegroundColor Yellow
Write-Host ""

# Test 1: Gateway Base Connectivity
Write-Host "1. Testing Gateway base connectivity..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://gateway.metroinsurance.services" -Method GET -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Gateway is reachable (Status: $($response.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host "✗ Gateway returned status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Gateway base connectivity failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 2: Gateway Health Endpoint
Write-Host "2. Testing Gateway health endpoint..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://gateway.metroinsurance.services/health" -Method GET -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Gateway health endpoint is reachable" -ForegroundColor Green
        Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Gateway health endpoint returned status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Gateway health endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Gateway SMS Webhook Endpoint (Empty Payload)
Write-Host "3. Testing Gateway SMS webhook endpoint with empty payload..." -ForegroundColor Yellow
try {
    $headers = @{
        "Content-Type" = "application/json"
        "User-Agent" = "RingCentral-Webhooks/1.0"
    }
    
    # Empty payload test
    $body = @{
        uuid = "test-uuid-$(Get-Random)"
        timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
        subscriptionId = "test-subscription"
        ownerId = "test-owner"
        body = @{}
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-WebRequest -Uri "https://gateway.metroinsurance.services/api/smswebhook" -Method POST -Headers $headers -Body $body -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Gateway webhook endpoint accepts requests" -ForegroundColor Green
        Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Gateway webhook endpoint returned status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Gateway webhook endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host ""

# Test 4: Gateway with Realistic SMS Payload
Write-Host "4. Testing Gateway with realistic SMS payload..." -ForegroundColor Yellow
try {
    $headers = @{
        "Content-Type" = "application/json"
        "User-Agent" = "RingCentral-Webhooks/1.0"
    }
    
    # Realistic SMS payload that RingCentral would send
    $smsPayload = @{
        uuid = "test-uuid-$(Get-Random)"
        timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
        subscriptionId = "test-subscription"
        ownerId = "test-owner"
        body = @{
            id = 12345
            to = @(
                @{
                    phoneNumber = "+***********"
                    name = "Test Recipient"
                }
            )
            from = @{
                phoneNumber = "+***********"
                name = "Test Sender"
            }
            type = "SMS"
            creationTime = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
            readStatus = "Unread"
            priority = "Normal"
            attachments = @(
                @{
                    id = 1
                    type = "Text"
                    contentType = "text/plain"
                }
            )
            direction = "Inbound"
            availability = "Alive"
            subject = "Test SMS message from external Gateway test"
            messageStatus = "Received"
            conversationId = "test-conversation-123"
            conversation = @{
                id = "test-conversation-123"
            }
            owner = @{
                accountId = "test-account"
                extensionId = "test-extension"
            }
        }
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-WebRequest -Uri "https://gateway.metroinsurance.services/api/smswebhook" -Method POST -Headers $headers -Body $smsPayload -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Gateway realistic SMS payload test successful" -ForegroundColor Green
        Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Gateway realistic SMS payload test failed with status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Gateway realistic SMS payload test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 5: Gateway with RingCentral-style Headers
Write-Host "5. Testing Gateway with RingCentral-style headers..." -ForegroundColor Yellow
try {
    $headers = @{
        "Content-Type" = "application/json"
        "User-Agent" = "RingCentral-Webhooks/1.0"
        "X-RingCentral-Event" = "/restapi/v1.0/account/*********/extension/*********/message-store/instant?type=SMS"
        "X-RingCentral-Signature" = "test-signature"
    }
    
    # SMS payload with RingCentral structure
    $smsPayload = @{
        uuid = "rc-test-uuid-$(Get-Random)"
        timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
        subscriptionId = "rc-subscription-123"
        ownerId = "rc-owner-456"
        body = @{
            id = 98765
            to = @(
                @{
                    phoneNumber = "+***********"
                    name = "Test Recipient"
                }
            )
            from = @{
                phoneNumber = "+***********"
                name = "Test Sender"
            }
            type = "SMS"
            creationTime = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
            readStatus = "Unread"
            priority = "Normal"
            attachments = @(
                @{
                    id = 1
                    type = "Text"
                    contentType = "text/plain"
                }
            )
            direction = "Inbound"
            availability = "Alive"
            subject = "Test SMS with RingCentral headers"
            messageStatus = "Received"
            conversationId = "rc-conversation-789"
            conversation = @{
                id = "rc-conversation-789"
            }
            owner = @{
                accountId = "rc-account-001"
                extensionId = "rc-extension-002"
            }
        }
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-WebRequest -Uri "https://gateway.metroinsurance.services/api/smswebhook" -Method POST -Headers $headers -Body $smsPayload -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Gateway RingCentral headers test successful" -ForegroundColor Green
        Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Gateway RingCentral headers test failed with status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Gateway RingCentral headers test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

Write-Host "=== External Gateway Test Summary ===" -ForegroundColor Green
Write-Host "If all tests passed:" -ForegroundColor Yellow
Write-Host "✓ Gateway is accessible from external locations" -ForegroundColor Green
Write-Host "✓ Gateway can receive webhook requests" -ForegroundColor Green
Write-Host "✓ Gateway should forward requests to the main app" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Configure RingCentral webhook URL to: https://gateway.metroinsurance.services/api/smswebhook" -ForegroundColor Cyan
Write-Host "2. Monitor Gateway logs for incoming webhooks" -ForegroundColor Cyan
Write-Host "3. Monitor main app database logs for forwarded requests" -ForegroundColor Cyan
Write-Host ""
Write-Host "If tests failed:" -ForegroundColor Yellow
Write-Host "- Check Gateway server status and configuration" -ForegroundColor Red
Write-Host "- Verify DNS resolution for gateway.metroinsurance.services" -ForegroundColor Red
Write-Host "- Check SSL certificate validity" -ForegroundColor Red
Write-Host "- Verify firewall rules allow external access to Gateway" -ForegroundColor Red
Write-Host ""
Write-Host "Test completed!" -ForegroundColor Green 