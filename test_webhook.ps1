# Test script to verify Gateway to Main App connectivity
$ErrorActionPreference = "Stop"

Write-Host "Testing Gateway to Main App connectivity..." -ForegroundColor Green

# Test 1: Check if main app test endpoint is reachable
Write-Host "`n1. Testing main app test endpoint..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://surefire.local/api/internal/smswebhook/test" -Method GET -UseBasicParsing
    Write-Host "✓ Main app test endpoint is reachable" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Main app test endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Test webhook endpoint with API key
Write-Host "`n2. Testing webhook endpoint with API key..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-API-Key" = "MetroGateway_07122025"
        "X-Request-ID" = "test-$(Get-Random)"
        "Content-Type" = "application/json"
    }
    
    $testPayload = @{
        "uuid" = "test-uuid"
        "event" = "message.store.instant.message" 
        "body" = @{
            "changes" = $null
        }
    } | ConvertTo-Json -Depth 5
    
    $response = Invoke-WebRequest -Uri "https://surefire.local/api/internal/smswebhook" -Method POST -Headers $headers -Body $testPayload -UseBasicParsing
    Write-Host "✓ Webhook endpoint accepts requests with API key" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Webhook endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test Gateway forwarding (if Gateway is running)
Write-Host "`n3. Testing Gateway forwarding..." -ForegroundColor Yellow
try {
    $testPayload = @{
        "uuid" = "test-gateway-uuid"
        "event" = "message.store.instant.message"
        "body" = @{
            "changes" = $null
        }
    } | ConvertTo-Json -Depth 5
    
    $response = Invoke-WebRequest -Uri "https://gateway.metroinsurance.services/api/smswebhook" -Method POST -Body $testPayload -ContentType "application/json" -UseBasicParsing
    Write-Host "✓ Gateway forwarding works" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Gateway forwarding failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure the Gateway is running on https://gateway.metroinsurance.services/api/smswebhook" -ForegroundColor Yellow
}

# Test 4: Test with a realistic SMS payload (like what RingCentral sends)
Write-Host "`n4. Testing with realistic SMS payload..." -ForegroundColor Yellow
try {
    $realisticPayload = @{
        "uuid" = "test-realistic-uuid"
        "event" = "message.store.instant.message"
        "timestamp" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        "subscriptionId" = "test-subscription-id"
        "ownerId" = "220528104"
        "body" = @{
            "id" = "test-message-id-$(Get-Random)"
            "to" = @(
                @{
                    "phoneNumber" = "+17145738460"
                    "name" = "Test Recipient"
                    "target" = $true
                }
            )
            "from" = @{
                "phoneNumber" = "+17146187735"
                "phoneNumberInfo" = @{
                    "countryCode" = "1"
                    "nationalDestinationCode" = "714"
                    "subscriberNumber" = "6187735"
                }
            }
            "type" = "SMS"
            "creationTime" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            "lastModifiedTime" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            "readStatus" = "Unread"
            "priority" = "Normal"
            "attachments" = @(
                @{
                    "id" = "test-attachment-id"
                    "type" = "Text"
                    "contentType" = "text/plain"
                }
            )
            "direction" = "Inbound"
            "availability" = "Alive"
            "subject" = "Test message from PowerShell script"
            "messageStatus" = "Received"
            "conversation" = @{
                "id" = "test-conversation-id"
            }
            "eventType" = "Create"
            "owner" = @{
                "extensionId" = "220528104"
                "extensionType" = "User"
                "name" = "Test User"
            }
        }
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-WebRequest -Uri "https://gateway.metroinsurance.services/api/smswebhook" -Method POST -Body $realisticPayload -ContentType "application/json" -UseBasicParsing
    Write-Host "✓ Gateway accepts realistic SMS payload" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Realistic SMS payload test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest completed!" -ForegroundColor Green 