# Test script to verify Gateway to Main App connectivity on server
$ErrorActionPreference = "Continue"  # Continue on errors to see all results

Write-Host "=== Testing Gateway to Main App connectivity on server ===" -ForegroundColor Green
Write-Host "Server URLs:" -ForegroundColor Yellow
Write-Host "  Main App: https://surefire.local" -ForegroundColor Cyan
Write-Host "  Gateway:  https://gateway.metroinsurance.services" -ForegroundColor Cyan
Write-Host ""

# Test 1: Check if main app is reachable at all
Write-Host "1. Testing main app base connectivity..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://surefire.local" -Method GET -UseBasicParsing -TimeoutSec 10
    Write-Host "✓ Main app is reachable (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ Main app base connectivity failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Check if main app test endpoint is reachable
Write-Host "`n2. Testing main app test endpoint..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://surefire.local/api/internal/smswebhook/test" -Method GET -UseBasicParsing -TimeoutSec 10
    Write-Host "✓ Main app test endpoint is reachable" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Main app test endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# Test 3: Test webhook endpoint with API key
Write-Host "`n3. Testing webhook endpoint with API key..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-API-Key" = "MetroGateway_07122025"
        "X-Request-ID" = "test-$(Get-Random)"
        "Content-Type" = "application/json"
    }
    
    $testPayload = @{
        "uuid" = "test-uuid-$(Get-Random)"
        "event" = "message.store.instant.message" 
        "body" = @{
            "changes" = $null
        }
    } | ConvertTo-Json -Depth 5
    
    $response = Invoke-WebRequest -Uri "https://surefire.local/api/internal/smswebhook" -Method POST -Headers $headers -Body $testPayload -UseBasicParsing -TimeoutSec 10
    Write-Host "✓ Webhook endpoint accepts requests with API key" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Webhook endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# Test 4: Check if Gateway is reachable
Write-Host "`n4. Testing Gateway base connectivity..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://gateway.metroinsurance.services" -Method GET -UseBasicParsing -TimeoutSec 10
    Write-Host "✓ Gateway is reachable (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ Gateway base connectivity failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Test Gateway health endpoint
Write-Host "`n5. Testing Gateway health endpoint..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://gateway.metroinsurance.services/health" -Method GET -UseBasicParsing -TimeoutSec 10
    Write-Host "✓ Gateway health endpoint is reachable" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Gateway health endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Test Gateway webhook endpoint
Write-Host "`n6. Testing Gateway webhook endpoint..." -ForegroundColor Yellow
try {
    $testPayload = @{
        "uuid" = "test-gateway-uuid-$(Get-Random)"
        "event" = "message.store.instant.message"
        "body" = @{
            "changes" = $null
        }
    } | ConvertTo-Json -Depth 5
    
    $response = Invoke-WebRequest -Uri "https://gateway.metroinsurance.services/api/smswebhook" -Method POST -Body $testPayload -ContentType "application/json" -UseBasicParsing -TimeoutSec 10
    Write-Host "✓ Gateway webhook endpoint is reachable" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Gateway webhook endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# Test 7: Test with realistic SMS payload structure
Write-Host "`n7. Testing Gateway with realistic SMS payload..." -ForegroundColor Yellow
try {
    $realisticPayload = @{
        "uuid" = "test-realistic-$(Get-Random)"
        "event" = "message.store.instant.message"
        "timestamp" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        "subscriptionId" = "test-subscription-id"
        "ownerId" = "220528104"
        "body" = @{
            "id" = "test-message-$(Get-Random)"
            "to" = @(
                @{
                    "phoneNumber" = "+17145738460"
                    "name" = "Test Recipient"
                    "target" = $true
                }
            )
            "from" = @{
                "phoneNumber" = "+17146187735"
                "phoneNumberInfo" = @{
                    "countryCode" = "1"
                    "nationalDestinationCode" = "714"
                    "subscriberNumber" = "6187735"
                }
            }
            "type" = "SMS"
            "creationTime" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            "direction" = "Inbound"
            "subject" = "Test message from PowerShell script - $(Get-Date)"
            "attachments" = @(
                @{
                    "id" = "test-attachment-$(Get-Random)"
                    "type" = "Text"
                    "contentType" = "text/plain"
                }
            )
        }
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-WebRequest -Uri "https://gateway.metroinsurance.services/api/smswebhook" -Method POST -Body $realisticPayload -ContentType "application/json" -UseBasicParsing -TimeoutSec 15
    Write-Host "✓ Gateway processes realistic SMS payload" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Realistic SMS payload test failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n=== Test Summary ===" -ForegroundColor Green
Write-Host "Check the database logs in the main app for detailed webhook processing information." -ForegroundColor Yellow
Write-Host "Look for log entries with category 'InternalSmsWebhook' and 'GatewayConnectivityTest'" -ForegroundColor Yellow
Write-Host "Test completed!" -ForegroundColor Green 