# Internal Server Test Script - Only tests what's reachable from inside the network
# This script should be run ON the server to test internal connectivity

Write-Host "=== Testing Internal Server Components ===" -ForegroundColor Green
Write-Host "Server URLs:" -ForegroundColor Yellow
Write-Host "  Main App: https://surefire.local" -ForegroundColor Cyan
Write-Host "  Note: Gateway tests skipped due to firewall restrictions" -ForegroundColor Yellow
Write-Host ""

# Test 1: Main App Base Connectivity
Write-Host "1. Testing main app base connectivity..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://surefire.local" -Method GET -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Main app is reachable (Status: $($response.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host "✗ Main app returned status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Main app base connectivity failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 2: Main App Test Endpoint
Write-Host "2. Testing main app test endpoint..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-API-Key" = "MetroGateway_07122025"
        "Content-Type" = "application/json"
    }
    
    $response = Invoke-WebRequest -Uri "https://surefire.local/api/internal/smswebhook/test" -Method GET -Headers $headers -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Main app test endpoint is reachable" -ForegroundColor Green
        Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Test endpoint returned status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Main app test endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Webhook Endpoint with API Key
Write-Host "3. Testing webhook endpoint with API key..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-API-Key" = "MetroGateway_07122025"
        "Content-Type" = "application/json"
    }
    
    # Empty payload test
    $body = @{
        uuid = "test-uuid-$(Get-Random)"
        timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
        subscriptionId = "test-subscription"
        ownerId = "test-owner"
        body = @{}
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-WebRequest -Uri "https://surefire.local/api/internal/smswebhook" -Method POST -Headers $headers -Body $body -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Webhook endpoint accepts requests with API key" -ForegroundColor Green
        Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Webhook endpoint returned status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Webhook endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
}

Write-Host ""

# Test 4: Realistic SMS Payload Test
Write-Host "4. Testing with realistic SMS payload..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-API-Key" = "MetroGateway_07122025"
        "Content-Type" = "application/json"
    }
    
    # Realistic SMS payload based on RingCentral structure
    $smsPayload = @{
        uuid = "test-uuid-$(Get-Random)"
        timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
        subscriptionId = "test-subscription"
        ownerId = "test-owner"
        body = @{
            id = 12345
            to = @(
                @{
                    phoneNumber = "+15551234567"
                    name = "Test Recipient"
                }
            )
            from = @{
                phoneNumber = "+15557654321"
                name = "Test Sender"
            }
            type = "SMS"
            creationTime = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
            readStatus = "Unread"
            priority = "Normal"
            attachments = @(
                @{
                    id = 1
                    type = "Text"
                    contentType = "text/plain"
                }
            )
            direction = "Inbound"
            availability = "Alive"
            subject = "Test SMS message from internal server"
            messageStatus = "Received"
            conversationId = "test-conversation-123"
            conversation = @{
                id = "test-conversation-123"
            }
            owner = @{
                accountId = "test-account"
                extensionId = "test-extension"
            }
        }
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-WebRequest -Uri "https://surefire.local/api/internal/smswebhook" -Method POST -Headers $headers -Body $smsPayload -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Realistic SMS payload test successful" -ForegroundColor Green
        Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Realistic SMS payload test failed with status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Realistic SMS payload test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 5: Wrong API Key Test
Write-Host "5. Testing with wrong API key (should fail)..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-API-Key" = "wrong-api-key"
        "Content-Type" = "application/json"
    }
    
    $body = @{
        uuid = "test-uuid-$(Get-Random)"
        timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"
        subscriptionId = "test-subscription"
        ownerId = "test-owner"
        body = @{}
    } | ConvertTo-Json -Depth 10
    
    $response = Invoke-WebRequest -Uri "https://surefire.local/api/internal/smswebhook" -Method POST -Headers $headers -Body $body -UseBasicParsing -TimeoutSec 30
    Write-Host "✗ Wrong API key test failed - should have been rejected but got status: $($response.StatusCode)" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✓ Wrong API key correctly rejected with 401 Unauthorized" -ForegroundColor Green
    } else {
        Write-Host "✗ Wrong API key test failed with unexpected error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Internal Test Summary ===" -ForegroundColor Green
Write-Host "✓ Main app internal components are working correctly" -ForegroundColor Green
Write-Host "✓ Webhook endpoint is ready to receive requests from Gateway" -ForegroundColor Green
Write-Host "✓ API key validation is working" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Test the Gateway externally (from outside the firewall)" -ForegroundColor Cyan
Write-Host "2. Configure RingCentral to send webhooks to Gateway" -ForegroundColor Cyan
Write-Host "3. Monitor database logs for webhook processing" -ForegroundColor Cyan
Write-Host ""
Write-Host "Check the database logs in the main app for detailed webhook processing information." -ForegroundColor Yellow
Write-Host "Look for log entries with category 'InternalSmsWebhook' and 'GatewayConnectivityTest'" -ForegroundColor Yellow
Write-Host "Test completed!" -ForegroundColor Green 